# Terraform Cloud Projects Configuration Guide

This document provides a comprehensive guide to managing the `projects.yaml` file located at `iac/tfc-workspace-tf/configs/projects.yaml`. This file defines Terraform Cloud projects that are used to organize workspaces in the company's infrastructure.

## File Structure

The `projects.yaml` file has a simple structure where each top-level key represents a project identifier that can be referenced from the `workspaces.yaml` file:

```yaml
project-identifier:
  name: Project-Display-Name

another-project-identifier:
  name: Another-Project-Display-Name
```

## Key Components

### Project Definition

Each project is defined as a top-level key with the following structure:

```yaml
project-identifier:
  name: Project-Display-Name
  # Potential for future properties
```

Where:
- `project-identifier`: A unique identifier used to reference the project from `workspaces.yaml`
- `name`: The display name of the project in Terraform Cloud

## Current Projects

The file currently defines the following projects:

1. `aws-project`: For AWS infrastructure workspaces
2. `terraform-project`: For Terraform-related workspaces
3. `aws-grolier-project`: For Grolier-specific AWS infrastructure

## How Projects Are Used

Projects in Terraform Cloud are used to organize workspaces into logical groups. In this configuration:

1. Projects are defined in `projects.yaml`
2. Workspaces reference projects via the `project_id` property in `workspaces.yaml`
3. The Terraform code creates the projects in Terraform Cloud and associates workspaces with them

### Example Relationship

```yaml
# In projects.yaml
aws-project:
  name: AWS-Project

# In workspaces.yaml
aws-master:
  project_id: aws-project
  # Other workspace properties...
```

## Adding a New Project

To add a new project:

1. Add a new top-level key with a unique project identifier
2. Set the `name` property to the desired display name

Example:

```yaml
new-project:
  name: New-Project-Display-Name
```

## Modifying an Existing Project

To modify an existing project:

1. Locate the project entry by its identifier
2. Update the properties as needed

Note: Changing a project identifier requires updating all references to it in the `workspaces.yaml` file.

## Removing a Project

Before removing a project:

1. Ensure no workspaces reference the project in `workspaces.yaml`
2. Remove the project entry from `projects.yaml`

## Schema Validation

The `projects.yaml` file is processed by the Terraform code in `iac/tfc-workspace-tf/locals.tf`, which expects the following structure:

```hcl
locals {
  projects = try(yamldecode(file("configs/projects.yaml")), {})
  # Other locals...
}
```

And is used in `main.tf` to create Terraform Cloud projects:

```hcl
resource "tfe_project" "this" {
  for_each = local.projects

  name         = each.value.name
  organization = var.organization
}
```

Ensure your changes maintain compatibility with this processing logic.

## Project to Workspace Relationship

The relationship between projects and workspaces is established in `main.tf`:

```hcl
resource "tfe_workspace" "this" {
  # Other properties...
  project_id = try(tfe_project.this[lookup(each.value, "project_id", null)].id, null)
}
```

This means:

1. Each workspace can belong to one project
2. The `project_id` in `workspaces.yaml` must match a key in `projects.yaml`
3. If a project is referenced but doesn't exist, the workspace creation will fail

## AI-Friendly Markers

For AI assistants parsing this file:

1. **Project Definitions**: Each top-level key represents a project
2. **Project References**: The top-level keys are referenced by the `project_id` property in `workspaces.yaml`
3. **Display Names**: The `name` property contains the human-readable project name

## Best Practices

1. Use consistent naming conventions for project identifiers
2. Keep project display names clear and descriptive
3. Ensure all referenced projects exist before applying changes
4. Consider grouping workspaces by their purpose or environment

## Related Files

- `iac/tfc-workspace-tf/locals.tf`: Processes the YAML configuration
- `iac/tfc-workspace-tf/main.tf`: Creates the Terraform Cloud resources
- `iac/tfc-workspace-tf/configs/workspaces.yaml`: References projects via the `project_id` property

## Future Extensibility

While the current schema is simple, it could be extended in the future to include additional properties such as:

- Project descriptions
- Default settings for workspaces in the project
- Access control settings
- Custom metadata

If such extensions are implemented, this guide should be updated accordingly.
