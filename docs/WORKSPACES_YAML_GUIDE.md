# Terraform Cloud Workspaces Configuration Guide

This document provides a comprehensive guide to managing the `workspaces.yaml` file located at `iac/tfc-workspace-tf/configs/workspaces.yaml`. This file is used to define and configure Terraform Cloud workspaces for the company's infrastructure.

## File Structure

The `workspaces.yaml` file has the following high-level structure:

```yaml
defs:
  default: &default
    # Default workspace settings
  variables:
    # Reusable variable definitions

workspace-name-1:
  # Workspace configuration

workspace-name-2:
  # Workspace configuration
```

## Key Components

### 1. Definitions Section (`defs`)

The `defs` section contains reusable configurations using YAML anchors:

- `&default`: Default workspace settings
- Variables with anchors like `&TFC_AWS_PROVIDER_AUTH`: Reusable variable definitions

### 2. Workspace Definitions

Each workspace is defined as a top-level key (e.g., `aws-master`, `aws-company-prod`) with the following structure:

```yaml
workspace-name:
  <<: *default  # Include default settings
  name: display-name
  description: workspace-description
  working_directory: path/to/terraform/code
  auto_apply: true|false
  project_id: project-identifier
  trigger_prefixes:
    - path/to/watch/for/changes
  vcs_repo:
    branch: branch-name
    identifier: org/repo-name
    github_install_id: true|false
    bitbucket_auth_token_id: true|false
  variables:
    - key: variable-name
      category: terraform|env
      value: variable-value
      description: optional-description
      sensitive: true|false
      hcl: true|false
```

## YAML Anchors and References

The file uses YAML anchors and references to avoid repetition:

1. **Anchors** (`&name`): Define reusable blocks
2. **References** (`*name`): Include the anchored content
3. **Merge Key** (`<<: *name`): Merge all properties from the referenced anchor

Example:
```yaml
defs:
  default: &default
    terraform_version: 1.7.5
    auto_apply: true

aws-master:
  <<: *default  # Includes terraform_version and auto_apply
  # Other settings...
```

## Common Properties

### Workspace Properties

| Property | Description | Default |
|----------|-------------|---------|
| `name` | Display name of the workspace | Required |
| `description` | Description of the workspace | Required |
| `working_directory` | Path to Terraform code within the repository | Required |
| `auto_apply` | Whether to automatically apply changes | From `*default` |
| `terraform_version` | Terraform version to use | From `*default` |
| `project_id` | Project to organize the workspace under | Required |
| `trigger_prefixes` | Paths that trigger runs when changed | Optional |

### VCS Repository Properties

| Property | Description |
|----------|-------------|
| `branch` | Git branch to use |
| `identifier` | Repository identifier (org/repo) |
| `github_install_id` | Use GitHub App installation |
| `bitbucket_auth_token_id` | Use Bitbucket OAuth token |

### Variable Properties

| Property | Description | Default |
|----------|-------------|---------|
| `key` | Variable name | Required |
| `category` | `terraform` or `env` | `terraform` |
| `value` | Variable value | Required |
| `description` | Variable description | Optional |
| `sensitive` | Whether the variable is sensitive | `false` |
| `hcl` | Whether the value is HCL code | `false` |

## Common Variable Patterns

The file defines common variables that are reused across workspaces:

```yaml
defs:
  variables:
    - &TFC_AWS_PROVIDER_AUTH
      key: TFC_AWS_PROVIDER_AUTH
      category: env
      value: true
    - &TFC_AWS_RUN_ROLE_ARN
      key: TFC_AWS_RUN_ROLE_ARN
      category: env
      value: arn:aws:iam::************:role/tfc-role
    - &aws_provision_role
      key: aws_provision_role
      category: terraform
      value: OrganizationAccountAccessRole
```

These can be referenced in workspace variables:

```yaml
variables:
  - *TFC_AWS_PROVIDER_AUTH
  - *TFC_AWS_RUN_ROLE_ARN
  - *aws_provision_role
  - key: aws_provision_id
    category: terraform
    value: ************
```

## Adding a New Workspace

To add a new workspace:

1. Add a new top-level key with the workspace name
2. Include the default settings with `<<: *default`
3. Configure workspace-specific settings
4. Add required variables

Example:

```yaml
new-workspace:
  <<: *default
  name: new-workspace
  description: Description of the new workspace
  working_directory: path/to/terraform/code
  auto_apply: false
  project_id: project-id
  trigger_prefixes:
    - path/to/watch
  vcs_repo:
    branch: main
    identifier: org/repo-name
    github_install_id: true
  variables:
    - *TFC_AWS_PROVIDER_AUTH
    - *TFC_AWS_RUN_ROLE_ARN
    - key: custom_variable
      category: terraform
      value: custom_value
```

## Modifying an Existing Workspace

To modify an existing workspace:

1. Locate the workspace entry by its key
2. Update the properties as needed
3. Add, modify, or remove variables as required

## Schema Validation

The `workspaces.yaml` file is processed by the Terraform code in `iac/tfc-workspace-tf/locals.tf`, which expects the following structure:

```hcl
locals {
  workspaces_raw = yamldecode(file("configs/workspaces.yaml"))
  workspaces = { for k, v in local.workspaces_raw : k => v if k != "defs" }
  
  flat_vars = flatten([
    for workspace_name, workspace_details in local.workspaces : [
      for v in try(workspace_details["variables"], []) :
      merge(v, { workspace = workspace_name })
    ]
  ])
}
```

Ensure your changes maintain compatibility with this processing logic.

## AI-Friendly Markers

For AI assistants parsing this file:

1. **Workspace Definitions**: Top-level keys except for `defs`
2. **Variable References**: Entries starting with `*` (e.g., `*TFC_AWS_PROVIDER_AUTH`)
3. **Default Settings**: Look for `<<: *default` to identify inherited properties
4. **VCS Integration Type**: Check for `github_install_id: true` or `bitbucket_auth_token_id: true`
5. **Project Organization**: The `project_id` property links to entries in `projects.yaml`

## Related Files

- `iac/tfc-workspace-tf/locals.tf`: Processes the YAML configuration
- `iac/tfc-workspace-tf/main.tf`: Creates the Terraform Cloud resources
- `iac/tfc-workspace-tf/configs/projects.yaml`: Defines the projects referenced by `project_id`
