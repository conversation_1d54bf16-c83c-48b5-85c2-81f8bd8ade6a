import os
import asyncio
from dotenv import load_dotenv
import sys
import json

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the TelegramService class
from src.services.telegram_service import TelegramService
from src.services.logging_service import LoggingService

async def test_send_pdf():
    """Test sending a PDF file using the Telegram API"""
    # Load environment variables
    load_dotenv(".env.local")

    # Get the Telegram bot token
    token = os.getenv('TELEGRAM_BOT_TOKEN')
    if not token:
        print("ERROR: TELEGRAM_BOT_TOKEN environment variable not set")
        return

    # Initialize logging service
    logging_service = LoggingService(
        log_dir=os.getenv('LOG_DIR', 'logs')
    )

    # Create a TelegramService instance
    telegram_service = TelegramService(
        token=token,
        connection_pool_size=1,
        connect_timeout=5.0,
        read_timeout=30.0,
        max_session_history=10,
        max_sessions=10,
        logging_service=logging_service
    )

    # Get bot info
    try:
        bot_info = await telegram_service.bot.get_me()
        print(f"Bot info: {bot_info.username} (ID: {bot_info.id})")
    except Exception as e:
        print(f"Error getting bot info: {e}")
        return

    # Define the path to the PDF file
    pdf_path = os.path.join('data', 'tnc.pdf')
    abs_pdf_path = os.path.abspath(pdf_path)

    # Check if the file exists
    if not os.path.exists(abs_pdf_path):
        print(f"ERROR: PDF file not found at {abs_pdf_path}")
        return

    # Check if the file is readable
    if not os.access(abs_pdf_path, os.R_OK):
        print(f"ERROR: PDF file is not readable at {abs_pdf_path}")
        return

    # Print file information
    print(f"Found PDF file at: {abs_pdf_path}")
    print(f"File size: {os.path.getsize(abs_pdf_path)} bytes")

    # Try to read the first few bytes of the PDF
    try:
        with open(abs_pdf_path, 'rb') as f:
            first_bytes = f.read(100)
            print(f"First few bytes of PDF: {first_bytes[:20]}")
    except Exception as e:
        print(f"Error reading PDF: {e}")

    # Get the chat ID from the environment variable or use a default
    chat_id = os.getenv('TEST_CHAT_ID', '642972630')  # Default to Nicholas's chat ID
    if not chat_id:
        print("ERROR: TEST_CHAT_ID environment variable not set")
        return

    print(f"Sending PDF to chat ID: {chat_id}")

    # Try to send the PDF
    try:
        # Send the document
        result = await telegram_service.send_document(
            chat_id=chat_id,
            document_path=abs_pdf_path,
            caption="Test PDF document"
        )

        print(f"Document sent successfully: {result}")
    except Exception as e:
        print(f"ERROR sending document: {e}")

if __name__ == "__main__":
    # Run the async function
    asyncio.run(test_send_pdf())
