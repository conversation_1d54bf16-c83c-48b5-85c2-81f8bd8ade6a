import os
import asyncio
from dotenv import load_dotenv
import sys
import json

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the necessary modules
from src.services.telegram_service import TelegramService
from src.services.logging_service import LoggingService
from main import send_tnc_pdf

async def test_send_pdf():
    """Test sending a PDF file using the Telegram API"""
    # Load environment variables
    load_dotenv(".env.local")

    # Get the Telegram bot token
    token = os.getenv('TELEGRAM_BOT_TOKEN')
    if not token:
        print("ERROR: TELEGRAM_BOT_TOKEN environment variable not set")
        return

    # Initialize logging service
    logging_service = LoggingService(
        log_dir=os.getenv('LOG_DIR', 'logs')
    )

    # Create a TelegramService instance
    telegram_service = TelegramService(
        token=token,
        connection_pool_size=1,
        connect_timeout=5.0,
        read_timeout=30.0,
        max_session_history=10,
        max_sessions=10,
        logging_service=logging_service
    )

    # Get bot info
    try:
        bot_info = await telegram_service.bot.get_me()
        print(f"Bot info: {bot_info.username} (ID: {bot_info.id})")
    except Exception as e:
        print(f"Error getting bot info: {e}")
        return

    # Get the chat ID from the environment variable or use a default
    chat_id = os.getenv('TEST_CHAT_ID', '642972630')  # Default to Nicholas's chat ID
    if not chat_id:
        print("ERROR: TEST_CHAT_ID environment variable not set")
        return

    print(f"Sending PDF to chat ID: {chat_id}")

    # Try to send the PDF using the dedicated function
    pdf_result = await send_tnc_pdf(chat_id, telegram_service)
    print(f"PDF sending result: {pdf_result}")

if __name__ == "__main__":
    # Run the async function
    asyncio.run(test_send_pdf())
