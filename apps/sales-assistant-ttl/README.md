# AI-Powered Messaging Ordering System

A FastAPI application that processes orders via messaging platforms (Telegram or WhatsApp) using AI and stores data in CSV files. This system allows customers to place orders using natural language, with AI processing to understand their intent and order details.

## Features

- **Natural Language Processing**: Uses OpenAI's GPT models to understand customer messages
- **Messaging Platform Integration**: Supports both Telegram Bot API and Twilio's WhatsApp API
- **Menu Management**: Stores menu items in CSV files with stock tracking
- **Order Processing**: Analyzes customer orders, calculates totals, and stores order data
- **Conversation History**: Maintains conversation context for better responses
- **Recommendations**: Provides personalized menu recommendations
- **Stock Management**: Tracks inventory and prevents ordering out-of-stock items

## Tech Stack

- **Backend**: FastAPI (Python)
- **AI**: OpenAI GPT models
- **Messaging**: Telegram Bot API and/or Twilio WhatsApp API
- **Data Storage**: CSV files
- **Deployment**: Docker containerization with Docker Compose
- **Logging**: File-based logging system

## Project Structure

```
sales-assistant-poc/
├── data/                  # Data storage
│   ├── menu.csv           # Menu items
│   └── orders.csv         # Order records
├── logs/                  # Log files directory
├── src/                   # Source code
│   ├── controllers/       # Business logic
│   ├── models/            # Data models
│   └── services/          # External service integrations
├── tests/                 # Unit tests
├── .env.example           # Environment variables template
├── Dockerfile             # Docker container configuration
├── docker-compose.yml     # Docker Compose configuration
├── main.py                # Application entry point
├── run.py                 # Alternative entry point
└── requirements.txt       # Python dependencies
```

## Installation

### Prerequisites

- Python 3.8 or higher (for local development)
- Docker and Docker Compose (for containerized deployment)
- Telegram Bot (created via BotFather) or Twilio account with WhatsApp capabilities
- OpenAI API key

### Setup

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd sales-assistant-poc
   ```

2. Create and activate a virtual environment:
   ```bash
   # On Windows
   python -m venv venv
   .\venv\Scripts\Activate.ps1

   # On macOS/Linux
   python -m venv venv
   source venv/bin/activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Create a `.env` file based on `.env.example`:
   ```bash
   cp .env.example .env
   ```

5. Edit the `.env` file with your credentials:
   ```
   # Telegram Configuration
   TELEGRAM_BOT_TOKEN=your_telegram_bot_token

   # OpenAI Configuration
   OPENAI_API_KEY=your_openai_api_key
   OPENAI_MODEL=gpt-4.1-nano

   # CSV File Configuration
   MENU_CSV_PATH=data/menu.csv
   ORDERS_CSV_PATH=data/orders.csv

   # FastAPI Configuration
   ENVIRONMENT=development
   PORT=8080

   # Logging Configuration
   LOG_DIR=logs
   ```

   If you prefer to use WhatsApp instead, uncomment and fill in the Twilio configuration in the `.env.example` file.

6. Ensure the data directory exists:
   ```bash
   mkdir -p data
   ```

7. Customize the menu by editing `data/menu.csv` or using the provided sample:
   ```bash
   cp sample_menu.csv data/menu.csv
   ```

## Running the Application

### Development Mode

```bash
python run.py
```

This will start the FastAPI application with hot-reloading enabled.

### Alternative Port

If port 8080 is in use, you can specify a different port in your `.env` file:

```
PORT=3000
```

### Production Mode

For production deployment, set `ENVIRONMENT=production` in your `.env` file and use a production ASGI server like Uvicorn or Gunicorn:

```bash
uvicorn main:app --host 0.0.0.0 --port 8080
```

### Docker Deployment

The application can be easily deployed using Docker:

1. Make sure you have Docker and Docker Compose installed
2. Create and configure your `.env` file as described above
3. Build and start the containers:

```bash
docker-compose up -d
```

4. Access the application at http://localhost:8080
5. View logs:

```bash
docker-compose logs -f
```

The Docker setup provides:
- Automatic loading of environment variables from `.env`
- Persistent volumes for data and logs
- Health checks to ensure the application is running
- Automatic restart if the application crashes

For more detailed Docker setup instructions, see [DOCKER_SETUP.md](docs/DOCKER_SETUP.md).

### AWS ECR Deployment

For deploying to Amazon Elastic Container Registry (ECR), see [ECR_DEPLOYMENT.md](docs/ECR_DEPLOYMENT.md).

## Configuring Messaging Platforms

### Telegram Bot Setup

See the [TELEGRAM_SETUP.md](docs/TELEGRAM_SETUP.md) file for detailed instructions on setting up your Telegram bot.

### WhatsApp (Twilio) Setup

1. In your Twilio console, navigate to WhatsApp settings
2. Set up your webhook URL to point to your deployed application:
   ```
   https://your-app-domain.com/webhook
   ```
3. Ensure your WhatsApp sandbox is active or you have a production WhatsApp Business account

## Testing

Run the test suite with:

```bash
pytest
```

## Customer Interaction Flow

1. **Initial Contact**: Customer sends a message to your Telegram bot or WhatsApp number
2. **Menu Request**: Customer can ask to see the menu (with stock information)
3. **Recommendations**: Customer can ask for recommendations
4. **Placing an Order**: Customer can order items in natural language
5. **Stock Verification**: System checks if requested items are in stock
6. **Order Confirmation**: System provides an order summary and asks for confirmation
7. **Order Processing**: Once confirmed, the order is saved to the CSV file

## Customization

### Modifying the Menu

Edit the `data/menu.csv` file to update your menu items. The file should have the following columns:
- Name
- Category
- Description
- Price
- Available (TRUE/FALSE)

### Changing AI Behavior

#### Modifying the System Prompt

To modify how the AI responds to customers, edit the system prompt in `data/system_prompt.txt`. If this file doesn't exist, the system will use a default prompt.

#### Changing the OpenAI Model

You can change the OpenAI model used by the application by setting the `OPENAI_MODEL` environment variable in your `.env` file:

```
OPENAI_MODEL=gpt-4.1-nano  # Default model
```

Other options include:
- `gpt-4o`
- `gpt-4-turbo`
- `gpt-3.5-turbo`

The model choice affects response quality and API costs.

### Customizing Response Templates

To change how responses are formatted, edit the methods in `src/controllers/order_controller.py`:
- `_format_menu`: Customize menu display
- `_process_intent`: Modify response logic for different intents
- `_process_order_items`: Change how orders are processed

## Troubleshooting

### Common Issues

- **Docker container not starting**: Check logs with `docker-compose logs` and ensure all required environment variables are set in your `.env` file.

- **API key errors**: Verify that your OpenAI API key and Telegram Bot token are correctly set in the `.env` file.

- **Port conflicts**: If port 8080 is already in use, change the PORT environment variable in your `.env` file.

- **Volume permissions**: Ensure the Docker user has write permissions to the data and logs directories.

See the [USAGE.md](docs/USAGE.md) file for more detailed troubleshooting information and common issues.

### Viewing Logs

Logs are stored in the `logs` directory. You can access them through the API at `/logs` endpoint or directly from the filesystem.

When using Docker, you can view logs with:
```bash
docker-compose logs -f
```

## License

[MIT License](LICENSE)

## Documentation

Detailed documentation is available in the `docs` directory:

- [DOCKER_SETUP.md](docs/DOCKER_SETUP.md) - Detailed Docker setup and configuration
- [ECR_DEPLOYMENT.md](docs/ECR_DEPLOYMENT.md) - AWS ECR deployment instructions
- [TELEGRAM_SETUP.md](docs/TELEGRAM_SETUP.md) - Telegram bot setup guide
- [USAGE.md](docs/USAGE.md) - Detailed usage instructions and troubleshooting

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
