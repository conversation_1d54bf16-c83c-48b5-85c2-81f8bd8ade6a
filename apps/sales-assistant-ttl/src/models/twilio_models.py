from pydantic import BaseModel, Field
from typing import Optional

class TwilioWebhookRequest(BaseModel):
    """
    Model for Twilio webhook request data
    """
    MessageSid: Optional[str] = Field(None, description="The unique ID of the message")
    Body: str = Field(..., description="The body of the message")
    From: str = Field(..., description="The sender's phone number")
    To: Optional[str] = Field(None, description="The recipient's phone number")
    NumMedia: Optional[int] = Field(0, description="The number of media attachments")
    
    class Config:
        schema_extra = {
            "example": {
                "MessageSid": "SM12345678901234567890123456789012",
                "Body": "I'd like to order a pizza",
                "From": "whatsapp:+1234567890",
                "To": "whatsapp:+0987654321",
                "NumMedia": 0
            }
        }
