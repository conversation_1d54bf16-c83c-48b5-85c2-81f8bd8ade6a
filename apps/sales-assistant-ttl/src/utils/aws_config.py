import boto3
from botocore.exceptions import BotoCoreError, ClientError
from typing import Optional, Dict

_secret_cache: Dict[str, str] = {}

def get_config_value(secret_arn: str, region: str = "ap-southeast-1") -> Optional[str]:
    """
    Retrieve a config value from AWS Secret Manager using the provided secret_arn and region.
    Each secret ARN stores only a single value (not a JSON object).
    Caches secrets in memory for performance.
    """
    if not secret_arn:
        print("SECRET_ARN must be provided to get_config_value.")
        return None
    if secret_arn in _secret_cache:
        return _secret_cache[secret_arn]
    try:
        session = boto3.session.Session()
        client = session.client(service_name="secretsmanager", region_name=region)
        get_secret_value_response = client.get_secret_value(SecretId=secret_arn)
        secret_string = get_secret_value_response.get("SecretString")
        if secret_string:
            _secret_cache[secret_arn] = secret_string
            return secret_string
    except (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ClientError) as e:
        print(f"Error retrieving secret from AWS Secret Manager: {e}")
    return None 