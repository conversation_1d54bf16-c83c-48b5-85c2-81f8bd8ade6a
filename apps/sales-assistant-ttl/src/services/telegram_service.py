from telegram import Update, <PERSON><PERSON>
import json
import asyncio
import datetime
import os
from telegram.request import HTT<PERSON><PERSON><PERSON>equest
from typing import Dict, List, Any, Optional
from src.services.logging_service import LoggingService

class TelegramService:
    """
    Service for interacting with Telegram Bot API for messaging
    with session memory capabilities
    """

    def __init__(self, token, connection_pool_size=8, connect_timeout=5.0, read_timeout=30.0,
                 max_session_history=10, max_sessions=1000, logging_service: Optional[LoggingService] = None):
        """
        Initialize the Telegram service

        Args:
            token (str): Telegram Bot API token
            connection_pool_size (int): Size of the connection pool
            connect_timeout (float): Connection timeout in seconds
            read_timeout (float): Read timeout in seconds
            max_session_history (int): Maximum number of messages to store per session
            max_sessions (int): Maximum number of sessions to store
            logging_service (LoggingService, optional): Service for logging chat history
        """
        self.token = token

        # Configure request parameters with larger connection pool
        request = HTTPXRequest(
            connection_pool_size=connection_pool_size,
            connect_timeout=connect_timeout,
            read_timeout=read_timeout
        )

        # Initialize bot with custom request parameters
        self.bot = Bot(token=token, request=request)

        # Initialize session storage
        self.sessions: Dict[str, List[Dict]] = {}
        self.max_session_history = max_session_history
        self.max_sessions = max_sessions

        # Initialize logging service
        self.logging_service = logging_service

    async def send_message(self, chat_id, message_body, retry_count=3, retry_delay=1.0, save_to_session=True):
        """
        Send a message to a Telegram chat with retry logic

        Args:
            chat_id (str): Telegram chat ID
            message_body (str): Message content to send
            retry_count (int): Number of retries on failure
            retry_delay (float): Delay between retries in seconds
            save_to_session (bool): Whether to save the message to the session history

        Returns:
            dict: Response from Telegram API
        """
        for attempt in range(retry_count + 1):
            try:
                # Send the message
                message = await self.bot.send_message(
                    chat_id=chat_id,
                    text=message_body
                )

                # Save to session history if enabled
                if save_to_session:
                    self.add_to_session(
                        chat_id=chat_id,
                        role="assistant",
                        content=message_body,
                        metadata={
                            "message_id": message.message_id,
                            "sent": True
                        }
                    )

                # Log the message if logging service is available
                if self.logging_service:
                    # Use bot username as the assistant username
                    bot_info = await self.bot.get_me()
                    bot_username = bot_info.username or "bot"
                    self.logging_service.log_chat_message(
                        chat_id=str(chat_id),
                        username=bot_username,
                        role="assistant",
                        message=message_body
                    )

                return {
                    "message_id": message.message_id,
                    "chat_id": message.chat_id,
                    "text": message.text
                }
            except Exception as e:
                error_message = str(e)
                print(f"Error sending Telegram message (attempt {attempt+1}/{retry_count+1}): {error_message}")

                # If this was the last attempt, return the error
                if attempt >= retry_count:
                    return {"error": error_message}

                # Otherwise wait and retry
                await asyncio.sleep(retry_delay * (attempt + 1))  # Exponential backoff

    async def send_document(self, chat_id, document_path, caption=None, retry_count=3, retry_delay=1.0, save_to_session=True):
        """
        Send a document to a Telegram chat with retry logic

        Args:
            chat_id (str): Telegram chat ID
            document_path (str): Path to the document file
            caption (str, optional): Caption for the document
            retry_count (int): Number of retries on failure
            retry_delay (float): Delay between retries in seconds
            save_to_session (bool): Whether to save the message to the session history

        Returns:
            dict: Response from Telegram API
        """
        for attempt in range(retry_count + 1):
            try:
                print(f"Attempting to send document: {document_path} to chat_id: {chat_id}")
                print(f"File exists: {os.path.exists(document_path)}")
                if os.path.exists(document_path):
                    print(f"File size: {os.path.getsize(document_path)} bytes")
                    print(f"File readable: {os.access(document_path, os.R_OK)}")
                    print(f"File absolute path: {os.path.abspath(document_path)}")

                # Try to get bot info to verify the bot is working
                try:
                    bot_info = await self.bot.get_me()
                    print(f"Bot info: {bot_info.username} (ID: {bot_info.id})")
                except Exception as e:
                    print(f"Error getting bot info: {e}")

                # Open the document file
                with open(document_path, 'rb') as document:
                    print(f"File opened successfully, sending document...")
                    # Send the document
                    message = await self.bot.send_document(
                        chat_id=chat_id,
                        document=document,
                        caption=caption
                    )

                print(f"Document sent successfully: {message.document.file_id}")

                # Save to session history if enabled
                if save_to_session:
                    content = f"[Document: {document_path}]"
                    if caption:
                        content += f"\n{caption}"

                    self.add_to_session(
                        chat_id=chat_id,
                        role="assistant",
                        content=content,
                        metadata={
                            "message_id": message.message_id,
                            "document_id": message.document.file_id,
                            "document_path": document_path,
                            "sent": True
                        }
                    )

                # Log the message if logging service is available
                if self.logging_service:
                    # Use bot username as the assistant username
                    bot_info = await self.bot.get_me()
                    bot_username = bot_info.username or "bot"
                    log_message = f"[Document: {document_path}]"
                    if caption:
                        log_message += f"\n{caption}"

                    self.logging_service.log_chat_message(
                        chat_id=str(chat_id),
                        username=bot_username,
                        role="assistant",
                        message=log_message
                    )

                return {
                    "message_id": message.message_id,
                    "chat_id": message.chat_id,
                    "document_id": message.document.file_id,
                    "document_path": document_path
                }
            except Exception as e:
                error_message = str(e)
                print(f"Error sending Telegram document (attempt {attempt+1}/{retry_count+1}): {error_message}")

                # If this was the last attempt, return the error
                if attempt >= retry_count:
                    return {"error": error_message}

                # Otherwise wait and retry
                await asyncio.sleep(retry_delay * (attempt + 1))  # Exponential backoff

    def extract_message_data(self, update_json, save_to_session=True):
        """
        Extract message data from a Telegram update

        Args:
            update_json (dict): Telegram update object
            save_to_session (bool): Whether to save the message to the session history

        Returns:
            tuple: (chat_id, user_id, message_text, username)
        """
        try:
            # Parse the update
            update = Update.de_json(update_json, self.bot)

            # Extract message data
            message = update.message or update.edited_message

            if message:
                chat_id = message.chat_id
                user_id = message.from_user.id
                message_text = message.text or ""
                username = message.from_user.username or message.from_user.first_name

                # Save to session history if enabled and message is not empty
                if save_to_session and message_text:
                    self.add_to_session(
                        chat_id=chat_id,
                        role="user",
                        content=message_text,
                        metadata={
                            "user_id": user_id,
                            "username": username,
                            "message_id": message.message_id
                        }
                    )

                # Log the message if logging service is available and message is not empty
                if self.logging_service and message_text:
                    self.logging_service.log_chat_message(
                        chat_id=str(chat_id),
                        username=username,
                        role="user",
                        message=message_text
                    )

                return chat_id, user_id, message_text, username

            # Handle callback queries (button clicks)
            if update.callback_query:
                callback_query = update.callback_query
                chat_id = callback_query.message.chat_id
                user_id = callback_query.from_user.id
                message_text = callback_query.data or ""
                username = callback_query.from_user.username or callback_query.from_user.first_name

                # Save to session history if enabled and message is not empty
                if save_to_session and message_text:
                    self.add_to_session(
                        chat_id=chat_id,
                        role="user",
                        content=message_text,
                        metadata={
                            "user_id": user_id,
                            "username": username,
                            "callback_query": True
                        }
                    )

                # Log the message if logging service is available and message is not empty
                if self.logging_service and message_text:
                    self.logging_service.log_chat_message(
                        chat_id=str(chat_id),
                        username=username,
                        role="user",
                        message=message_text
                    )

                return chat_id, user_id, message_text, username

            return None, None, None, None
        except Exception as e:
            print(f"Error extracting message data: {e}")
            return None, None, None, None

    def format_order_json(self, item_name, quantity):
        """
        Format an order as JSON

        Args:
            item_name (str): Name of the ordered item
            quantity (int): Quantity of the ordered item

        Returns:
            str: JSON string with order details
        """
        order_data = {
            "item_name": item_name,
            "quantity": quantity,
            "order_date_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        return json.dumps(order_data, indent=2)

    def add_to_session(self, chat_id: str, role: str, content: str, metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Add a message to the session history for a specific chat

        Args:
            chat_id (str): The chat ID to add the message to
            role (str): The role of the message sender (e.g., 'user', 'assistant')
            content (str): The content of the message
            metadata (Dict, optional): Additional metadata for the message
        """
        # Convert chat_id to string to ensure consistency
        chat_id = str(chat_id)

        # Initialize session if it doesn't exist
        if chat_id not in self.sessions:
            self.sessions[chat_id] = []

            # If we've reached the maximum number of sessions, remove the oldest one
            if len(self.sessions) > self.max_sessions:
                oldest_chat_id = next(iter(self.sessions))
                del self.sessions[oldest_chat_id]

        # Create message entry
        timestamp = datetime.datetime.now().isoformat()
        message = {
            "role": role,
            "content": content,
            "timestamp": timestamp
        }

        # Add metadata if provided
        if metadata:
            message["metadata"] = metadata

        # Add to session
        self.sessions[chat_id].append(message)

        # Trim session if it exceeds the maximum history size
        if len(self.sessions[chat_id]) > self.max_session_history:
            self.sessions[chat_id] = self.sessions[chat_id][-self.max_session_history:]

        # Log the message if logging service is available and metadata contains username
        if self.logging_service and metadata and "username" in metadata:
            self.logging_service.log_chat_message(
                chat_id=chat_id,
                username=metadata["username"],
                role=role,
                message=content
            )

    def get_session(self, chat_id: str) -> List[Dict[str, Any]]:
        """
        Get the session history for a specific chat

        Args:
            chat_id (str): The chat ID to get the session for

        Returns:
            List[Dict]: The session history for the chat
        """
        chat_id = str(chat_id)
        return self.sessions.get(chat_id, [])

    def clear_session(self, chat_id: str) -> None:
        """
        Clear the session history for a specific chat

        Args:
            chat_id (str): The chat ID to clear the session for
        """
        chat_id = str(chat_id)
        if chat_id in self.sessions:
            self.sessions[chat_id] = []

    def get_session_as_string(self, chat_id: str) -> str:
        """
        Get the session history for a specific chat as a formatted string

        Args:
            chat_id (str): The chat ID to get the session for

        Returns:
            str: The formatted session history
        """
        chat_id = str(chat_id)
        session = self.get_session(chat_id)

        if not session:
            return "No session history available."

        result = []
        for msg in session:
            timestamp = msg.get("timestamp", "")
            if timestamp:
                timestamp = f"[{timestamp.split('T')[0]} {timestamp.split('T')[1][:8]}] "

            result.append(f"{timestamp}{msg['role'].capitalize()}: {msg['content']}")

        # Log the session history if logging service is available
        if self.logging_service:
            # Try to get username from the first user message
            username = "unknown"
            for msg in session:
                if msg['role'] == 'user' and 'metadata' in msg and 'username' in msg['metadata']:
                    username = msg['metadata']['username']
                    break

            self.logging_service.log_session_history(
                chat_id=chat_id,
                username=username,
                session_history=session
            )

        return "\n".join(result)
