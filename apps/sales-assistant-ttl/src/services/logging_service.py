import os
import datetime
import json
from typing import Dict, Any, List, Optional

class LoggingService:
    """
    Service for logging chat history and system events to files
    """
    
    def __init__(self, log_dir: str = "logs"):
        """
        Initialize the logging service
        
        Args:
            log_dir (str): Directory to store log files
        """
        self.log_dir = log_dir
        self._ensure_log_dir_exists()
    
    def _ensure_log_dir_exists(self) -> None:
        """Ensure the log directory exists"""
        os.makedirs(self.log_dir, exist_ok=True)
    
    def _get_date_str(self) -> str:
        """Get the current date as a string in YYYY-MM-DD format"""
        return datetime.datetime.now().strftime("%Y-%m-%d")
    
    def _get_timestamp(self) -> str:
        """Get the current timestamp as a string"""
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def log_chat_message(self, chat_id: str, username: str, role: str, message: str) -> None:
        """
        Log a chat message to the appropriate log file
        
        Args:
            chat_id (str): ID of the chat
            username (str): Username of the sender
            role (str): Role of the sender (user or assistant)
            message (str): Message content
        """
        # Create a log entry
        log_entry = {
            "timestamp": self._get_timestamp(),
            "chat_id": chat_id,
            "username": username,
            "role": role,
            "message": message
        }
        
        # Get the log file path
        log_file = os.path.join(self.log_dir, f"chat_{self._get_date_str()}.log")
        
        # Write the log entry to the file
        try:
            with open(log_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(log_entry) + "\n")
        except Exception as e:
            print(f"Error writing to chat log file: {e}")
    
    def log_session_history(self, chat_id: str, username: str, session_history: List[Dict[str, Any]]) -> None:
        """
        Log the entire session history for a chat
        
        Args:
            chat_id (str): ID of the chat
            username (str): Username of the user
            session_history (List[Dict]): Session history
        """
        # Create a log entry
        log_entry = {
            "timestamp": self._get_timestamp(),
            "chat_id": chat_id,
            "username": username,
            "session_history": session_history
        }
        
        # Get the log file path
        log_file = os.path.join(self.log_dir, f"session_{chat_id}_{self._get_date_str()}.log")
        
        # Write the log entry to the file
        try:
            with open(log_file, "w", encoding="utf-8") as f:
                f.write(json.dumps(log_entry, indent=2))
        except Exception as e:
            print(f"Error writing to session log file: {e}")
    
    def log_order(self, order_data: Dict[str, Any]) -> None:
        """
        Log an order to the orders log file
        
        Args:
            order_data (Dict): Order data
        """
        # Create a log entry
        log_entry = {
            "timestamp": self._get_timestamp(),
            "order": order_data
        }
        
        # Get the log file path
        log_file = os.path.join(self.log_dir, f"orders_{self._get_date_str()}.log")
        
        # Write the log entry to the file
        try:
            with open(log_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(log_entry) + "\n")
        except Exception as e:
            print(f"Error writing to orders log file: {e}")
    
    def log_error(self, error_message: str, context: Optional[Dict[str, Any]] = None) -> None:
        """
        Log an error to the errors log file
        
        Args:
            error_message (str): Error message
            context (Dict, optional): Additional context for the error
        """
        # Create a log entry
        log_entry = {
            "timestamp": self._get_timestamp(),
            "error": error_message,
            "context": context or {}
        }
        
        # Get the log file path
        log_file = os.path.join(self.log_dir, f"errors_{self._get_date_str()}.log")
        
        # Write the log entry to the file
        try:
            with open(log_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(log_entry) + "\n")
        except Exception as e:
            print(f"Error writing to errors log file: {e}")
