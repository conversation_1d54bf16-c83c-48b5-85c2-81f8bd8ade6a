import os
import datetime
from google.oauth2 import service_account
from googleapiclient.discovery import build

class GoogleSheetsService:
    """
    Service for interacting with Google Sheets API for menu and order management
    """
    
    def __init__(self, credentials_file, menu_spreadsheet_id, orders_spreadsheet_id):
        """
        Initialize the Google Sheets service
        
        Args:
            credentials_file (str): Path to the Google API credentials file
            menu_spreadsheet_id (str): ID of the menu spreadsheet
            orders_spreadsheet_id (str): ID of the orders spreadsheet
        """
        self.credentials_file = credentials_file
        self.menu_spreadsheet_id = menu_spreadsheet_id
        self.orders_spreadsheet_id = orders_spreadsheet_id
        self.service = self._create_service()
        
    def _create_service(self):
        """
        Create and return a Google Sheets service
        
        Returns:
            Resource: Google Sheets API service
        """
        try:
            credentials = service_account.Credentials.from_service_account_file(
                self.credentials_file,
                scopes=['https://www.googleapis.com/auth/spreadsheets']
            )
            return build('sheets', 'v4', credentials=credentials)
        except Exception as e:
            print(f"Error creating Google Sheets service: {e}")
            return None
    
    def get_menu_items(self):
        """
        Get all menu items from the menu spreadsheet
        
        Returns:
            list: List of menu items with their details
        """
        try:
            # Assuming the menu is in the first sheet, with headers in row 1
            result = self.service.spreadsheets().values().get(
                spreadsheetId=self.menu_spreadsheet_id,
                range='Menu!A2:E'  # Assuming columns A-E contain item data
            ).execute()
            
            values = result.get('values', [])
            
            menu_items = []
            for row in values:
                # Skip rows that don't have enough data
                if len(row) < 4:
                    continue
                    
                # Assuming columns: Name, Category, Description, Price, Available (True/False)
                item = {
                    'name': row[0],
                    'category': row[1],
                    'description': row[2],
                    'price': float(row[3]),
                    'available': True if len(row) > 4 and row[4].lower() == 'true' else False
                }
                
                # Only include available items
                if item['available']:
                    menu_items.append(item)
            
            return menu_items
        except Exception as e:
            print(f"Error getting menu items: {e}")
            return []
    
    def save_order(self, customer_phone, items, total_amount):
        """
        Save an order to the orders spreadsheet
        
        Args:
            customer_phone (str): Customer's phone number
            items (list): List of ordered items
            total_amount (float): Total order amount
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Format the order items as a string
            items_str = ", ".join([
                f"{item['quantity']}x {item['name']} (${item['price'] * item['quantity']:.2f})"
                for item in items
            ])
            
            # Prepare the row data
            now = datetime.datetime.now()
            row_data = [
                now.strftime("%Y-%m-%d"),  # Date
                now.strftime("%H:%M:%S"),  # Time
                customer_phone,            # Customer Phone
                items_str,                 # Items
                f"${total_amount:.2f}"     # Total Amount
            ]
            
            # Append the row to the orders sheet
            self.service.spreadsheets().values().append(
                spreadsheetId=self.orders_spreadsheet_id,
                range='Orders!A2:E',  # Assuming columns A-E for order data
                valueInputOption='USER_ENTERED',
                insertDataOption='INSERT_ROWS',
                body={
                    'values': [row_data]
                }
            ).execute()
            
            return True
        except Exception as e:
            print(f"Error saving order: {e}")
            return False
