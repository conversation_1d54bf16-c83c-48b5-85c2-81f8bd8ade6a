import os
import csv
import datetime

class CSVService:
    """
    Service for interacting with CSV files for menu and order management
    """

    def __init__(self, menu_file_path, orders_file_path):
        """
        Initialize the CSV service

        Args:
            menu_file_path (str): Path to the menu CSV file
            orders_file_path (str): Path to the orders CSV file
        """
        self.menu_file_path = menu_file_path
        self.orders_file_path = orders_file_path

        # Ensure the orders file exists with headers
        self._ensure_orders_file_exists()

    def _ensure_orders_file_exists(self):
        """
        Ensure the orders CSV file exists with proper headers
        """
        if not os.path.exists(self.orders_file_path):
            # Create the directory if it doesn't exist
            os.makedirs(os.path.dirname(self.orders_file_path), exist_ok=True)

            # Create the file with headers
            with open(self.orders_file_path, 'w', newline='') as file:
                writer = csv.writer(file)
                writer.writerow(['Date', 'Time', 'Customer Phone', 'Items', 'Total Amount'])

    def get_menu_items(self):
        """
        Get all menu items from the menu CSV file

        Returns:
            list: List of menu items with their details
        """
        try:
            menu_items = []

            # Check if the file exists
            if not os.path.exists(self.menu_file_path):
                print(f"Menu file not found: {self.menu_file_path}")
                return []

            # Read the CSV file
            with open(self.menu_file_path, 'r', newline='') as file:
                reader = csv.DictReader(file)

                for row in reader:
                    # Convert price to float and check availability
                    try:
                        price = float(row.get('Price', 0))
                        available = row.get('Available', '').lower() == 'true'
                        stock = int(row.get('Stock', 0))

                        # Only include available items
                        if available:
                            item = {
                                'name': row.get('Name', ''),
                                'category': row.get('Category', ''),
                                'description': row.get('Description', ''),
                                'price': price,
                                'available': available,
                                'stock': stock
                            }
                            menu_items.append(item)
                    except (ValueError, TypeError) as e:
                        print(f"Error processing menu item: {row} - {e}")

            return menu_items
        except Exception as e:
            print(f"Error getting menu items: {e}")
            return []

    def save_order(self, customer_phone, items, total_amount):
        """
        Save an order to the orders CSV file

        Args:
            customer_phone (str): Customer's phone number
            items (list): List of ordered items
            total_amount (float): Total order amount

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Format the order items as a string
            items_str = ", ".join([
                f"{item['quantity']}x {item['name']} (${item['price'] * item['quantity']:.2f})"
                for item in items
            ])

            # Prepare the row data
            now = datetime.datetime.now()
            row_data = [
                now.strftime("%Y-%m-%d"),  # Date
                now.strftime("%H:%M:%S"),  # Time
                customer_phone,            # Customer Phone
                items_str,                 # Items
                f"${total_amount:.2f}"     # Total Amount
            ]

            # Append the row to the orders CSV file
            with open(self.orders_file_path, 'a', newline='') as file:
                writer = csv.writer(file)
                writer.writerow(row_data)

            return True
        except Exception as e:
            print(f"Error saving order: {e}")
            return False
