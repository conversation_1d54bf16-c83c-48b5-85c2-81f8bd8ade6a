import json
import asyncio
import datetime
import os
import aiohttp
import boto3
from botocore.exceptions import BotoCoreError, ClientError
from typing import Dict, List, Any, Optional, Tuple
from dotenv import load_dotenv
from src.services.logging_service import LoggingService
from src.utils.aws_config import get_config_value

# Load environment variables
load_dotenv(".env")
whatsapp_secret_arn = os.getenv("WHATSAPP_SECRET_ARN")
whatsapp_api_url_arn = os.getenv("WHATSAPP_API_URL_ARN")

# Global cache for WhatsApp API key
def get_whatsapp_api_key() -> Optional[str]:
    """
    Retrieve the WhatsApp API key from AWS Secret Manager and cache it in memory.
    Returns None if retrieval fails.
    """
    if not hasattr(get_whatsapp_api_key, "_cached_key"):
        secret_name = get_config_value('WHATSAPP_API_SECRET_NAME', whatsapp_secret_arn),
        region_name = os.getenv("AWS_REGION", "ap-southeast-1")
        try:
            session = boto3.session.Session()
            client = session.client(
                service_name="secretsmanager",
                region_name=region_name
            )
            get_secret_value_response = client.get_secret_value(SecretId=secret_name)
            secret = get_secret_value_response.get("SecretString")
            if secret:
                get_whatsapp_api_key._cached_key = secret
            else:
                print("No SecretString found in AWS Secret Manager response.")
                get_whatsapp_api_key._cached_key = None
        except (BotoCoreError, ClientError) as e:
            print(f"Error retrieving WhatsApp API key from AWS Secret Manager: {e}")
            get_whatsapp_api_key._cached_key = None
    return get_whatsapp_api_key._cached_key

class WhatsAppService:
    """
    Service for interacting with WhatsApp API for messaging
    with session memory capabilities
    """

    def __init__(self, max_session_history=10, max_sessions=1000, logging_service: Optional[LoggingService] = None):
        """
        Initialize the WhatsApp service

        Args:
            max_session_history (int): Maximum number of messages to store per session
            max_sessions (int): Maximum number of sessions to store
            logging_service (LoggingService, optional): Service for logging chat history
        """
        # Initialize session storage
        self.sessions: Dict[str, List[Dict]] = {}
        self.max_session_history = max_session_history
        self.max_sessions = max_sessions

        # Initialize logging service
        self.logging_service = logging_service

    async def send_message(self, chat_id, message_body, retry_count=3, retry_delay=1.0, save_to_session=True, media_url=None):
        """
        Send a message to a WhatsApp chat

        This method calls the API endpoint to send a WhatsApp message.

        Args:
            chat_id (str): WhatsApp chat ID (phone number)
            message_body (str): Message content to send
            retry_count (int): Number of retries on failure
            retry_delay (float): Delay between retries in seconds
            save_to_session (bool): Whether to save the message to the session history
            media_url (str, optional): URL to media to send with the message

        Returns:
            dict: Response from WhatsApp API
        """
        try:
            # Log the message we would send
            print(f"Would send WhatsApp message to {chat_id}: {message_body}")

            # Prepare the payload for the API request according to the expected format
            payload = {
                "phone": chat_id,
                "message": message_body
            }

            # Add media URL if provided
            if media_url:
                payload["mediaUrl"] = media_url

            # Call the API endpoint
            for attempt in range(retry_count + 1):
                try:
                    api_url = get_config_value(whatsapp_api_url_arn, region)
                    # api_url = os.getenv('WHATSAPP_API_URL', 'http://localhost:3030/api/send')
                    print(f"Sending WhatsApp message to API URL: {api_url}")
                    api_key = get_whatsapp_api_key()
                    if not api_key:
                        return {"error": "WhatsApp API key not available from AWS Secret Manager."}
                    headers = {"x-api-key": api_key}
                    async with aiohttp.ClientSession() as session:
                        async with session.post(api_url, json=payload, headers=headers) as response:
                            if response.status == 200:
                                api_response = await response.json()
                                print(f"API response: {api_response}")
                                break
                            else:
                                error_text = await response.text()
                                print(f"API error (attempt {attempt+1}/{retry_count+1}): {response.status} - {error_text}")
                                if attempt < retry_count:
                                    await asyncio.sleep(retry_delay * (attempt + 1))
                                else:
                                    return {"error": f"API error: {response.status} - {error_text}"}
                except aiohttp.ClientError as e:
                    print(f"Connection error (attempt {attempt+1}/{retry_count+1}): {e}")
                    if attempt < retry_count:
                        await asyncio.sleep(retry_delay * (attempt + 1))
                    else:
                        return {"error": f"Connection error: {e}"}

            # Save to session history if enabled
            if save_to_session:
                self.add_to_session(
                    chat_id=chat_id,
                    role="assistant",
                    content=message_body,
                    metadata={
                        "sent": True
                    }
                )

            # Log the message if logging service is available
            if self.logging_service:
                self.logging_service.log_chat_message(
                    chat_id=str(chat_id),
                    username="bot",
                    role="assistant",
                    message=message_body
                )

            # Return a success response
            return {"status": "success", "message": "Message sent successfully"}

        except Exception as e:
            error_message = str(e)
            print(f"Error sending WhatsApp message: {error_message}")
            return {"error": error_message}

    async def send_document(self, chat_id, document_path, caption=None, retry_count=3, retry_delay=1.0):
        """
        Send a document to a WhatsApp chat

        This method calls the API endpoint to send a WhatsApp document.

        Args:
            chat_id (str): WhatsApp chat ID (phone number)
            document_path (str): Path or URL to the document file
            caption (str, optional): Caption for the document
            retry_count (int): Number of retries on failure
            retry_delay (float): Delay between retries in seconds

        Returns:
            dict: Response from WhatsApp API
        """
        try:
            # Log the document we would send
            print(f"Would send WhatsApp document to {chat_id}: {document_path} with caption: {caption}")

            # Check if the document_path is a URL or a local file path
            is_url = document_path.startswith('http://') or document_path.startswith('https://')

            if is_url:
                # For URLs, extract the filename from the URL
                document_name = os.path.basename(document_path)
                file_extension = os.path.splitext(document_name)[1].lower()
                print(f"Using document URL: {document_path}")
            else:
                # For local files, check if they exist and are readable
                if not os.path.exists(document_path):
                    return {"error": f"Document not found: {document_path}"}

                if not os.access(document_path, os.R_OK):
                    return {"error": f"Document not readable: {document_path}"}

                # Get the document file name and determine mimetype
                document_name = os.path.basename(document_path)
                file_extension = os.path.splitext(document_name)[1].lower()

            # Map common file extensions to MIME types
            mime_types = {
                '.pdf': 'application/pdf',
                '.doc': 'application/msword',
                '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                '.xls': 'application/vnd.ms-excel',
                '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                '.ppt': 'application/vnd.ms-powerpoint',
                '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                '.jpg': 'image/jpeg',
                '.jpeg': 'image/jpeg',
                '.png': 'image/png',
                '.gif': 'image/gif',
                '.mp4': 'video/mp4',
                '.mp3': 'audio/mpeg',
                '.txt': 'text/plain',
                '.csv': 'text/csv',
                '.zip': 'application/zip',
                '.rar': 'application/x-rar-compressed'
            }

            # Get the MIME type or default to application/octet-stream
            mimetype = mime_types.get(file_extension, 'application/octet-stream')

            # Prepare the payload for the API request according to the expected format
            payload = {
                "phone": chat_id,
                "message": caption if caption else "",  # Use caption as the message text
                "documentUrl": document_path if is_url else None,  # Use the S3 URL directly
                "documentName": document_name,
                "mimetype": mimetype
            }

            print("Document payload: ", payload)

            # If it's not a URL but a local file, we can't send it directly
            # In a production environment, you would upload the file to S3 first
            if not is_url:
                print("Warning: Local file paths are not supported for WhatsApp document sending.")
                print("Please use an S3 URL instead. Falling back to sending a message with the document name.")
                # We'll just send a message with the document name
                payload["message"] = f"[Document: {document_name}] {caption or ''}"
                # Remove the document-related fields
                payload.pop("documentUrl", None)
                payload.pop("documentName", None)
                payload.pop("mimetype", None)

            # Call the API endpoint
            for attempt in range(retry_count + 1):
                try:
                    api_url = get_config_value(whatsapp_api_url_arn, region)
                    print(f"Sending WhatsApp document to API URL: {api_url}")
                    api_key = get_whatsapp_api_key()
                    if not api_key:
                        return {"error": "WhatsApp API key not available from AWS Secret Manager."}
                    headers = {"x-api-key": api_key}
                    async with aiohttp.ClientSession() as session:
                        async with session.post(api_url, json=payload, headers=headers) as response:
                            if response.status == 200:
                                api_response = await response.json()
                                print(f"API response: {api_response}")
                                break
                            else:
                                error_text = await response.text()
                                print(f"API error (attempt {attempt+1}/{retry_count+1}): {response.status} - {error_text}")
                                if attempt < retry_count:
                                    await asyncio.sleep(retry_delay * (attempt + 1))
                                else:
                                    return {"error": f"API error: {response.status} - {error_text}"}
                except aiohttp.ClientError as e:
                    print(f"Connection error (attempt {attempt+1}/{retry_count+1}): {e}")
                    if attempt < retry_count:
                        await asyncio.sleep(retry_delay * (attempt + 1))
                    else:
                        return {"error": f"Connection error: {e}"}

            # Log the message if logging service is available
            if self.logging_service and caption:
                self.logging_service.log_chat_message(
                    chat_id=str(chat_id),
                    username="bot",
                    role="assistant",
                    message=f"[Document: {document_name}] {caption or ''}"
                )

            # Save to session history
            self.add_to_session(
                chat_id=chat_id,
                role="assistant",
                content=f"[Document: {document_name}] {caption or ''}",
                metadata={
                    "sent": True,
                    "document_path": document_path,
                    "document_name": document_name,
                    "mimetype": mimetype
                }
            )

            # Return a success response
            return {"status": "success", "message": "Document sent successfully"}

        except Exception as e:
            error_message = str(e)
            print(f"Error sending WhatsApp document: {error_message}")
            return {"error": error_message}

    def extract_message_data(self, update_json, save_to_session=True) -> Tuple[Optional[str], Optional[str], Optional[str], Optional[str]]:
        """
        Extract message data from a WhatsApp update

        Args:
            update_json (dict): WhatsApp update object
            save_to_session (bool): Whether to save the message to the session history

        Returns:
            tuple: (chat_id, user_id, message_text, username)
        """
        try:
            # Check if this is a valid WhatsApp message update
            if update_json.get('event') != 'message' or 'data' not in update_json:
                print("Not a valid WhatsApp message update")
                return None, None, None, None

            # Extract the data from the update
            data = update_json.get('data', {})

            # Extract message data
            chat_id = data.get('from')
            user_id = data.get('from')  # Use the phone number as the user ID
            push_name = data.get('pushName', 'Unknown User')

            # Extract the message text
            message_data = data.get('message', {})
            if message_data.get('type') == 'text':
                message_text = message_data.get('text', '')
            else:
                # Handle other message types if needed
                message_text = f"[{message_data.get('type', 'unknown')} message]"

            # Save to session history if enabled and message is not empty
            if save_to_session and message_text and chat_id:
                self.add_to_session(
                    chat_id=chat_id,
                    role="user",
                    content=message_text,
                    metadata={
                        "user_id": user_id,
                        "username": push_name,
                        "message_id": data.get('id')
                    }
                )

            # Log the message if logging service is available and message is not empty
            if self.logging_service and message_text and chat_id:
                self.logging_service.log_chat_message(
                    chat_id=str(chat_id),
                    username=push_name,
                    role="user",
                    message=message_text
                )

            return chat_id, user_id, message_text, push_name

        except Exception as e:
            print(f"Error extracting WhatsApp message data: {e}")
            return None, None, None, None

    def add_to_session(self, chat_id, role, content, metadata=None):
        """
        Add a message to the session history

        Args:
            chat_id (str): The chat ID to add the message to
            role (str): The role of the message sender ('user' or 'assistant')
            content (str): The message content
            metadata (dict, optional): Additional metadata for the message
        """
        chat_id = str(chat_id)

        # Initialize session if it doesn't exist
        if chat_id not in self.sessions:
            self.sessions[chat_id] = []

            # If we've reached the maximum number of sessions, remove the oldest one
            if len(self.sessions) > self.max_sessions:
                oldest_chat_id = next(iter(self.sessions))
                del self.sessions[oldest_chat_id]

        # Create message entry
        timestamp = datetime.datetime.now().isoformat()
        message = {
            "role": role,
            "content": content,
            "timestamp": timestamp
        }

        # Add metadata if provided
        if metadata:
            message["metadata"] = metadata

        # Add to session
        self.sessions[chat_id].append(message)

        # Trim session if it exceeds the maximum history size
        if len(self.sessions[chat_id]) > self.max_session_history:
            self.sessions[chat_id] = self.sessions[chat_id][-self.max_session_history:]

    def get_session(self, chat_id: str) -> List[Dict[str, Any]]:
        """
        Get the session history for a specific chat

        Args:
            chat_id (str): The chat ID to get the session for

        Returns:
            List[Dict]: The session history for the chat
        """
        chat_id = str(chat_id)
        return self.sessions.get(chat_id, [])

    def get_session_as_string(self, chat_id: str) -> str:
        """
        Get the session history for a specific chat as a formatted string

        Args:
            chat_id (str): The chat ID to get the session for

        Returns:
            str: The formatted session history
        """
        chat_id = str(chat_id)
        session = self.get_session(chat_id)

        if not session:
            return "No session history available."

        result = []
        for msg in session:
            timestamp = msg.get("timestamp", "")
            if timestamp:
                timestamp = f"[{timestamp.split('T')[0]} {timestamp.split('T')[1][:8]}] "

            result.append(f"{timestamp}{msg['role'].capitalize()}: {msg['content']}")

        return "\n".join(result)

    def clear_session(self, chat_id: str) -> bool:
        """
        Clear the session history for a specific chat

        Args:
            chat_id (str): The chat ID to clear the session for

        Returns:
            bool: True if the session was cleared, False if it didn't exist
        """
        chat_id = str(chat_id)
        if chat_id in self.sessions:
            self.sessions[chat_id] = []
            return True
        return False
