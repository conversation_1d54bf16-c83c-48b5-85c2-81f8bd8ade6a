import os
from typing import Dict, Any, List, Optional
from openai import <PERSON>ync<PERSON>penA<PERSON>
from src.services.rag_service import RAGService

class OpenAIService:
    """
    Service for handling OpenAI operations
    """

    def __init__(self, api_key: str, model: str = "gpt-4.1-nano", prompt_service=None):
        """
        Initialize the OpenAI service

        Args:
            api_key (str): OpenAI API key
            model (str): Model to use for completions
            prompt_service: Service for handling prompts
        """
        self.client = AsyncOpenAI(api_key=api_key)
        self.model = model
        self.rag_service = RAGService(api_key=api_key, model=model, prompt_service=prompt_service)

    async def analyze_message(
        self,
        message: str,
        conversation_history: Optional[List[Dict[str, Any]]] = None,
        session: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Analyze a message using RAG and function calling

        Args:
            message (str): User's message
            conversation_history (Optional[List[Dict[str, Any]]]): Conversation history
            session (Optional[Dict[str, Any]]): Session state

        Returns:
            Dict[str, Any]: Analysis result with response and function calls
        """
        # Define available functions
        functions = [
            {
                "name": "generate_order",
                "description": "Generate an order for a customer",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "items": {
                            "type": "array",
                            "description": "List of items to order",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "name": {
                                        "type": "string",
                                        "description": "Name of the item to order"
                                    },
                                    "quantity": {
                                        "type": "integer",
                                        "description": "Quantity of the item to order",
                                        "minimum": 1
                                    },
                                    "duration": {
                                        "type": "integer",
                                        "description": "Duration of rental in months (required for rental items)",
                                        "minimum": 1
                                    }
                                },
                                "required": ["name", "quantity"]
                            }
                        }
                    },
                    "required": ["items"]
                }
            },
            {
                "name": "get_tnc_pdf",
                "description": "Only use when only need to get and send the Terms and Conditions PDF, do not use this function for any other purpose",
                "parameters": {
                    "type": "object",
                    "properties": {}
                }
            },
            {
                "name": "show_menu",
                "description": "Show the menu",
                "parameters": {
                    "type": "object",
                    "properties": {}
                }
            },
            {
                "name": "confirm_order",
                "description": "Agreed to T&C and Confirm an order",
                "parameters": {
                    "type": "object",
                    "properties": {}
                }
            },
            {
                "name": "show_rental_equipment",
                "description": "Show available rental equipment and handle rental orders",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "equipment_types": {
                            "type": "array",
                            "description": "Types of equipment to show (e.g., generator, water_pump, compressor, etc.)",
                            "items": {
                                "type": "string"
                            }
                        },
                        "items": {
                            "type": "array",
                            "description": "List of items to rent",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "name": {
                                        "type": "string",
                                        "description": "Name of the equipment to rent"
                                    },
                                    "quantity": {
                                        "type": "integer",
                                        "description": "Quantity of the equipment to rent",
                                        "minimum": 1
                                    },
                                    "duration": {
                                        "type": "integer",
                                        "description": "Duration of rental in months",
                                        "minimum": 1
                                    }
                                },
                                "required": ["name", "quantity", "duration"]
                            }
                        }
                    },
                    "required": ["equipment_types"]
                }
            }
        ]

        # Get response using RAG
        response = await self.rag_service.generate_response(
            query=message,
            conversation_history=conversation_history,
            functions=functions,
            session=session
        )

        return response

    async def generate_response(
        self,
        query: str,
        conversation_history: Optional[List[Dict[str, Any]]] = None,
        menu_items: Optional[List[Dict[str, Any]]] = None,
        functions: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """
        Generate a response using RAG and function calling

        Args:
            query (str): User query
            conversation_history (Optional[List[Dict[str, Any]]]): Conversation history
            menu_items (Optional[List[Dict[str, Any]]]): Available menu items
            functions (Optional[List[Dict[str, Any]]]): Available functions

        Returns:
            Dict[str, Any]: Response with text and function calls
        """
        # Get response using RAG
        response = await self.rag_service.generate_response(
            query=query,
            conversation_history=conversation_history,
            menu_items=menu_items,
            functions=functions
        )

        return response


