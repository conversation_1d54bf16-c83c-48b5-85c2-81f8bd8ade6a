import json
import os
from typing import Dict, Any, List
from dotenv import load_dotenv
from src.services.order_service import OrderService

# Load environment variables
load_dotenv(".env.local")

class PromptService:
    """
    Service for handling system prompts and function calls
    """

    def __init__(self, order_service: OrderService):
        """
        Initialize the prompt service

        Args:
            order_service (OrderService): Service for handling orders
        """
        self.order_service = order_service
        self.prompt_path = os.getenv('SYSTEM_PROMPT_PATH', 'data/system_prompt.txt')
        self.last_modified_time = 0
        self.system_prompt = ""
        self._load_system_prompt()

    def _load_system_prompt(self) -> None:
        """
        Load the system prompt from a file or use the default

        This method loads the system prompt and stores it in the instance variable.
        It doesn't return anything.
        """
        # Check if we already have a prompt loaded and the file hasn't been modified
        if self.system_prompt and os.path.exists(self.prompt_path):
            current_modified_time = os.path.getmtime(self.prompt_path)
            if current_modified_time <= self.last_modified_time:
                # File hasn't been modified, use the cached prompt
                return

        # Try to load the prompt from the file
        if os.path.exists(self.prompt_path):
            try:
                print(f"Loading system prompt from {self.prompt_path}")
                with open(self.prompt_path, 'r', encoding='utf-8') as file:
                    self.system_prompt = file.read()

                # Update the last modified time
                self.last_modified_time = os.path.getmtime(self.prompt_path)
                return
            except Exception as e:
                error_msg = f"Error loading system prompt from {self.prompt_path}: {e}"
                print(error_msg)
                # Don't overwrite an existing prompt if we have one
                if not self.system_prompt:
                    self.system_prompt = f"ERROR: {error_msg}\n\nUsing default prompt instead.\n\n"
        else:
            error_msg = f"System prompt file not found at {self.prompt_path}"
            print(error_msg)
            # Don't overwrite an existing prompt if we have one
            if not self.system_prompt:
                self.system_prompt = f"ERROR: {error_msg}\n\nUsing default prompt instead.\n\n"

        # Only set default if we don't already have a prompt loaded
        if not self.system_prompt or self.system_prompt.startswith("ERROR:"):
            print("Using default system prompt")
            self.system_prompt = """
As a sales executive at TTL Sales & Rental, your responsibilities include handling customer inquiries and accepting orders or rental requests.

Product Information:
{menu_items}

Steps:
1. Understand Customer Inquiry: Read the customer's question or requirements carefully. Identify if they need product details, or are ready to purchase or rent.
2. Provide Product Details: Upon request for the product, list all product items including sales or rental prices without recommendations.
3. Gather Mandatory Information: Company name, contact person, contact number, email address, and application industry.
4. Gather Additional Information: If further details are required, like renting duration, buying/renting number of units, equipment's country of origin, technical specifications, deposit required, terms and conditions of rental, application and etc
5. Order Processing: If a customer wants to place an order, verify item and order quantity availability. If the stock is sufficient, create an order summary and ask the customer to confirm their order with a clear yes/no question.
6. Order Confirmation and Terms & Conditions Flow:
   - Always confirm with customer whether they want to purchase or rent and show correct price accordingly.
   - First, create and show a detailed order summary with all items, quantities, and the total price.
   - IMMEDIATELY after showing the order summary, call get_tnc_pdf function to retrieve the Terms and Conditions PDF from data/tnc.pdf
   - The system will send the Terms and Conditions PDF document to the user with the message: "Please review our Terms and Conditions before proceeding with your order. After reviewing, please reply with 'I agree' to confirm your order or 'cancel' to cancel it."
   - After the user agrees to the Terms and Conditions (by replying with "I agree" or similar), ask them to confirm their order with a clear yes/no question.
   - ONLY after the user has agreed to the Terms and Conditions AND confirmed their order, call the generate_order function to record the order in the CSV file.
   - If the customer cancels or declines their order at any point, acknowledge this and DO NOT call the generate_order function.
   - If the function call returns any response other than "success," or if it returns "fail," inform the customer of the issue and suggest trying again or picking another item.
7. Order Status Communication:
   - For successful orders: Clearly state "Your order has been successfully placed" or "Your order has been confirmed and recorded" so the customer knows their order was processed.
   - For unsuccessful or cancelled orders: Clearly state "Your order was not placed" or "Your order has been cancelled" so the customer understands no order was recorded.
8. Post-Order Conversation: After the customer confirms or cancels their order, ALWAYS continue the conversation naturally and WAIT for their response. Do not end the conversation abruptly.
   - For confirmed orders: Provide estimated preparation time (1 - 2 days), offer information about pickup or delivery options, and ask if they would like anything else.
   - For cancelled orders: Acknowledge the cancellation politely and ask if they would like to order something else or if they have any other questions.
9. Offer Additional Assistance: Inform the customer about special offers or additional services that might enhance their buying experience.
10. Ensure Customer Satisfaction: Make sure the customer is satisfied with their inquiries or orders and offer further assistance if necessary.

Output Format:
A casual, friendly yet professional response formatted as a short paragraph. Include full product details if the customer asks for the product or proceed with order confirmation if placing an order.

Examples:
Example 1:
Customer Inquiry: "I'd like to rent the Atlas Copco Air Compressor Zone 2 185CFM (102psig), please."
Response: "Great choice! The Atlas Copco Air Compressor Zone 2 185CFM (102psig) rental is RM220.00/month. Here's your order summary:

• 1x Atlas Copco Air Compressor Zone 2 185CFM (102psig) - Rental: RM220.00/month

Total: RM220.00/month"

Action: Call get_tnc_pdf()
Response: [System sends Terms and Conditions PDF document from data/tnc.pdf]
"Please review our Terms and Conditions before proceeding with your order. After reviewing, please reply with 'I agree' to confirm your order or 'cancel' to cancel it."

Customer Response: "I agree"
Response: "Thank you for agreeing to our Terms and Conditions. Would you like to confirm this order? Please reply with 'yes' to confirm or 'no' to cancel."

Customer Response: "Yes"
Action: Call generate_order("Atlas Copco Air Compressor Zone 2 185CFM (102psig)", 1)
After successful order: "Your order has been successfully placed! The receipts will be sent to your email soon"

Notes:
- Maintain a friendly and natural tone throughout all interactions.
- Use the product details for accurate inquiries and order processing.
- Try to talk in a simpler way, avoiding complex language.
- Provide comprehensive menu details upon request.
- IMPORTANT: ALWAYS show a detailed order summary FIRST, then IMMEDIATELY show Terms and Conditions.
- Call the get_tnc_pdf function IMMEDIATELY after showing the order summary, NOT after the customer confirms.
- Ask the customer to agree to the Terms and Conditions BEFORE asking them to confirm their order.
- ONLY call the generate_order function AFTER the customer has BOTH agreed to the Terms and Conditions AND confirmed their order.
- NEVER record an order to CSV if the customer hasn't agreed to Terms and Conditions or hasn't confirmed or has cancelled their order.
- ALWAYS continue the conversation naturally after an order is confirmed or cancelled - don't end the interaction.
- ONLY call get_tnc_pdf() ONCE when showing the order summary
"""

    def get_formatted_prompt(self) -> str:
        """
        Get the system prompt with menu items inserted

        This method reloads the system prompt from the file if it has been modified,
        then formats it with the current menu items.

        Returns:
            str: Formatted system prompt
        """
        # Reload the system prompt if the file has been modified
        self._load_system_prompt()

        # Get menu items as a formatted string
        menu_text = self._format_menu_items()

        # Replace placeholder with actual menu items
        return self.system_prompt.replace("{menu_items}", menu_text)

    def _format_menu_items(self) -> str:
        """
        Format menu items for insertion into the system prompt

        Returns:
            str: Formatted menu items text
        """
        menu_items = self.order_service.get_menu_items()

        if not menu_items:
            return "Menu currently unavailable."

        menu_text = ""
        for item in menu_items:
            menu_text += f"{item['category']} | {item['name']}: {item['description']} | Price: RM{item['price']:.2f}\n"

        return menu_text

    def handle_function_call(self, function_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle function calls from the AI

        Args:
            function_name (str): Name of the function to call
            arguments (Dict): Arguments for the function

        Returns:
            Dict: Result of the function call
        """
        if function_name == "generate_order":
            # Extract arguments
            item_name = arguments.get("item_name", "")
            item_quantity = int(arguments.get("item_quantity", 0))

            # Call the order service
            return self.order_service.generate_order(item_name, item_quantity)

        elif function_name == "get_tnc_pdf":
            # Return the Terms and Conditions PDF
            return self.get_tnc_pdf()

        return {"status": "fail", "message": f"Unknown function: {function_name}"}

    def get_tnc_pdf(self) -> Dict[str, Any]:
        """
        Get the Terms and Conditions PDF file

        Returns:
            Dict: Result with the PDF file path or URL and status
        """
        # Get the S3 URL from environment variables
        s3_url = os.getenv('TNC_PDF_URL', 'https://ai-chatbot-asset.s3.ap-southeast-1.amazonaws.com/tnc.pdf')

        print(f"Using Terms and Conditions PDF from S3 URL: {s3_url}")

        # Return the S3 URL
        return {
            "status": "success",
            "type": "document",
            "path": s3_url,
            "caption": "Please review our Terms and Conditions before proceeding with your order. After reviewing, please reply with 'I agree' to confirm your order or 'cancel' to cancel it."
        }

    def reload_system_prompt(self, force: bool = False) -> dict:
        """
        Force reload the system prompt from the file

        Args:
            force (bool): If True, reload even if the file hasn't been modified

        Returns:
            dict: Result with status, message, and whether the prompt was reloaded
        """
        if force:
            # Reset the last modified time to force a reload
            self.last_modified_time = 0

        # Store the old prompt for comparison
        old_prompt = self.system_prompt

        # Check if the file exists before attempting to load
        if not os.path.exists(self.prompt_path):
            error_msg = f"System prompt file not found at {self.prompt_path}"
            print(error_msg)
            return {
                "status": "error",
                "message": error_msg,
                "reloaded": False
            }

        # Try to load the system prompt
        try:
            self._load_system_prompt()

            # Check if the prompt was actually reloaded
            if old_prompt != self.system_prompt:
                return {
                    "status": "success",
                    "message": f"System prompt successfully reloaded from {self.prompt_path}",
                    "reloaded": True
                }
            else:
                return {
                    "status": "success",
                    "message": "System prompt unchanged",
                    "reloaded": False
                }
        except Exception as e:
            error_msg = f"Error reloading system prompt: {e}"
            print(error_msg)
            return {
                "status": "error",
                "message": error_msg,
                "reloaded": False
            }

    def get_function_definitions(self) -> List[Dict[str, Any]]:
        """
        Get the function definitions for the AI

        Returns:
            List[Dict]: Function definitions
        """
        return [
            {
                "name": "generate_order",
                "description": "Generates an order with item name and quantity",
                "parameters": {
                    "type": "object",
                    "required": ["item_name", "item_quantity"],
                    "properties": {
                        "item_name": {
                            "type": "string",
                            "description": "Name of the item to be ordered"
                        },
                        "item_quantity": {
                            "type": "number",
                            "description": "Quantity of the item to be ordered"
                        }
                    }
                }
            },
            {
                "name": "get_tnc_pdf",
                "description": "Retrieves the Terms and Conditions PDF document to show to the customer",
                "parameters": {
                    "type": "object",
                    "properties": {}  # No parameters needed
                }
            }
        ]
