from twilio.rest import Client
from twilio.twiml.messaging_response import MessagingResponse
import re

class TwilioService:
    """
    Service for interacting with Twilio API for WhatsApp messaging
    """

    def __init__(self, account_sid, auth_token, phone_number):
        """
        Initialize the Twilio service

        Args:
            account_sid (str): Twilio account SID
            auth_token (str): Twilio auth token
            phone_number (str): Twilio WhatsApp phone number
        """
        self.client = Client(account_sid, auth_token)
        self.phone_number = phone_number

    def clean_phone_number(self, phone_number):
        """
        Clean a phone number by removing the 'whatsapp:' prefix if present

        Args:
            phone_number (str): Phone number to clean

        Returns:
            str: Cleaned phone number
        """
        return re.sub(r'^whatsapp:', '', phone_number)

    def format_whatsapp_number(self, phone_number):
        """
        Format a phone number for WhatsApp by adding the 'whatsapp:' prefix if not present

        Args:
            phone_number (str): Phone number to format

        Returns:
            str: Formatted WhatsApp phone number
        """
        if not phone_number:
            # Return a default number if phone_number is empty
            return "whatsapp:+**********"

        # Clean the phone number
        phone_number = self.clean_phone_number(phone_number)

        # Ensure the phone number has a '+' prefix
        if not phone_number.startswith('+'):
            # Add '+' if missing
            if phone_number.isdigit():
                phone_number = f"+{phone_number}"

        return f"whatsapp:{phone_number}"

    def send_message(self, to_phone, message_body):
        """
        Send a WhatsApp message to a customer

        Args:
            to_phone (str): Customer's phone number
            message_body (str): Message content to send

        Returns:
            dict: Response from Twilio API
        """
        # Format the phone number for WhatsApp
        formatted_to = self.format_whatsapp_number(to_phone)
        formatted_from = self.format_whatsapp_number(self.phone_number)

        message = self.client.messages.create(
            from_=formatted_from,
            body=message_body,
            to=formatted_to
        )

        return {
            "sid": message.sid,
            "status": message.status,
            "to": message.to,
            "body": message.body
        }

    def create_twiml_response(self, message_body):
        """
        Create a TwiML response for a WhatsApp message

        Args:
            message_body (str): Message content to send

        Returns:
            str: TwiML response as a string
        """
        # For Twilio Conversations API, we can just return a simple acknowledgment
        # The actual response is sent via the send_message method

        # Create a standard TwiML response for SMS/WhatsApp API
        response = MessagingResponse()
        response.message(message_body)
        return str(response)
