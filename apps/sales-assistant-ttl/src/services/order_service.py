import os
import csv
import datetime
from typing import Dict, List, Any, Optional
from src.services.logging_service import LoggingService

class OrderService:
    """
    Service for handling orders and menu operations
    """

    def __init__(self, menu_file_path: str = 'data/menu.csv', orders_file_path: str = 'data/orders.csv',
                 logging_service: Optional[LoggingService] = None):
        """
        Initialize the order service

        Args:
            menu_file_path (str): Path to the menu CSV file
            orders_file_path (str): Path to the orders CSV file
            logging_service (LoggingService, optional): Service for logging orders
        """
        self.menu_file_path = menu_file_path
        self.orders_file_path = orders_file_path
        self.logging_service = logging_service

        # Create orders file if it doesn't exist
        if not os.path.exists(orders_file_path):
            self._create_orders_file()

    def _create_orders_file(self):
        """Create the orders CSV file with headers"""
        os.makedirs(os.path.dirname(self.orders_file_path), exist_ok=True)
        with open(self.orders_file_path, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerow(['OrderID', 'CustomerID', 'ItemName', 'Quantity', 'Price', 'TotalAmount', 'OrderDate'])

    def get_menu_items(self) -> List[Dict[str, Any]]:
        """
        Get all menu items

        Returns:
            List[Dict]: List of menu items
        """
        menu_items = []

        try:
            with open(self.menu_file_path, 'r', newline='') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    # Convert price to float and stock to int
                    row['price'] = float(row['Price'])
                    row['stock'] = int(row['Stock'])
                    row['name'] = row['Name']
                    row['category'] = row['Category']
                    row['description'] = row['Description']
                    row['available'] = row['Available'].upper() == 'TRUE'
                    menu_items.append(row)
        except Exception as e:
            print(f"Error reading menu file: {e}")

        return menu_items

    def generate_order(self, items: List[Dict[str, Any]], customer_id: str = "unknown") -> Dict[str, Any]:
        """
        Generate an order for multiple menu items

        Args:
            items (List[Dict[str, Any]]): List of items to order, each containing:
                - name (str): Name of the item
                - quantity (int): Quantity of the item
            customer_id (str): Customer identifier

        Returns:
            Dict: Order result with status and details
        """
        print(f"\n===== PROCESSING ORDER =====\nItems: {items}\nCustomer ID: {customer_id}\n")

        # Validate inputs
        if not items:
            print("Order validation failed: No items provided")
            return {"status": "fail", "message": "No items provided"}

        # Get menu items
        menu_items = self.get_menu_items()
        order_items = []
        total_amount = 0

        # Process each item
        for item in items:
            item_name = item['name']
            item_quantity = item['quantity']

            if not item_name or item_quantity <= 0:
                print(f"Order validation failed: Invalid item name or quantity for {item_name}")
                return {"status": "fail", "message": f"Invalid item name or quantity for {item_name}"}

            # Find the requested item
            menu_item = next((m for m in menu_items if m['name'].lower() == item_name.lower()), None)

            if not menu_item:
                print(f"Order failed: Item '{item_name}' not found in menu")
                return {"status": "fail", "message": f"Item '{item_name}' not found in menu"}

            # Check stock
            if menu_item['stock'] < item_quantity:
                print(f"Order failed: Not enough stock for {item_name}. Available: {menu_item['stock']}, Requested: {item_quantity}")
                return {"status": "fail", "message": f"Not enough stock for {item_name}. Available: {menu_item['stock']}, Requested: {item_quantity}"}

            # Calculate item total
            item_total = menu_item['price'] * item_quantity
            total_amount += item_total

            # Add to order items
            order_items.append({
                'name': menu_item['name'],
                'quantity': item_quantity,
                'price': menu_item['price'],
                'total': item_total
            })

            # Update stock
            self._update_item_stock(menu_item['name'], menu_item['stock'] - item_quantity)

        # Generate order ID
        order_id = f"ORD-{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"

        # Save order to CSV
        try:
            # Format items string
            items_str = ", ".join([
                f"{item['quantity']}x {item['name']} (RM{item['total']:.2f})"
                for item in order_items
            ])

            with open(self.orders_file_path, 'a', newline='') as file:
                writer = csv.writer(file)
                writer.writerow([
                    order_id,
                    customer_id,
                    items_str,
                    total_amount,
                    datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                ])

            # Create the result
            result = {
                "status": "success",
                "order_id": order_id,
                "items": order_items,
                "total_amount": total_amount,
                "total_formatted": f"RM{total_amount:.2f}",
                "estimated_time": "(1 - 2 days)",
                "message": f"Your order {order_id} has successfully been placed. Please wait for it to be processed. Thank you for choosing us!"
            }

            print(f"Order successfully created:\nOrder ID: {order_id}\nItems: {items_str}\nTotal: RM{total_amount:.2f}\n=======================\n")

            # Log the order if logging service is available
            if self.logging_service:
                self.logging_service.log_order(result)

            return result

        except Exception as e:
            print(f"Error generating order: {e}")
            return {"status": "fail", "message": f"Error generating order: {e}"}

    def _update_item_stock(self, item_name: str, new_stock: int) -> bool:
        """
        Update the stock of an item in the menu

        Args:
            item_name (str): Name of the item to update
            new_stock (int): New stock value

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Read all rows
            rows = []
            with open(self.menu_file_path, 'r', newline='') as file:
                reader = csv.reader(file)
                header = next(reader)  # Save header
                rows.append(header)

                for row in reader:
                    if row and row[0].lower() == item_name.lower():
                        row[5] = str(new_stock)  # Update stock
                    rows.append(row)

            # Write back all rows
            with open(self.menu_file_path, 'w', newline='') as file:
                writer = csv.writer(file)
                writer.writerows(rows)

            return True

        except Exception as e:
            print(f"Error updating item stock: {e}")
            return False

    def format_menu_for_display(self) -> str:
        """
        Format the menu for display in a message

        Returns:
            str: Formatted menu text
        """
        menu_items = self.get_menu_items()

        if not menu_items:
            return "Sorry, the menu is currently unavailable."

        # Group by category
        categories = {}
        for item in menu_items:
            category = item['category']
            if category not in categories:
                categories[category] = []
            categories[category].append(item)

        # Format the menu
        menu_text = "📋 *Our Products* 📋\n\n"

        for category, items in categories.items():
            menu_text += f"*{category}*\n"
            for item in items:
                menu_text += f"• {item['name']} - RM{item['price']:.2f} (Stock: {item['stock']})\n"
                menu_text += f"  {item['description']}\n\n"

            menu_text += "\n"

        menu_text += "To order, simply tell me what you'd like!"

        return menu_text
