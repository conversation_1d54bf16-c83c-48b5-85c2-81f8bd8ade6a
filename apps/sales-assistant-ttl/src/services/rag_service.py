import os
import json
import csv
from typing import Dict, Any, List, Optional
from openai import Async<PERSON>penAI
from src.services.prompt_service import PromptService

class RAGService:
    """
    Service for implementing Retrieval-Augmented Generation (RAG) using OpenAI's API
    """

    def __init__(self, api_key: str, model: str = "gpt-4.1-nano", prompt_service: Optional[PromptService] = None):
        """
        Initialize the RAG service

        Args:
            api_key (str): OpenAI API key
            model (str): Model to use for completions
            prompt_service (Optional[PromptService]): Service for handling prompts
        """
        self.client = AsyncOpenAI(api_key=api_key)
        self.model = model
        self.prompt_service = prompt_service
        self.menu_items = self._load_menu_items()

    def _load_menu_items(self) -> List[Dict[str, Any]]:
        """
        Load menu items from menu.csv

        Returns:
            List[Dict[str, Any]]: List of menu items
        """
        menu_items = []
        csv_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'menu.csv')
        
        try:
            with open(csv_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    menu_items.append({
                        'name': row['Name'],
                        'category': row['Category'],
                        'description': row['Description'],
                        'price': float(row['Price']),
                        'available': row['Available'].lower() == 'true',
                        'stock': int(row['Stock'])
                    })
        except Exception as e:
            print(f"Error loading menu items: {e}")
            return []

        return menu_items

    def _extract_flow_steps(self, system_prompt: str) -> Dict[str, List[str]]:
        """
        Extract flow steps from system prompt

        Args:
            system_prompt (str): System prompt text

        Returns:
            Dict[str, List[str]]: Dictionary of flow steps by type
        """
        flows = {
            'order': [],
            'rental': [],
            'general': []
        }
        
        try:
            # Extract steps section
            if 'Steps:' not in system_prompt or 'Output Format:' not in system_prompt:
                return flows
                
            steps_section = system_prompt.split('Steps:')[1].split('Output Format:')[0]
            
            # Parse steps
            current_flow = 'general'
            for line in steps_section.split('\n'):
                line = line.strip()
                if not line:
                    continue
                    
                # Check for flow type
                if 'Order Processing' in line:
                    current_flow = 'order'
                elif 'Rental' in line:
                    current_flow = 'rental'
                elif line.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.', '10.')):
                    flows[current_flow].append(line)
                elif line.startswith('   -'):
                    flows[current_flow].append(line.strip())
        except Exception as e:
            print(f"Error extracting flow steps: {e}")
            # Return default flows if extraction fails
            flows['general'] = [
                "1. Understand Customer Inquiry",
                "2. Provide Product Details",
                "3. Gather Required Information",
                "4. Process Request",
                "5. Confirm Details"
            ]
            
        return flows

    def _get_flow_context(self, query: str, flows: Dict[str, List[str]]) -> str:
        """
        Get relevant flow context based on query

        Args:
            query (str): User query
            flows (Dict[str, List[str]]): Flow steps by type

        Returns:
            str: Relevant flow context
        """
        try:
            # Determine flow type from query
            flow_type = 'general'
            query_lower = query.lower()
            
            if any(word in query_lower for word in ['order', 'buy', 'purchase']):
                flow_type = 'order'
            elif any(word in query_lower for word in ['rent', 'rental', 'lease']):
                flow_type = 'rental'
                
            # Get relevant flow steps
            relevant_steps = flows.get(flow_type, flows['general'])
            
            # Format flow context
            flow_context = f"Relevant flow steps for {flow_type}:\n"
            for step in relevant_steps:
                flow_context += f"- {step}\n"
                
            return flow_context
        except Exception as e:
            print(f"Error getting flow context: {e}")
            return "Using general flow steps for handling the request."

    async def get_relevant_context(
        self,
        query: str,
        menu_items: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """
        Get relevant context for the query
        """
        try:
            # Use provided menu items or loaded ones
            items = menu_items if menu_items is not None else self.menu_items
            
            # Get the system prompt
            system_prompt = self.prompt_service.get_formatted_prompt() if self.prompt_service else ""
            
            # Create the prompt for context extraction
            prompt = f"""
                Given the following system prompt and user query, identify ALL relevant information and determine the appropriate flow:

                System Prompt:
                {system_prompt}

                Menu Items:
                {json.dumps(items, indent=2)}

                User Query: {query}

                Please analyze the query and:
                1. Identify the type of request (rental, purchase, inquiry, etc.)
                2. Extract ALL relevant equipment types and details mentioned in the query
                3. For each equipment type mentioned:
                   - List all available items in that category
                   - Include complete specifications and pricing
                   - Note any specific requirements (e.g., duration, quantity)
                   - IMPORTANT: Extract exact quantities mentioned (e.g., "2 water pumps" = quantity: 2)
                4. Group items by their categories for clarity
                5. Include any special requirements or considerations

                Important:
                - If multiple equipment types are mentioned, include ALL of them
                - Do not skip any equipment types mentioned in the query
                - Ensure all specifications and pricing are included for each item
                - Group similar items together by category
                - Extract exact quantities mentioned in the query
                - If no quantity is mentioned, default to 1
                - Do not duplicate items or quantities

                Return the information in a structured format that includes:
                - The type of request
                - ALL equipment types mentioned
                - Complete details for each item in each category
                - Exact quantities mentioned for each item
                - Any special requirements or considerations
                """

            # Get completion from OpenAI
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a helpful assistant that analyzes queries and extracts ALL relevant information using the system prompt as a guide. You must include ALL equipment types mentioned in the query and their exact quantities."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=1000
            )

            return response.choices[0].message.content
        except Exception as e:
            print(f"Error in get_relevant_context: {e}")
            return ""

    async def generate_response(
        self,
        query: str,
        conversation_history: Optional[List[Dict[str, Any]]] = None,
        menu_items: Optional[List[Dict[str, Any]]] = None,
        functions: Optional[List[Dict[str, Any]]] = None,
        session: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate a response using RAG and function calling
        """
        try:
            if conversation_history is None:
                conversation_history = []

            # Use provided menu items or loaded ones
            items = menu_items if menu_items is not None else self.menu_items

            # Get relevant context
            context = await self.get_relevant_context(query, items)

            # Get the system prompt with menu items
            system_prompt = self.prompt_service.get_formatted_prompt() if self.prompt_service else ""

            # Add session state to context if available
            session_state = ""
            if session:
                if session.get('tnc_shown') and not session.get('tnc_agreed'):
                    session_state = "Waiting for T&C agreement"
                elif session.get('tnc_agreed') and not session.get('order_confirmed'):
                    session_state = "Waiting for final order confirmation"
                elif session.get('order_confirmed'):
                    session_state = "Order completed"

            # Prepare messages for chat completion
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "system", "content": f"Here is the relevant context and flow for the current query:\n{context}"},
                {"role": "system", "content": f"Current session state: {session_state}"},
                {"role": "system", "content": "IMPORTANT: When handling rental equipment requests, you MUST include ALL equipment types mentioned in the query. For example, if the user asks about both generators and water pumps, you must show both types of equipment."},
                {"role": "system", "content": "CRITICAL: After showing order summary, you MUST ALWAYS call get_tnc_pdf function to show Terms and Conditions. The order summary should be followed immediately by the T&C document."}
            ]

            # Add conversation history
            for msg in conversation_history:
                role = "assistant" if not msg.get('is_customer', True) else "user"
                messages.append({"role": role, "content": msg['message']})

            # Add current query
            messages.append({"role": "user", "content": query})

            # Get completion from OpenAI
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                functions=functions,
                function_call="auto",
                temperature=0.7,
                max_tokens=1000
            )

            # Process the response
            message = response.choices[0].message
            response_text = message.content or ""

            # Check for function calls
            if message.function_call:
                function_call = {
                    "name": message.function_call.name,
                    "arguments": json.loads(message.function_call.arguments)
                }

                # If showing order summary, ensure T&C is shown next
                if session.get('tnc_shown'):
                    return {
                        "response": response_text,
                        "function_call": {
                            "name": "get_tnc_pdf",
                            "arguments": {},
                            "result": {
                                "path": "data/tnc.pdf",
                                "caption": "Please review our Terms and Conditions before proceeding. Reply with 'I agree' to confirm or 'cancel' to cancel."
                            }
                        }
                    }

                # For show_rental_equipment function, ensure all equipment types are included
                if function_call["name"] == "show_rental_equipment":
                    # Extract equipment types from the query
                    query_lower = query.lower()
                    equipment_types = []
                    
                    # Check for common equipment types
                    if "generator" in query_lower or "gen" in query_lower:
                        equipment_types.append("generator")
                    if "water pump" in query_lower or "pump" in query_lower:
                        equipment_types.append("water_pump")
                    if "compressor" in query_lower:
                        equipment_types.append("compressor")
                    if "scaffold" in query_lower:
                        equipment_types.append("scaffold")
                    
                    # Update the function call arguments to include all equipment types
                    if equipment_types:
                        function_call["arguments"]["equipment_types"] = equipment_types

                return {
                    "response": response_text,
                    "function_call": function_call
                }

            return {"response": response_text}
        except Exception as e:
            print(f"Error in generate_response: {e}")
            return {"response": ""}