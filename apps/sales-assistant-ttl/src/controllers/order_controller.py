import json
import datetime
import os
from dotenv import load_dotenv
from typing import Dict, Any, Optional, List, Union
from src.services.whatsapp_service import WhatsAppService
from src.services.telegram_service import TelegramService
from src.services.openai_service import OpenAIService
from src.services.csv_service import CSVService
from src.services.order_service import OrderService

# Load environment variables
load_dotenv(".env.local")

class OrderController:
    """
    Controller for handling customer orders and interactions using RAG and function calling
    """

    def __init__(
        self,
        messaging_service: Union[WhatsAppService, TelegramService],
        openai_service: OpenAIService,
        sheets_service: CSVService,
        order_service: OrderService,
        tnc_pdf_url: str
    ):
        """
        Initialize the order controller

        Args:
            messaging_service (Union[WhatsAppService, TelegramService]): Service for handling messaging
            openai_service (OpenAIService): Service for handling OpenAI interactions
            sheets_service (CSVService): Service for handling CSV operations
            order_service (OrderService): Service for handling order operations
            tnc_pdf_url (str): URL to the Terms and Conditions PDF
        """
        self.messaging_service = messaging_service
        self.openai_service = openai_service
        self.sheets_service = sheets_service
        self.order_service = order_service
        self.tnc_pdf_url = tnc_pdf_url
        self.sessions = {}

    def _get_or_create_session(self, user_id: str) -> Dict[str, Any]:
        """
        Get or create a session for a user

        Args:
            user_id (str): User ID

        Returns:
            Dict[str, Any]: User session
        """
        if user_id not in self.sessions:
            self.sessions[user_id] = {
                'history': [],
                'tnc_shown': False,
                'tnc_agreed': False,
                'order_confirmed': False,
                'current_order': []  # Add this to track current order items
            }
        return self.sessions[user_id]

    async def process_message(self, message: str, user_id: str) -> Dict[str, Any]:
        """
        Process a message from a user using RAG and function calling

        Args:
            message (str): User's message
            user_id (str): User ID

        Returns:
            Dict[str, Any]: Response to send to the user
        """
        # Get or create session
        session = self._get_or_create_session(user_id)

        # Add message to history
        session['history'].append({
            'is_customer': True,
            'message': message
        })

        # Process message with OpenAI using RAG
        response = await self.openai_service.analyze_message(
            message=message,
            conversation_history=session['history'],
            session=session  # Pass session to OpenAI service
        )

        # Add response to history
        session['history'].append({
            'is_customer': False,
            'message': response.get('response', '')
        })

        # Handle function calls
        if 'function_call' in response:
            function_name = response['function_call']['name']
            arguments = response['function_call']['arguments']

            if function_name == 'generate_order':
                print("Generating order")
                # Check if T&C has been shown and agreed to
                if not session.get('tnc_shown'):
                    # Get order summary from the response
                    order_summary = response.get('response', '')
                    
                    # Update current order with new items
                    order_items = arguments.get('items', [])
                    if order_items:
                        # Process each item
                        processed_items = []
                        for item in order_items:
                            # Find the item in the menu
                            menu_item = next(
                                (m for m in self.openai_service.rag_service.menu_items 
                                 if m['name'].lower() == item['name'].lower()),
                                None
                            )
                            
                            if menu_item:
                                # Calculate total price based on quantity
                                quantity = item.get('quantity', 1)
                                total_price = menu_item['price'] * quantity
                                
                                processed_items.append({
                                    'name': menu_item['name'],
                                    'quantity': quantity,
                                    'price': menu_item['price'],
                                    'total': total_price
                                })
                        
                        # Update session's current order
                        session['current_order'] = processed_items

                    # Format the order summary with current items
                    formatted_summary = "📋 *Current Order Summary*\n\n"
                    total_amount = 0

                    for item in session['current_order']:
                        formatted_summary += (
                            f"• {item['quantity']}x {item['name']}\n"
                            f"  Price: RM{item['price']:.2f} each\n"
                            f"  Total: RM{item['total']:.2f}\n\n"
                        )
                        total_amount += item['total']

                    formatted_summary += f"*Total Amount: RM{total_amount:.2f}*\n\n"
                    # formatted_summary += "Please review and agree the Terms and Conditions to confirm your order."
                    
                    # # First send the order summary
                    # await self.messaging_service.send_message(
                    #     chat_id=user_id,
                    #     message_body=formatted_summary
                    # )

                    # session['tnc_shown'] = True
                    
                    # # Then send T&C
                    # return {
                    #     'response': '',
                    #     'function_call': {
                    #         'name': 'get_tnc_pdf',
                    #         'arguments': {},
                    #         'result': {
                    #             'path': self.tnc_pdf_url,
                    #             'caption': 'Please review the Terms and Conditions before proceeding. Reply with ''I agree'' to confirm or ''cancel'' to cancel.'
                    #         }
                    #     }
                    # }

                # Generate order
                order_items = arguments.get('items', [])
                if not order_items:
                    return {
                        'response': "I couldn't find any items to order. Please specify what you'd like to order."
                    }

                # Process each item
                processed_items = []
                for item in order_items:
                    # Find the item in the menu
                    menu_item = next(
                        (m for m in self.openai_service.rag_service.menu_items 
                         if m['name'].lower() == item['name'].lower()),
                        None
                    )
                    
                    if menu_item:
                        # Calculate total price based on quantity and duration (if rental)
                        quantity = item.get('quantity', 1)
                        duration = item.get('duration', 1)  # Default to 1 month for rentals
                        
                        # For rental items, multiply by duration
                        if 'rent' in message.lower() or 'rental' in message.lower():
                            total_price = menu_item['price'] * quantity * duration
                        else:
                            total_price = menu_item['price'] * quantity
                        
                        processed_items.append({
                            'name': menu_item['name'],
                            'quantity': quantity,
                            'price': menu_item['price'],
                            'total': total_price,
                            'duration': duration if 'rent' in message.lower() or 'rental' in message.lower() else None
                        })

                if not processed_items:
                    return {
                        'response': "I couldn't find any of the requested items in our inventory. Please check the item names and try again."
                    }

                # Generate the order
                order_result = self.order_service.generate_order(
                    items=processed_items,
                    customer_id=user_id
                )

                if order_result.get('status') == 'success':
                    session['order_confirmed'] = True
                    return {
                        'response': order_result.get('message', ''),
                        'function_call': {
                            'name': function_name,
                            'arguments': {'items': processed_items},
                            'result': order_result
                        }
                    }
                else:
                    return {
                        'response': order_result.get('message', '')
                    }

            elif function_name == 'show_rental_equipment':
                print("Showing rental equipment")
                equipment_types = arguments.get('equipment_types', [])
                rental_items = arguments.get('items', [])
                
                # Normalize equipment types for matching
                normalized_types = [et.lower().replace('_', ' ') for et in equipment_types]
                
                # Filter menu items by categories
                equipment_items = [
                    item for item in self.openai_service.rag_service.menu_items 
                    if item['category'].lower() in normalized_types
                ]

                if equipment_items:
                    # Group items by category
                    categories = {}
                    for item in equipment_items:
                        category = item['category']
                        if category not in categories:
                            categories[category] = []
                        categories[category].append(item)

                    # Format response with all equipment types
                    response = "📋 *Available Rental Equipment*\n\n"
                    
                    for category, items in categories.items():
                        response += f"*{category.title()}*\n"
                        for item in items:
                            response += (
                                f"• {item['name']}\n"
                                f"  {item['description']}\n"
                                f"  Monthly Rate: RM{item['price']:.2f}\n"
                                f"  Available: {'Yes' if item['available'] else 'No'}\n"
                                f"  Stock: {item['stock']}\n\n"
                            )
                        response += "\n"
                    
                    # If rental items are provided, process the rental order
                    if rental_items:
                        # Check if T&C has been shown and agreed to
                        if not session.get('tnc_shown'):
                            # Calculate rental summary
                            rental_summary = "📋 *Rental Order Summary*\n\n"
                            total_amount = 0
                            
                            # Group rental items by category for better organization
                            rental_by_category = {}
                            processed_items = set()  # Track processed items to prevent duplicates
                            
                            for rental in rental_items:
                                # Find the equipment in the menu
                                equipment = next((item for item in equipment_items if item['name'].lower() == rental['name'].lower()), None)
                                if equipment:
                                    # Skip if this item has already been processed
                                    if equipment['name'].lower() in processed_items:
                                        continue
                                        
                                    processed_items.add(equipment['name'].lower())
                                    category = equipment['category']
                                    if category not in rental_by_category:
                                        rental_by_category[category] = []
                                    rental_by_category[category].append((equipment, rental))
                            
                            # Format rental summary by category
                            for category, items in rental_by_category.items():
                                rental_summary += f"*{category.title()}*\n"
                                for equipment, rental in items:
                                    monthly_total = (equipment['price'] * rental['quantity']) / rental['duration']
                                    total_amount += monthly_total
                                    rental_summary += (
                                        f"• {rental['quantity']}x {equipment['name']}\n"
                                        f"  Duration: {rental['duration']} months\n"
                                        f"  Equipment Price: RM{equipment['price']:.2f}\n"
                                        f"  Monthly Total: RM{monthly_total:.2f}\n\n"
                                    )
                                rental_summary += "\n"
                            
                            rental_summary += f"*Total Amount for Monthly Rental: RM{total_amount:.2f}*\n\n"
                            rental_summary += "Please review and agree the Terms and Conditions to confirm your rental."
                            
                            # First send the rental summary
                            await self.messaging_service.send_message(
                                chat_id=user_id,
                                message_body=rental_summary
                            )

                            session['tnc_shown'] = True
                            
                            # Then send T&C
                            return {
                                'response': '',
                                'function_call': {
                                    'name': 'get_tnc_pdf',
                                    'arguments': {},
                                    'result': {
                                        'path': self.tnc_pdf_url,
                                        'caption': 'Please review the Terms and Conditions before proceeding. Reply with ''I agree'' to confirm or ''cancel'' to cancel.'
                                    }
                                }
                            }
                        
                        # Process the rental order
                        order_items = []
                        processed_items = set()  # Track processed items to prevent duplicates
                        
                        for rental in rental_items:
                            equipment = next((item for item in equipment_items if item['name'].lower() == rental['name'].lower()), None)
                            if equipment and equipment['name'].lower() not in processed_items:
                                processed_items.add(equipment['name'].lower())
                                order_items.append({
                                    'name': equipment['name'],
                                    'quantity': rental['quantity'],
                                    'price': equipment['price'] * rental['duration'],  # Total price for the duration
                                    'total': equipment['price'] * rental['quantity'] * rental['duration']
                                })
                        
                        if order_items:
                            order_result = self.order_service.generate_order(
                                items=order_items,
                                customer_id=user_id
                            )
                            
                            if order_result.get('status') == 'success':
                                session['order_confirmed'] = True
                                return {
                                    'response': order_result.get('message', ''),
                                    'function_call': {
                                        'name': 'generate_order',
                                        'arguments': {'items': order_items},
                                        'result': order_result
                                    }
                                }
                            else:
                                return {
                                    'response': order_result.get('message', '')
                                }
                    
                    return {'response': response}
                else:
                    return {'response': f"No equipment available for the requested types: {', '.join(equipment_types)}"}

            # elif function_name == 'get_tnc_pdf':
            #     print("Getting T&C PDF")
            #     # Mark TNC as shown
            #     session['tnc_shown'] = True
            #     session['tnc_agreed'] = False
            #     return {
            #         'response': '',
            #         'function_call': {
            #             'name': function_name,
            #             'arguments': arguments,
            #             'result': {
            #                 'path': self.tnc_pdf_url,
            #                 'caption': ''
            #             }
            #         }
            #     }

            elif function_name == 'show_menu':
                menu_text = self._format_menu(self.openai_service.rag_service.menu_items)
                return {
                    'response': menu_text
                }

            elif function_name == 'confirm_order':
                print("Confirming order")
                session['tnc_shown'] = True
                if not session.get('tnc_shown'):
                    return {
                        'response': '',
                        'function_call': {
                            'name': 'get_tnc_pdf',
                            'arguments': {},
                            'result': {
                                'path': self.tnc_pdf_url,
                                'caption': 'Please review and agree the Terms and Conditions to confirm your order'
                            }
                        }
                    }

                # Process the order
                order_result = self._process_order(session, user_id)
                return {
                    'response': order_result
                }

        return response

    def _format_menu(self, menu_items: List[Dict[str, Any]]) -> str:
        """
        Format the menu items into a readable message

        Args:
            menu_items (List[Dict[str, Any]]): Available menu items

        Returns:
            str: Formatted menu message
        """
        if not menu_items:
            return ""

        # Group items by category
        categories = {}
        for item in menu_items:
            category = item['category']
            if category not in categories:
                categories[category] = []
            categories[category].append(item)

        # Format the menu
        menu_text = "📋 *Our Product Listing* 📋\n\n"

        for category, items in categories.items():
            menu_text += f"*{category}*\n"
            for item in items:
                menu_text += f"• {item['name']} - ${item['price']:.2f} (Stock: {item['stock']})\n"
                menu_text += f"  {item['description']}\n\n"

            menu_text += "\n"

        return menu_text

    def _process_order(self, session: Dict[str, Any], user_id: str) -> str:
        """
        Process an order and return the result message

        Args:
            session (Dict[str, Any]): User session
            user_id (str): User ID

        Returns:
            str: Order result message
        """
        # Get the current order from the session
        order_items = session.get('current_order', [])
        if not order_items:
            return ""

        # Calculate total
        total = sum(item['total'] for item in order_items)

        # Process the order
        order_result = self.order_service.generate_order(
            items=order_items,
            customer_id=user_id
        )

        if order_result.get('status') == 'success':
            # Add to order history
            if 'history' in session:
                order_record = {
                    'items': order_items.copy(),
                    'total': total,
                    'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                session['history'].append(order_record)

            # Clear session state
            session['current_order'] = []
            session['order_confirmed'] = True
            session['tnc_shown'] = False
            session['tnc_agreed'] = False

            # Get order details
            order_id = order_result.get('order_id', '')
            est_time = order_result.get('estimated_time', '')

            return f"Your order (#{order_id}) has been confirmed! Total: {order_result['total_formatted']}. Estimated delivery time: {est_time}"
        else:
            session['order_confirmed'] = True
            return order_result.get('message', '')
