# AI-Powered Telegram Ordering System - Usage Guide

This document provides detailed instructions on how to use and interact with the AI-Powered Telegram Ordering System.

## Table of Contents

1. [Initial Setup](#initial-setup)
2. [Customer Interaction Flow](#customer-interaction-flow)
3. [Admin Operations](#admin-operations)
4. [Customization](#customization)
5. [Troubleshooting](#troubleshooting)

## Initial Setup

Before customers can start using the system, you need to complete these setup steps:

### 1. Set Up Environment

Ensure all environment variables are properly configured in your `.env` file:

```
# Telegram Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4.1-nano

# CSV File Configuration
MENU_CSV_PATH=data/menu.csv
ORDERS_CSV_PATH=data/orders.csv

# FastAPI Configuration
PORT=8080
ENVIRONMENT=development
LOG_DIR=logs
```

### 2. Prepare CSV Files

#### Menu CSV Setup

1. Create a CSV file for your menu at `data/menu.csv`
2. Set up the following columns:
   - Name (item name)
   - Category (e.g., Main, Sides, Drinks)
   - Description (item description)
   - Price (numerical value)
   - Available (TRUE/FALSE)
   - Stock (numerical value)
3. Add your menu items

You can use the provided `sample_menu.csv` as a template if available.

#### Orders CSV Setup

1. The system will automatically create an orders CSV file at `data/orders.csv` if it doesn't exist
2. The file will have the following columns:
   - Order ID
   - Date
   - Time
   - Customer ID
   - Items
   - Quantity
   - Total Amount

### 3. Configure Telegram Bot

1. Create a Telegram bot using BotFather (see [TELEGRAM_SETUP.md](TELEGRAM_SETUP.md) for details)
2. Set up your webhook URL to point to your deployed application:
   ```
   https://your-app-domain.com/webhook
   ```
3. Ensure your Telegram bot token is correctly set in your .env file

## Customer Interaction Flow

Here's how customers will interact with your system:

### 1. Initial Contact

When a customer messages your Telegram bot for the first time, they'll receive a welcome message.

**Example:**
- Customer: "Hello"
- System: "Welcome to [Restaurant Name]! How can I help you today? You can ask to see our menu, place an order, or get recommendations."

### 2. Viewing the Menu

Customers can request to see the menu in several ways:

**Examples:**
- Customer: "Show me the menu"
- Customer: "What do you have?"
- Customer: "Menu please"

The system will respond with a formatted menu showing categories, items, prices, and descriptions.

### 3. Getting Recommendations

Customers can ask for recommendations:

**Examples:**
- Customer: "What's good today?"
- Customer: "I want something spicy"
- Customer: "What do you recommend for lunch?"

The AI will provide personalized recommendations based on the query and available menu items.

### 4. Placing an Order

Customers can place orders in natural language:

**Examples:**
- Customer: "I'd like to order a burger and fries"
- Customer: "Can I get 2 pizzas and a soda?"
- Customer: "I want a chicken sandwich with no mayo and a side of onion rings"

The system will:
1. Identify the items and quantities
2. Process any special requests
3. Generate an order summary with the total price
4. Ask for confirmation

### 5. Confirming or Modifying an Order

After receiving the order summary:

**To confirm:**
- Customer: "confirm" or "yes" or "that's correct"

**To modify:**
- Customer: "I want to add a soda"
- Customer: "Remove the fries"
- Customer: "Make it 2 burgers instead of 1"

**To cancel:**
- Customer: "cancel" or "nevermind"

### 6. Order Confirmation

Once confirmed, the system will:
1. Save the order to the orders CSV file
2. Send a confirmation message with order details and next steps
3. Reset the order state for future orders

## Admin Operations

As an administrator, you can perform several operations:

### Viewing Orders

1. Open the `data/orders.csv` file
2. All confirmed orders will appear with timestamp, customer information, and order details

### Updating the Menu

To update your menu:
1. Open the `data/menu.csv` file
2. Add, modify, or remove items
3. Update prices, availability, and stock quantities
4. Changes will be reflected immediately in the system

### Monitoring System Health

Check the application logs for any errors or issues:

```bash
# If running with Docker
docker-compose logs -f

# If running locally
python run.py
```

You can also access logs through the API at `/logs` endpoint.

## Customization

### Modifying AI Behavior

To change how the AI responds to customers, edit the system prompt in `data/system_prompt.txt`. If this file doesn't exist, the system will use a default prompt.

You can also change the OpenAI model used by setting the `OPENAI_MODEL` environment variable in your `.env` file:

```
OPENAI_MODEL=gpt-4.1-nano  # Default model
```

Other options include:
- `gpt-4o`
- `gpt-4-turbo`
- `gpt-3.5-turbo`

Modify this prompt to change the AI's personality, response style, or behavior.

### Customizing Response Templates

To change how responses are formatted, edit the methods in `src/controllers/order_controller.py`:

- `_format_menu`: Customize menu display
- `_process_intent`: Modify response logic for different intents
- `_process_order_items`: Change how orders are processed

## Troubleshooting

### Common Issues and Solutions

#### Messages Not Being Received

**Issue**: Telegram messages aren't reaching your application.

**Solutions**:
- Verify your Telegram webhook URL is correct
- Ensure your application is publicly accessible
- Check the webhook status using the Telegram API
- Verify your TELEGRAM_BOT_TOKEN is correct

#### Incorrect Order Processing

**Issue**: The AI is misinterpreting customer orders.

**Solutions**:
- Adjust the system prompt in `data/system_prompt.txt`
- Check that your menu items are correctly formatted in the CSV file
- Review conversation logs to identify patterns in misinterpretations
- Try a different OpenAI model by changing the OPENAI_MODEL environment variable

#### CSV File Issues

**Issue**: Orders aren't being saved or menu isn't loading.

**Solutions**:
- Verify the CSV files exist in the data directory
- Check that the CSV files have the correct format and columns
- Ensure the application has write permissions to the data directory
- Check the logs for any file access errors

### Getting Support

If you encounter issues not covered here:

1. Check the application logs for error messages
2. Review the code in the relevant service or controller
3. Consult the documentation for the specific API (Telegram, OpenAI)
4. Open an issue on the project repository
