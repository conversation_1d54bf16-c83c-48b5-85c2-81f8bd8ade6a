# Deploying to Amazon ECR

This guide explains how to build and deploy the Anchor Food Ordering System to Amazon ECR (Elastic Container Registry).

## Prerequisites

1. **AWS CLI**: Make sure you have the AWS CLI installed and configured with appropriate credentials.
   ```bash
   aws --version
   ```

2. **Docker**: Ensure Docker is installed and running.
   ```bash
   docker --version
   ```

3. **AWS Permissions**: You need permissions to create and push to ECR repositories.

## Configuration

Before deploying, update the following variables in the `deploy-to-ecr.sh` script:

1. `AWS_REGION`: Your AWS region (e.g., "us-east-1", "eu-west-1")
2. `ECR_REPOSITORY_NAME`: The name for your ECR repository
3. `IMAGE_TAG`: The tag for your Docker image (default: "latest")

## Deployment Steps

### 1. Make the deployment script executable

```bash
chmod +x deploy-to-ecr.sh
```

### 2. Run the deployment script

```bash
./deploy-to-ecr.sh
```

This script will:
- Create an ECR repository if it doesn't exist
- Authenticate Docker with ECR
- Build the Docker image
- Tag the image for ECR
- Push the image to ECR

### 3. Verify the deployment

Check that your image was successfully pushed to ECR:

```bash
aws ecr describe-images --repository-name anchor-food-ordering --region your-aws-region
```

## Running the Container

### Locally

To run the container locally with required environment variables:

```bash
docker run -p 8080:8080 \
  -e OPENAI_API_KEY=your_openai_api_key \
  -e TELEGRAM_BOT_TOKEN=your_telegram_token \
  -e OPENAI_MODEL=gpt-4.1-nano \
  your-account-id.dkr.ecr.your-region.amazonaws.com/anchor-food-ordering:latest
```

### On AWS ECS

1. Create a task definition with these environment variables:
   - `OPENAI_API_KEY`
   - `TELEGRAM_BOT_TOKEN`
   - `OPENAI_MODEL=gpt-4.1-nano`
   - `PORT=8080`
   - `HOST=0.0.0.0`
   - `LOG_DIR=/app/logs`

2. Use the image URI provided by the deployment script.

3. Configure port mappings to expose port 8080.

## Environment Variables

The application requires these environment variables:

| Variable | Description | Required |
|----------|-------------|----------|
| OPENAI_API_KEY | Your OpenAI API key | Yes |
| TELEGRAM_BOT_TOKEN | Your Telegram bot token | Yes |
| PORT | The port to run the application on | No (default: 8080) |
| HOST | The host to bind to | No (default: 0.0.0.0) |
| OPENAI_MODEL | The OpenAI model to use | No (default: gpt-4.1-nano) |
| MENU_CSV_PATH | Path to the menu CSV file | No (default: data/menu.csv) |
| ORDERS_CSV_PATH | Path to the orders CSV file | No (default: data/orders.csv) |

## Troubleshooting

### Authentication Issues

If you encounter authentication issues:

```bash
aws ecr get-login-password --region your-region | docker login --username AWS --password-stdin your-account-id.dkr.ecr.your-region.amazonaws.com
```

### Build Failures

If the Docker build fails, check:
- The Dockerfile syntax
- That all required files are present
- That all required files are present in the Docker context

### Push Failures

If pushing to ECR fails:
- Verify your AWS credentials
- Check your IAM permissions
- Ensure your Docker is authenticated with ECR
