# Setting Up Your Telegram Bot

This guide will walk you through the process of setting up a Telegram bot for your AI-Powered Ordering System.

## Step 1: Create a Telegram Bot

1. Open Telegram and search for the "BotFather" (@BotFather).
2. Start a chat with <PERSON><PERSON><PERSON><PERSON> and send the command `/newbot`.
3. Follow the instructions to create your bot:
   - Provide a name for your bot (e.g., "Anchor Food Assistant")
   - Provide a username for your bot (must end with "bot", e.g., "anchor_food_bot")
4. <PERSON><PERSON><PERSON>ather will give you a token for your new bot. This token is used to authenticate your bot with the Telegram API.
5. Save this token as you'll need it for your application.

## Step 2: Configure Your Environment

1. Open your `.env` file in the project root directory.
2. Add your Telegram bot token:
   ```
   TELEGRAM_BOT_TOKEN=your_telegram_bot_token
   ```
3. Save the file.

## Step 3: Set Up Webhook for Your Bot

To receive messages from Telegram, you need to set up a webhook. This tells Telegram where to send updates when your bot receives messages.

### Using ngrok for Local Development

1. Start your FastAPI application:
   ```
   python main.py
   ```

2. In a separate terminal, start ngrok to create a public URL for your local server:
   ```
   ngrok http 8000
   ```

3. Note the HTTPS URL provided by ngrok (e.g., `https://a1b2c3d4.ngrok.io`).

4. Set up the webhook for your Telegram bot by visiting the following URL in your browser (replace with your actual values):
   ```
   https://api.telegram.org/bot<YOUR_BOT_TOKEN>/setWebhook?url=<YOUR_NGROK_URL>/webhook
   ```

   For example:
   ```
   https://api.telegram.org/bot8019978396:AAEbQRL6st356I2Jl7PGSiFJwj0J2bCtv4w/setWebhook?url=https://ae94-2001-f40-956-487-11ad-861e-ffd1-1a68.ngrok-free.app/webhook
   ```

5. You should receive a response like:
   ```json
   {"ok":true,"result":true,"description":"Webhook was set"}
   ```

### For Production Deployment

1. Deploy your application to your production server.
2. Set up the webhook using your production URL:
   ```
   https://api.telegram.org/bot<YOUR_BOT_TOKEN>/setWebhook?url=<YOUR_PRODUCTION_URL>/webhook
   ```

## Step 4: Test Your Bot

1. Open Telegram and search for your bot using the username you created.
2. Start a chat with your bot.
3. Send a message like "Hello" or "Show me the menu".
4. Your bot should respond based on the AI processing in your application.

## Troubleshooting

If your bot is not responding:

1. Check your application logs for any errors.
2. Verify that the webhook is set correctly by visiting:
   ```
   https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getWebhookInfo
   ```
3. Make sure your server is accessible from the internet.
4. Ensure your TELEGRAM_BOT_TOKEN is correctly set in your .env file.

## Additional Bot Features

You can enhance your bot with additional features:

1. **Bot Commands**: Set up commands for your bot using BotFather's `/setcommands` feature.
2. **Bot Description**: Set a description for your bot using `/setdescription`.
3. **Bot Profile Picture**: Set a profile picture using `/setuserpic`.

These features can make your bot more user-friendly and professional.

## Security Considerations

1. Keep your bot token secure and never commit it to public repositories.
2. Consider implementing authentication for sensitive operations.
3. Monitor your bot's usage to detect any unusual activity.

For more information, refer to the [Telegram Bot API documentation](https://core.telegram.org/bots/api).
