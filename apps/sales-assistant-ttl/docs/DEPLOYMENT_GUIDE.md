# Deploying the Sales Assistant Chatbot to AWS ECS

This guide explains how to deploy the Sales Assistant Chatbot to Amazon ECS (Elastic Container Service) using Terraform through GitHub Actions, including exposing webhook endpoints.

## Prerequisites

1. **AWS Account**: You need an AWS account with appropriate permissions.
2. **GitHub Repository**: The code should be in a GitHub repository.
3. **AWS IAM Role**: A role for GitHub Actions to assume with permissions to:
   - Push to ECR
   - Deploy to ECS
   - Apply Terraform changes
4. **AWS Secrets Manager**: Secrets for the chatbot (OpenAI API key, Telegram Bot Token)

## Deployment Architecture

The deployment consists of:

1. **Docker Container**: The chatbot application packaged as a Docker container
2. **Amazon ECR**: Stores the Docker image
3. **Amazon ECS**: Runs the container as a service
4. **Application Load Balancer (ALB)**: Routes traffic to the service and exposes webhook endpoints
5. **AWS Secrets Manager**: Securely stores API keys and tokens

## Deployment Steps

### 1. Set Up AWS Secrets Manager

Run the provided script to set up the required secrets in AWS Secrets Manager:

```bash
# Make the script executable
chmod +x scripts/setup-aws-secrets.sh

# Run the script
./scripts/setup-aws-secrets.sh
```

This script will:
- Prompt you for your OpenAI API key and Telegram Bot Token
- Create or update the secrets in AWS Secrets Manager
- These secrets will be used by the ECS service

### 2. Configure GitHub Repository Secrets

Add the following secrets to your GitHub repository:

- `AWS_ROLE_TO_ASSUME`: The ARN of the IAM role that GitHub Actions will assume

To add these secrets:
1. Go to your GitHub repository
2. Click on "Settings" > "Secrets and variables" > "Actions"
3. Click on "New repository secret"
4. Add the secret name and value

### 3. Deploy Using GitHub Actions

The deployment is automated using GitHub Actions. You can trigger the deployment by:

1. Pushing to the main branch:
   ```bash
   git add .
   git commit -m "Deploy chatbot to ECS"
   git push origin main
   ```

2. Manually triggering the workflow:
   - Go to your GitHub repository
   - Click on "Actions"
   - Select the "Deploy Chatbot to ECS" workflow
   - Click on "Run workflow"

The GitHub Actions workflow will:
1. Build the Docker image
2. Push the image to Amazon ECR
3. Update the ECS service configuration
4. Apply Terraform changes to deploy the service

### 4. Set Up Telegram Webhook

After the deployment is complete, set up the Telegram webhook using the provided script:

```bash
# Make the script executable
chmod +x scripts/setup-telegram-webhook.sh

# Run the script
./scripts/setup-telegram-webhook.sh
```

This script will:
- Prompt you for your Telegram Bot Token (if not set as an environment variable)
- Configure the webhook URL to point to your ECS service
- Verify that the webhook was set up correctly

## Webhook Configuration

The chatbot exposes two webhook endpoints:

1. `/webhook`: For Telegram Bot messages
2. `/whatsapp-webhook`: For WhatsApp messages

The ALB routes traffic to these endpoints based on the path patterns configured in `ecs_services.yaml`.

### Telegram Webhook

The Telegram webhook is configured to receive updates at:
```
https://ttl-chatbot.dev.anchorsprint.com/webhook
```

### WhatsApp Webhook

The WhatsApp webhook is configured to receive updates at:
```
https://ttl-chatbot.dev.anchorsprint.com/whatsapp-webhook
```

## Monitoring and Troubleshooting

### Checking Service Status

```bash
# Check ECS service status
aws ecs describe-services \
    --cluster api-platform-dev-cluster \
    --services sales-assistant-service

# View logs
aws logs get-log-events \
    --log-group-name /ecs/sales-assistant-service \
    --log-stream-name <log-stream-name>
```

### Common Issues

1. **Health Check Failures**: Ensure the `/health` endpoint is working correctly
2. **Missing Environment Variables**: Check that all required environment variables are set
3. **Webhook Configuration**: Verify the webhook URLs are correctly set up

## Updating the Deployment

To update the deployment:

1. Push changes to the main branch
2. GitHub Actions will automatically build and deploy the changes
3. Monitor the GitHub Actions workflow for any errors

Alternatively, you can manually trigger the workflow using the "Run workflow" button in the GitHub Actions tab.

## Security Considerations

1. **API Keys and Tokens**: Store these in AWS Secrets Manager, not in environment variables or code
2. **HTTPS**: Ensure all webhook endpoints use HTTPS
3. **Access Control**: Restrict access to the ECS service and AWS resources

## Conclusion

This guide provides a comprehensive approach to deploying the Sales Assistant Chatbot to AWS ECS using Terraform through GitHub Actions, with proper webhook configuration for Telegram and WhatsApp integration.
