# Deploying the Sales Assistant Chatbot to AWS ECS

This guide explains how to deploy the Sales Assistant Chatbot to Amazon ECS (Elastic Container Service) using Terraform and GitHub Actions.

## Prerequisites

1. **AWS Account**: You need an AWS account with appropriate permissions.
2. **GitHub Repository**: The code should be in a GitHub repository.
3. **AWS IAM Role**: A role for GitHub Actions to assume with permissions to:
   - Push to ECR
   - Deploy to ECS
   - Apply Terraform changes
4. **AWS Secrets Manager**: Secrets for the chatbot (OpenAI API key, Telegram Bot Token)

## Deployment Architecture

The deployment consists of:

1. **Docker Container**: The chatbot application packaged as a Docker container
2. **Amazon ECR**: Stores the Docker image
3. **Amazon ECS**: Runs the container as a service
4. **Application Load Balancer (ALB)**: Routes traffic to the service
5. **AWS Secrets Manager**: Securely stores API keys and tokens

## Setting Up AWS Resources

### 1. Create AWS Secrets

Create the following secrets in AWS Secrets Manager:

```bash
# Create OpenAI API key secret
aws secretsmanager create-secret \
    --name sales-assistant/openai-api-key \
    --secret-string "your-openai-api-key"

# Create Telegram Bot Token secret
aws secretsmanager create-secret \
    --name sales-assistant/telegram-bot-token \
    --secret-string "your-telegram-bot-token"
```

### 2. Create IAM Role for GitHub Actions

Create an IAM role that GitHub Actions can assume:

1. Create a new IAM role with a trust policy for GitHub Actions
2. Attach policies for ECR, ECS, and Terraform operations
3. Note the ARN of the role for GitHub Actions configuration

## GitHub Repository Setup

### 1. Add GitHub Secrets

Add the following secrets to your GitHub repository:

- `AWS_ROLE_TO_ASSUME`: The ARN of the IAM role created above

### 2. Configure GitHub Actions Workflow

The workflow file `.github/workflows/deploy-chatbot.yml` is already set up to:

1. Build and push the Docker image to ECR
2. Update the ECS service configuration
3. Apply Terraform changes to deploy the service

## Webhook Configuration

The chatbot exposes two webhook endpoints:

1. `/webhook`: For Telegram Bot messages
2. `/whatsapp-webhook`: For WhatsApp messages

The ALB routes traffic to these endpoints based on the path patterns configured in `ecs_services.yaml`.

### Setting Up Telegram Webhook

After deployment, set up the Telegram webhook:

```bash
curl -X POST https://api.telegram.org/bot<TELEGRAM_BOT_TOKEN>/setWebhook \
     -H "Content-Type: application/json" \
     -d '{"url": "https://api.dev.anchorsprint.com/webhook"}'
```

## Monitoring and Troubleshooting

### Checking Service Status

```bash
# Check ECS service status
aws ecs describe-services \
    --cluster api-platform-dev-cluster \
    --services sales-assistant-service

# View logs
aws logs get-log-events \
    --log-group-name /ecs/sales-assistant-service \
    --log-stream-name <log-stream-name>
```

### Common Issues

1. **Health Check Failures**: Ensure the `/health` endpoint is working correctly
2. **Missing Environment Variables**: Check that all required environment variables are set
3. **Webhook Configuration**: Verify the webhook URLs are correctly set up

## Updating the Deployment

To update the deployment:

1. Push changes to the main branch
2. GitHub Actions will automatically build and deploy the changes
3. Monitor the GitHub Actions workflow for any errors

Alternatively, you can manually trigger the workflow using the "Run workflow" button in the GitHub Actions tab.

## Cleaning Up

To remove the deployment:

```bash
cd infra/aws-playground-dev-tf
terraform destroy
```

This will remove all AWS resources created by Terraform.
