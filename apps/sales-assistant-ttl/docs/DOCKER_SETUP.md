# Docker Setup for Anchor Food Ordering System

This document provides instructions for running the Anchor Food Ordering System using Docker.

## Prerequisites

- [Docker](https://docs.docker.com/get-docker/)
- [Docker Compose](https://docs.docker.com/compose/install/)

## Quick Start

1. Create a `.env` file based on the `.env.example` template:

```bash
cp .env.example .env
```

2. Edit the `.env` file and fill in your actual API keys and configuration values:

```
# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4.1-nano

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here

# Application Configuration
PORT=8080
ENVIRONMENT=production

# Logging Configuration
LOG_DIR=logs
LOG_LEVEL=INFO
```

3. Build and run with Docker Compose:

```bash
docker-compose up -d
```

4. Access the application at http://localhost:8080

## How It Works

The Docker setup:
- Includes default environment variables in the Dockerfile
- Allows overriding variables through your `.env` file
- Installs all dependencies from `requirements.txt`
- Maps the specified port (default: 8080) to your host
- Mounts the data and logs directories as volumes
- Includes a health check to ensure the application is running
- Validates required environment variables before starting

## Environment Variables

Environment variables can be set in three ways:

1. **Default values** in the Dockerfile
2. **Override values** in your `.env` file
3. **Runtime values** in the docker-compose.yml file

Here are the key environment variables:

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| OPENAI_API_KEY | Your OpenAI API key | (empty) | Yes |
| TELEGRAM_BOT_TOKEN | Your Telegram bot token | (empty) | Yes |
| OPENAI_MODEL | The OpenAI model to use | gpt-4.1-nano | No |
| MENU_CSV_PATH | Path to the menu CSV file | data/menu.csv | No |
| ORDERS_CSV_PATH | Path to the orders CSV file | data/orders.csv | No |
| PORT | The port to run the application on | 8080 | No |
| HOST | The host to bind to | 0.0.0.0 | No |
| LOG_LEVEL | The logging level | INFO | No |
| ENVIRONMENT | The environment | production | No |

## Running with Docker directly

If you prefer to run with Docker directly:

```bash
# Build the image
docker build -t anchor-food-ordering .

# Run the container
docker run -p 8080:8080 --env-file .env -v ./data:/app/data -v ./logs:/app/logs anchor-food-ordering
```

## Troubleshooting

If you encounter any issues:

1. Check the logs:
```bash
docker-compose logs
```

2. Verify the application is running with the health check:
```bash
curl http://localhost:8080/health
```

3. Ensure the required data files exist in the `data` directory.

4. If the container is not starting, try running it without detached mode to see the output:
```bash
docker-compose up
```

5. If you make changes to the code or dependencies, rebuild the container:
```bash
docker-compose build --no-cache
docker-compose up -d
```
