import unittest
from unittest.mock import Magic<PERSON>ock, patch
from src.services.twilio_service import TwilioService
from src.services.openai_service import OpenAIService
from src.services.sheets_service import GoogleSheetsService

class TestTwilioService(unittest.TestCase):
    @patch('src.services.twilio_service.Client')
    def test_send_message(self, mock_client):
        # Set up mock Twilio client
        mock_message = MagicMock()
        mock_message.sid = 'test_sid'
        mock_message.status = 'sent'
        mock_message.to = 'whatsapp:+**********'
        mock_message.body = 'Test message'

        mock_messages = MagicMock()
        mock_messages.create.return_value = mock_message

        mock_client_instance = MagicMock()
        mock_client_instance.messages = mock_messages

        mock_client.return_value = mock_client_instance

        # Create the service
        service = TwilioService(
            account_sid='test_sid',
            auth_token='test_token',
            phone_number='+**********'
        )

        # Send a message
        result = service.send_message('+**********', 'Test message')

        # Verify the message was sent with correct parameters
        mock_messages.create.assert_called_once_with(
            from_='whatsapp:+**********',
            body='Test message',
            to='whatsapp:+**********'
        )

        # Verify the result
        self.assertEqual(result['sid'], 'test_sid')
        self.assertEqual(result['status'], 'sent')
        self.assertEqual(result['to'], 'whatsapp:+**********')
        self.assertEqual(result['body'], 'Test message')

class TestOpenAIService(unittest.TestCase):
    @patch('src.services.openai_service.openai.chat.completions.create')
    def test_analyze_message(self, mock_create):
        # Set up mock OpenAI response
        mock_response = MagicMock()
        mock_choice = MagicMock()
        mock_message = MagicMock()
        mock_message.content = '{"intent": "order", "items": [{"name": "Burger", "quantity": 1}], "response": "You want a burger"}'
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]

        mock_create.return_value = mock_response

        # Create the service
        service = OpenAIService(api_key='test_key', model='gpt-4.1-nano')

        # Analyze a message
        result = service.analyze_message(
            message='I want a burger',
            menu_items=[{'name': 'Burger', 'description': 'Beef burger', 'price': 10.99}]
        )

        # Verify OpenAI was called
        mock_create.assert_called_once()

        # Verify the result
        self.assertEqual(result, '{"intent": "order", "items": [{"name": "Burger", "quantity": 1}], "response": "You want a burger"}')

class TestGoogleSheetsService(unittest.TestCase):
    @patch('src.services.sheets_service.build')
    @patch('src.services.sheets_service.service_account.Credentials.from_service_account_file')
    def test_get_menu_items(self, mock_credentials, mock_build):
        # Set up mock Google Sheets API response
        mock_values = MagicMock()
        mock_values.get().execute.return_value = {
            'values': [
                ['Burger', 'Main', 'Beef burger', '10.99', 'True'],
                ['Fries', 'Sides', 'French fries', '3.99', 'True'],
                ['Salad', 'Sides', 'Fresh salad', '5.99', 'False']  # Not available
            ]
        }

        mock_spreadsheets = MagicMock()
        mock_spreadsheets.values.return_value = mock_values

        mock_service = MagicMock()
        mock_service.spreadsheets.return_value = mock_spreadsheets

        mock_build.return_value = mock_service

        # Create the service
        service = GoogleSheetsService(
            credentials_file='test_credentials.json',
            menu_spreadsheet_id='test_menu_id',
            orders_spreadsheet_id='test_orders_id'
        )

        # Get menu items
        result = service.get_menu_items()

        # Verify the API was called correctly
        mock_values.get.assert_called_once_with(
            spreadsheetId='test_menu_id',
            range='Menu!A2:E'
        )

        # Verify the result (only available items should be included)
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0]['name'], 'Burger')
        self.assertEqual(result[1]['name'], 'Fries')
        self.assertTrue(result[0]['available'])
        self.assertTrue(result[1]['available'])

if __name__ == '__main__':
    unittest.main()
