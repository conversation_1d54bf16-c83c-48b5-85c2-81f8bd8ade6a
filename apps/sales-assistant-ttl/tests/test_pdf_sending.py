import unittest
import os
import asyncio
from unittest.mock import <PERSON><PERSON><PERSON>, AsyncMock, patch
import sys
import json

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

class AsyncioTestCase(unittest.TestCase):
    """Base class for asyncio test cases"""

    def run_async(self, coro):
        """Run a coroutine in the event loop"""
        return asyncio.get_event_loop().run_until_complete(coro)

class TestPDFSending(AsyncioTestCase):
    """Test the PDF sending functionality"""

    def setUp(self):
        """Set up the test environment"""
        # Create a mock bot
        self.mock_bot = AsyncMock()
        self.mock_bot.send_document = AsyncMock()
        self.mock_bot.get_me = AsyncMock(return_value=MagicMock(username="test_bot", id=12345))

        # Create a mock logging service
        self.mock_logging_service = MagicMock()

        # Import the TelegramService class
        from src.services.telegram_service import TelegramService

        # Create the telegram service with the mock bot
        self.telegram_service = TelegramService(
            token="test_token",
            connection_pool_size=1,
            connect_timeout=1.0,
            read_timeout=1.0,
            max_session_history=10,
            max_sessions=10,
            logging_service=self.mock_logging_service
        )

        # Replace the bot with our mock
        self.telegram_service.bot = self.mock_bot

        # Create a test PDF file
        self.test_pdf_path = os.path.join(os.path.dirname(__file__), "test_tnc.pdf")
        with open(self.test_pdf_path, "wb") as f:
            f.write(b"%PDF-1.5\n%Test PDF file for unit tests")

    def tearDown(self):
        """Clean up after the test"""
        # Remove the test PDF file
        if os.path.exists(self.test_pdf_path):
            os.remove(self.test_pdf_path)

    def test_send_document(self):
        """Test sending a document"""
        # Set up the mock bot to return a message with a document
        mock_message = MagicMock()
        mock_message.message_id = 123
        mock_message.chat_id = 456
        mock_message.document = MagicMock()
        mock_message.document.file_id = "test_file_id"
        self.mock_bot.send_document.return_value = mock_message

        # Call the send_document method
        result = self.run_async(self.telegram_service.send_document(
            chat_id=456,
            document_path=self.test_pdf_path,
            caption="Test caption"
        ))

        # Check that the bot's send_document method was called with the correct parameters
        self.mock_bot.send_document.assert_called_once()
        args, kwargs = self.mock_bot.send_document.call_args
        self.assertEqual(kwargs["chat_id"], 456)
        self.assertEqual(kwargs["caption"], "Test caption")

        # Check that the result contains the correct information
        self.assertEqual(result["message_id"], 123)
        self.assertEqual(result["chat_id"], 456)
        self.assertEqual(result["document_id"], "test_file_id")
        self.assertEqual(result["document_path"], self.test_pdf_path)

    @patch("os.path.exists")
    @patch("os.path.getsize")
    @patch("os.access")
    def test_telegram_service_sends_document(self, mock_access, mock_getsize, mock_exists):
        """Test that the TelegramService can send a document"""
        # Set up the mocks
        mock_exists.return_value = True
        mock_getsize.return_value = 1024
        mock_access.return_value = True

    def test_handle_telegram_update(self):
        """Test that the handle_telegram_update function correctly handles a TNC document intent"""
        # Import the handle_telegram_update function from main.py
        from main import handle_telegram_update

        # Create a mock message for the bot's response
        mock_message = MagicMock()
        mock_message.message_id = 123
        mock_message.chat_id = 456
        mock_message.document = MagicMock()
        mock_message.document.file_id = "test_file_id"
        self.mock_bot.send_document.return_value = mock_message

        # Call the send_document method
        result = self.run_async(self.telegram_service.send_document(
            chat_id=456,
            document_path=self.test_pdf_path,
            caption="Please review our Terms and Conditions"
        ))

        # Check that the bot's send_document method was called with the correct parameters
        self.mock_bot.send_document.assert_called_once()
        args, kwargs = self.mock_bot.send_document.call_args
        self.assertEqual(kwargs["chat_id"], 456)
        self.assertEqual(kwargs["caption"], "Please review our Terms and Conditions")

        # Check that the result contains the correct information
        self.assertEqual(result["message_id"], 123)
        self.assertEqual(result["chat_id"], 456)
        self.assertEqual(result["document_id"], "test_file_id")
        self.assertEqual(result["document_path"], self.test_pdf_path)

if __name__ == '__main__':
    # Run the tests
    unittest.main()
