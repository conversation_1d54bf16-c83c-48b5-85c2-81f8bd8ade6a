from fastapi.testclient import TestClient
from unittest.mock import MagicMock, patch
import pytest
from main import app

client = TestClient(app)

def test_root_endpoint():
    """Test the root endpoint returns a welcome message"""
    response = client.get("/")
    assert response.status_code == 200
    assert response.json() == {"message": "Welcome to the AI-Powered WhatsApp Ordering System API"}

def test_health_check():
    """Test the health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "healthy"}

@patch('main.order_controller.process_message')
@patch('main.twilio_service.create_twiml_response')
def test_webhook_endpoint(mock_create_twiml, mock_process_message):
    """Test the webhook endpoint processes messages correctly"""
    # Set up mocks
    mock_process_message.return_value = "Thank you for your order!"
    mock_create_twiml.return_value = "<?xml version='1.0' encoding='UTF-8'?><Response><Message>Thank you for your order!</Message></Response>"
    
    # Send a test request
    response = client.post(
        "/webhook",
        data={
            "Body": "I want to order a pizza",
            "From": "whatsapp:+**********"
        }
    )
    
    # Check the response
    assert response.status_code == 200
    assert "Thank you for your order!" in response.text
    
    # Verify the mocks were called correctly
    mock_process_message.assert_called_once_with("I want to order a pizza", "whatsapp:+**********")
    mock_create_twiml.assert_called_once_with("Thank you for your order!")
