import unittest
import asyncio
import os
import sys

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import the test modules
from test_pdf_sending import TestPDFSending

class AsyncioTestCase(unittest.TestCase):
    """Base class for asyncio test cases"""
    
    def run_async(self, coro):
        """Run a coroutine in the event loop"""
        return asyncio.get_event_loop().run_until_complete(coro)

# Create a test suite
def create_test_suite():
    """Create a test suite with all the tests"""
    suite = unittest.TestSuite()
    
    # Add the PDF sending tests
    suite.addTest(TestPDFSending('test_send_document'))
    suite.addTest(TestPDFSending('test_main_handler_sends_document'))
    
    return suite

if __name__ == '__main__':
    # Run the tests
    runner = unittest.TextTestRunner()
    runner.run(create_test_suite())
