import unittest
from unittest.mock import MagicMock, patch
import json
from src.controllers.order_controller import OrderController

class TestOrderController(unittest.TestCase):
    def setUp(self):
        # Create mock services
        self.messaging_service = MagicMock()
        self.openai_service = MagicMock()
        self.sheets_service = MagicMock()
        self.order_service = MagicMock()

        # Create the controller with mock services
        self.controller = OrderController(
            messaging_service=self.messaging_service,
            openai_service=self.openai_service,
            sheets_service=self.sheets_service,
            order_service=self.order_service
        )

        # Sample menu items for testing
        self.menu_items = [
            {
                'name': 'Burger',
                'category': 'Main',
                'description': 'Delicious beef burger',
                'price': 10.99,
                'available': True,
                'stock': 10  # Add stock field
            },
            {
                'name': 'Fries',
                'category': 'Sides',
                'description': 'Crispy french fries',
                'price': 3.99,
                'available': True,
                'stock': 20  # Add stock field
            }
        ]

        # Set up the sheets service to return our sample menu
        self.sheets_service.get_menu_items.return_value = self.menu_items

    def test_process_message_menu_intent(self):
        # Set up the OpenAI service to return a menu intent
        self.openai_service.analyze_message.return_value = {
            'intent': 'menu',
            'items': [],
            'response': 'Here is our products'
        }

        # Process a message
        response = self.controller.process_message('Show me the menu', 'whatsapp:+1234567890')

        # Check that the response contains menu items
        self.assertIn('Burger', response)
        self.assertIn('Fries', response)

        # We no longer verify messaging service calls in the controller tests
        # The controller now returns the response directly

    def test_process_message_order_intent(self):
        # Set up the OpenAI service to return an order intent
        self.openai_service.analyze_message.return_value = {
            'intent': 'order',
            'items': [
                {'name': 'Burger', 'quantity': 2, 'special_requests': 'No onions'},
                {'name': 'Fries', 'quantity': 1}
            ],
            'response': 'You want to order 2 burgers and fries'
        }

        # Process a message
        response = self.controller.process_message('I want 2 burgers with no onions and a side of fries', 'whatsapp:+1234567890')

        # Check that the response is a dictionary with the expected structure
        self.assertIsInstance(response, dict)
        self.assertEqual(response.get('intent'), 'tnc_document')
        self.assertIn('function_call', response)
        self.assertIn('response', response)
        self.assertIn('order summary', response.get('response', '').lower())
        # Check that the order summary contains the expected items
        order_summary = response.get('response', '')
        self.assertIn('2x Burger', order_summary)
        self.assertIn('1x Fries', order_summary)
        self.assertIn('No onions', order_summary)

        # Calculate expected total
        expected_total = (10.99 * 2) + 3.99
        self.assertIn(f'${expected_total:.2f}', order_summary)

        # We no longer verify messaging service calls in the controller tests
        # The controller now returns the response directly

    def test_process_message_confirm_order(self):
        # First, set up an order in the session
        phone = 'whatsapp:+1234567890'  # Match the phone format used in the test
        self.controller.customer_sessions[phone] = {
            'conversation_history': [
                {'is_customer': True, 'message': 'Hello, I want to order food'},
                {'is_customer': False, 'message': 'What would you like to order?'},
                {'is_customer': True, 'message': 'I want 2 burgers with no onions and a side of fries'}
            ],
            'current_order': [
                {'name': 'Burger', 'price': 10.99, 'quantity': 2, 'special_requests': 'No onions'},
                {'name': 'Fries', 'price': 3.99, 'quantity': 1}
            ],
            'order_confirmed': False,
            'tnc_shown': True,  # Mark that T&C have been shown
            'tnc_agreed': True  # Mark that user has agreed to T&C
        }

        # Set up the OpenAI service to return a confirm intent
        self.openai_service.analyze_message.return_value = {
            'intent': 'confirm',
            'items': [],
            'response': 'Confirming your order'
        }

        # Set up the order service to successfully generate orders
        self.order_service.generate_order.return_value = {
            "status": "success",
            "order_id": "ORD-20230101000000",
            "estimated_time": "10-15 minutes"
        }

        # Process a confirmation message
        response = self.controller.process_message('confirm', 'whatsapp:+1234567890')

        # Check that the response confirms the order
        # The response should now be in the format "Your order (#order_id) will be ready in about est_time..."
        self.assertTrue(
            'your order (#' in response.lower() or
            'will be ready in about' in response.lower() or
            'would you like anything else' in response.lower()
        )

        # Verify that the order was generated
        self.order_service.generate_order.assert_called()

        # Verify that the session was updated
        self.assertEqual(self.controller.customer_sessions[phone]['current_order'], [])
        self.assertTrue(self.controller.customer_sessions[phone]['order_confirmed'])
        # Verify that the conversation history was cleared
        self.assertEqual(self.controller.customer_sessions[phone]['conversation_history'], [])

        # Verify that the messaging service's clear_session method was called
        self.messaging_service.clear_session.assert_called_once_with(phone)

        # Verify that a new system message was added to the session
        self.messaging_service.add_to_session.assert_called_with(
            chat_id=phone,
            role="system",
            content="Order successfully placed. Starting a new conversation. INTENT: order_success"
        )

        # We no longer verify messaging service calls in the controller tests
        # The controller now returns the response directly

if __name__ == '__main__':
    unittest.main()
