#!/bin/bash

# Exit on error
set -e

# Configuration - CHANGE THESE VALUES
AWS_REGION="ap-southeast-1"  # Change to your AWS region
ECR_REPOSITORY_NAME="sales-assistant-ttl"
IMAGE_TAG="latest"
AWS_PROFILE="playground"  # Your AWS SSO profile name

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Check if we're in the correct directory
if [ ! -f "${PROJECT_ROOT}/Dockerfile" ]; then
    echo "Error: Dockerfile not found in ${PROJECT_ROOT}"
    echo "Please run this script from the scripts directory or ensure the Dockerfile exists in the project root."
    exit 1
fi

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "AWS CLI is not installed. Please install it first."
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "Docker is not installed. Please install it first."
    exit 1
fi

# Check if AWS SSO session is valid, if not, login
echo "Checking AWS SSO session..."
if ! aws sts get-caller-identity --profile ${AWS_PROFILE} &> /dev/null; then
    echo "AWS SSO session expired or invalid. Please login..."
    aws sso login --profile ${AWS_PROFILE}
    
    # Verify login was successful
    if ! aws sts get-caller-identity --profile ${AWS_PROFILE} &> /dev/null; then
        echo "Failed to authenticate with AWS SSO. Please check your credentials and try again."
        exit 1
    fi
fi

# Get AWS account ID using SSO profile
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --profile ${AWS_PROFILE} --query Account --output text)
if [ $? -ne 0 ]; then
    echo "Failed to get AWS account ID. Make sure you're authenticated with AWS SSO."
    exit 1
fi

# ECR repository URI
ECR_REPOSITORY_URI="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPOSITORY_NAME}"

echo "=== Building and Deploying to Amazon ECR ==="
echo "Repository: ${ECR_REPOSITORY_URI}"
echo "Image tag: ${IMAGE_TAG}"
echo "Using AWS Profile: ${AWS_PROFILE}"
echo "Project Root: ${PROJECT_ROOT}"

# Step 1: Create ECR repository if it doesn't exist
echo "Creating ECR repository if it doesn't exist..."
aws ecr describe-repositories --repository-names ${ECR_REPOSITORY_NAME} --region ${AWS_REGION} --profile ${AWS_PROFILE} || \
    aws ecr create-repository --repository-name ${ECR_REPOSITORY_NAME} --region ${AWS_REGION} --profile ${AWS_PROFILE}

# Step 2: Authenticate Docker to ECR
echo "Authenticating Docker with ECR..."
aws ecr get-login-password --region ${AWS_REGION} --profile ${AWS_PROFILE} | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com

# Step 3: Build the Docker image
echo "Building Docker image..."
cd "${PROJECT_ROOT}"  # Change to project root directory
docker build -t ${ECR_REPOSITORY_NAME}:${IMAGE_TAG} .

# Step 4: Tag the image for ECR
echo "Tagging image for ECR..."
docker tag ${ECR_REPOSITORY_NAME}:${IMAGE_TAG} ${ECR_REPOSITORY_URI}:${IMAGE_TAG}

# Step 5: Push the image to ECR
echo "Pushing image to ECR..."
docker push ${ECR_REPOSITORY_URI}:${IMAGE_TAG}

echo "=== Deployment Complete ==="
echo "Image URI: ${ECR_REPOSITORY_URI}:${IMAGE_TAG}"
echo ""
echo "To run this container with required environment variables:"
echo "docker run -p 8000:8000 \\"
echo "  -e OPENAI_API_KEY=your_openai_api_key \\"
echo "  -e TELEGRAM_BOT_TOKEN=your_telegram_token \\"
echo "  ${ECR_REPOSITORY_URI}:${IMAGE_TAG}"

echo ""
echo "To deploy to ECS, create a task definition with these environment variables:"
echo "  - OPENAI_API_KEY"
echo "  - TELEGRAM_BOT_TOKEN"
echo "  - PORT=8000"
echo "  - HOST=0.0.0.0"
