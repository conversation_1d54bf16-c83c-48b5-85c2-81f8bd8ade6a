import os
import json
import asyncio
import datetime
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from src.services.telegram_service import TelegramService
from src.services.whatsapp_service import WhatsAppService
from src.services.openai_service import OpenAIService
from src.services.csv_service import CSVService
from src.services.order_service import OrderService
from src.services.prompt_service import PromptService
from src.services.logging_service import LoggingService
from src.controllers.order_controller import OrderController
from src.services.rag_service import RAGService
import uvicorn
from typing import Dict, Any
from src.utils.aws_config import get_config_value

region = os.getenv("AWS_REGION", "ap-southeast-1")
openai_secret_arn = os.getenv("OPENAI_SECRET_ARN")
telegram_secret_arn = os.getenv("TELEGRAM_SECRET_ARN")
whatsapp_secret_arn = os.getenv("WHATSAPP_SECRET_ARN")
tnc_secret_arn = os.getenv("TNC_URL_ARN")

# Initialize FastAPI app
app = FastAPI(
    title="AI-Powered WhatsApp Ordering System",
    description="A system that processes WhatsApp orders using AI and stores data in CSV files",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Initialize logging service
logging_service = LoggingService(
    log_dir=os.getenv('LOG_DIR', 'logs')  # Use LOG_DIR env var or default to 'logs'
)

# Initialize services
telegram_service = TelegramService(
    token=get_config_value(telegram_secret_arn, region),
    connection_pool_size=16,  # Increased pool size
    connect_timeout=10.0,     # Longer connect timeout
    read_timeout=60.0,        # Longer read timeout
    max_session_history=20,   # Store up to 20 messages per chat
    max_sessions=1000,        # Support up to 1000 different chats
    logging_service=logging_service  # Add logging service
)

# Initialize WhatsApp service
whatsapp_service = WhatsAppService(
    max_session_history=20,   # Store up to 20 messages per chat
    max_sessions=1000,        # Support up to 1000 different chats
    logging_service=logging_service  # Add logging service
)

# Initialize order service for menu and orders
order_service = OrderService(
    menu_file_path=os.getenv('MENU_CSV_PATH', 'data/menu.csv'),
    orders_file_path=os.getenv('ORDERS_CSV_PATH', 'data/orders.csv'),
    logging_service=logging_service  # Add logging service
)

# Initialize prompt service
prompt_service = PromptService(order_service=order_service)

# Initialize OpenAI service with prompt service
openai_service = OpenAIService(
    api_key=get_config_value(openai_secret_arn, region),
    prompt_service=prompt_service,
    model=get_config_value(openai_secret_arn, region) or 'gpt-4.1-nano'  # Load model from secret with default
)

# Initialize RAG service with prompt service
rag_service = RAGService(
    api_key=get_config_value(openai_secret_arn, region),
    model=get_config_value(openai_secret_arn, region) or 'gpt-4.1-nano',
    prompt_service=prompt_service
)

# Keep the CSV service for backward compatibility
csv_service = CSVService(
    menu_file_path=os.getenv('MENU_CSV_PATH', 'data/menu.csv'),
    orders_file_path=os.getenv('ORDERS_CSV_PATH', 'data/orders.csv')
)

# Initialize controllers for different messaging platforms
telegram_controller = OrderController(
    messaging_service=telegram_service,
    openai_service=openai_service,
    sheets_service=csv_service,
    order_service=order_service,
    tnc_pdf_url=get_config_value(tnc_secret_arn, region) or 'https://ai-chatbot-asset.s3.ap-southeast-1.amazonaws.com/tnc.pdf'
)

# Create a separate controller for WhatsApp
whatsapp_controller = OrderController(
    messaging_service=whatsapp_service,  # Use WhatsApp service for WhatsApp messages
    openai_service=openai_service,
    sheets_service=csv_service,
    order_service=order_service,
    tnc_pdf_url=get_config_value(tnc_secret_arn, region) or 'https://ai-chatbot-asset.s3.ap-southeast-1.amazonaws.com/tnc.pdf'
)

@app.get("/")
async def root():
    """
    Root endpoint that returns a welcome message
    """
    return {"message": "Welcome to the AI-Powered WhatsApp Ordering System API"}

@app.get("/sales-assistant-ttl/health")
async def health():
    """
    Health check endpoint for ECS
    """
    return {"status": "healthy", "timestamp": datetime.datetime.now().isoformat()}

@app.post("/webhook")
async def webhook(request: Request):
    """
    Webhook endpoint for Telegram Bot messages

    This endpoint receives updates from Telegram and processes them.

    Returns:
        JSONResponse: A response to Telegram
    """
    try:
        # Parse the request body as JSON
        update_json = await request.json()

        # Log the update for debugging
        print(f"Received Telegram update: {json.dumps(update_json, indent=2)}")

        # Handle the update
        result = await handle_telegram_update(update_json)
        print(f"Webhook handler result: {json.dumps(result, indent=2)}")
        return result
    except Exception as e:
        print(f"ERROR in webhook handler: {e}")
        import traceback
        traceback.print_exc()
        return {"status": "error", "message": str(e)}

@app.post("/whatsapp-webhook")
async def whatsapp_webhook(request: Request):
    """
    Webhook endpoint for WhatsApp messages

    This endpoint receives updates from WhatsApp and processes them.

    Returns:
        JSONResponse: A response to WhatsApp
    """
    try:
        # Parse the request body as JSON
        update_json = await request.json()

        # Log the update for debugging
        print(f"Received WhatsApp update: {json.dumps(update_json, indent=2)}")

        # Handle the update
        result = await handle_whatsapp_update(update_json, whatsapp_service, whatsapp_controller)
        print(f"WhatsApp webhook handler result: {json.dumps(result, indent=2)}")
        return result
    except Exception as e:
        print(f"ERROR in WhatsApp webhook handler: {e}")
        import traceback
        traceback.print_exc()
        return {"status": "error", "message": str(e)}

async def send_whatsapp_tnc_pdf(chat_id, whatsapp_service=whatsapp_service, pdf_path=None, caption=None):
    """
    Send the Terms and Conditions PDF to the user via WhatsApp.
    This is a dedicated function to ensure the PDF is sent correctly.

    Args:
        chat_id (str): The chat ID to send the PDF to
        whatsapp_service (WhatsAppService): The WhatsApp service to use
        pdf_path (str, optional): The path or URL to the PDF file. If not provided, uses the S3 URL from environment variables
        caption (str, optional): The caption for the PDF. If not provided, uses a default caption

    Returns:
        dict: The result of sending the PDF
    """
    print(f"\n===== SENDING TNC PDF VIA WHATSAPP =====")

    # Define the path to the PDF file - prefer S3 URL from environment variables
    if not pdf_path:
        pdf_path = get_config_value(tnc_secret_arn, region) or 'https://ai-chatbot-asset.s3.ap-southeast-1.amazonaws.com/tnc.pdf'
        print(f"Using S3 URL for Terms and Conditions PDF: {pdf_path}")

    # Use the provided caption or a default one
    if not caption:
        caption = "Please review our Terms and Conditions before proceeding with your order. After reviewing, please reply with 'I agree' to confirm your order or 'cancel' to cancel it."

    try:
        # Send the document using the WhatsAppService
        print(f"Sending document with chat_id={chat_id}, document_path={pdf_path}")
        result = await whatsapp_service.send_document(
            chat_id=chat_id,
            document_path=pdf_path,
            caption=caption
        )

        # Check if there was an error
        if "error" in result:
            print(f"ERROR sending document through WhatsAppService: {result['error']}")
            return {"status": "error", "message": result['error']}
        else:
            print(f"Document sent successfully through WhatsAppService: {result}")
            return {"status": "success", "result": result}
    except Exception as e:
        print(f"ERROR sending document: {e}")
        print(f"Exception type: {type(e)}")
        import traceback
        traceback.print_exc()
        return {"status": "error", "message": f"Error sending document: {e}"}

async def handle_whatsapp_update(update: Dict[str, Any], whatsapp_service: WhatsAppService, order_controller: OrderController) -> Dict[str, Any]:
    """
    Handle a WhatsApp update

    Args:
        update (Dict[str, Any]): WhatsApp update
        whatsapp_service (WhatsAppService): WhatsApp service
        order_controller (OrderController): Order controller

    Returns:
        Dict[str, Any]: Response
    """
    try:
        # Extract message data
        message_data = update.get('data', {})
        message = message_data.get('message', {})
        message_type = message.get('type')
        user_id = message_data.get('from')

        if not user_id or not message_type:
            return {
                'status': 'error',
                'message': 'Invalid message data'
            }

        # Handle text messages
        if message_type == 'text':
            text = message.get('text', '')
            if not text:
                return {
                    'status': 'error',
                    'message': 'Empty text message'
                }

            # Process message
            response = await order_controller.process_message(text, user_id)

            # Handle function calls
            if response.get('function_call'):
                function_call = response['function_call']
                function_name = function_call['name']
                arguments = function_call['arguments']

                if function_name == 'get_tnc_pdf':
                    # Send T&C PDF
                    pdf_url = get_config_value(tnc_secret_arn, region) or 'https://ai-chatbot-asset.s3.ap-southeast-1.amazonaws.com/tnc.pdf'
                    caption = function_call.get('result', {}).get('caption', 'Please review and agree to our Terms and Conditions before proceeding with your order.')
                    await whatsapp_service.send_document(
                        chat_id=user_id,
                        document_path=pdf_url,
                        caption=caption
                    )

            # Send text response
            if response.get('response'):
                await whatsapp_service.send_message(
                    chat_id=user_id,
                    message_body=response['response']
                )

            return {
                'status': 'success',
                'message': 'Message processed successfully'
            }

        return {
            'status': 'error',
            'message': f'Unsupported message type: {message_type}'
        }

    except Exception as e:
        print(f"Error processing WhatsApp update: {e}")
        return {
            'status': 'error',
            'message': str(e)
        }

@app.post("/webhook/telegram")
async def handle_telegram_update(update: Dict[str, Any]):
    """
    Handle incoming Telegram updates
    """
    try:
        # Extract message data
        message = update.get('message', {})
        user_id = str(message.get('from', {}).get('id'))
        message_text = message.get('text', '')

        # Handle special commands
        if message_text.startswith('/'):
            command = message_text[1:].lower()
            if command == 'history':
                response = order_controller.get_order_history(user_id)
                await telegram_service.send_message(user_id, response)
                return {"status": "ok"}
            elif command == 'clear':
                response = order_controller.clear_session(user_id)
                await telegram_service.send_message(user_id, response)
                return {"status": "ok"}
            elif command == 'export':
                response = order_controller.export_order_history(user_id)
                await telegram_service.send_message(user_id, response)
                return {"status": "ok"}
            elif command == 'help':
                help_text = (
                    "Available commands:\n"
                    "/history - View your order history\n"
                    "/clear - Clear your current session\n"
                    "/export - Export your order history\n"
                    "/help - Show this help message"
                )
                await telegram_service.send_message(user_id, help_text)
                return {"status": "ok"}

        # Process regular message
        response = await order_controller.process_message(message_text, user_id)
        
        # Send response back to user
        if response.get('function_call'):
            function_call = response['function_call']
            if function_call['name'] == 'get_tnc_pdf':
                # Send PDF first
                pdf_path = function_call['result']['path']
                caption = function_call['result']['caption']
                await telegram_service.send_pdf(user_id, pdf_path, caption)
                # Then send the text response
                await telegram_service.send_message(user_id, response['response'])
            else:
                # Send the text response
                await telegram_service.send_message(user_id, response['response'])
        else:
            # Send regular text response
            await telegram_service.send_message(user_id, response['response'])

        return {"status": "ok"}

    except Exception as e:
        print(f"Error processing Telegram update: {str(e)}")
        return {"status": "error", "message": str(e)}

# Health check endpoint is already defined above

@app.post("/reload-prompt")
async def reload_prompt():
    """
    Reload the system prompt from the file
    """
    result = prompt_service.reload_system_prompt(force=True)
    return result

@app.get("/logs")
async def list_logs():
    """
    List available log files
    """
    log_dir = get_config_value(log_secret_arn, region) or 'logs'
    if not os.path.exists(log_dir):
        return {"status": "error", "message": "Log directory does not exist"}

    log_files = [f for f in os.listdir(log_dir) if os.path.isfile(os.path.join(log_dir, f))]
    return {"status": "success", "log_files": log_files}

@app.get("/logs/{filename}")
async def get_log_file(filename: str):
    """
    Get the contents of a log file
    """
    log_dir = get_config_value(log_secret_arn, region) or 'logs'
    log_path = os.path.join(log_dir, filename)

    if not os.path.exists(log_path):
        return {"status": "error", "message": f"Log file {filename} does not exist"}

    try:
        with open(log_path, "r", encoding="utf-8") as f:
            content = f.read()
        return {"status": "success", "filename": filename, "content": content}
    except Exception as e:
        return {"status": "error", "message": f"Error reading log file: {e}"}

if __name__ == "__main__":
    # Run the FastAPI app with Uvicorn
    port = int(get_config_value(tnc_secret_arn, region) or 8080)
    print("Starting server with verbose logging...")
    uvicorn.run("main:app", host="0.0.0.0", port=port, reload=(get_config_value(tnc_secret_arn, region) == 'development'), log_level="debug")
