import os
import asyncio
import json
from dotenv import load_dotenv
import sys

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the necessary classes
from src.services.whatsapp_service import WhatsAppService
from src.services.logging_service import LoggingService
from src.services.prompt_service import PromptService
from src.services.order_service import OrderService
from src.services.openai_service import OpenAIService
from src.services.csv_service import CSVService
from src.controllers.order_controller import OrderController

# Sample WhatsApp webhook response
SAMPLE_WHATSAPP_UPDATE = {
    "event": "message",
    "timestamp": "2025-05-15T07:02:50.351Z",
    "data": {
        "id": "3A020F7F85FCC3ABEFBB",
        "from": "<EMAIL>",
        "pushName": "<PERSON>",
        "timestamp": 1747292570,
        "message": {
            "type": "text",
            "text": "Show me the menu"
        }
    }
}

async def test_whatsapp_webhook():
    """
    Test the WhatsApp webhook handler with a sample update
    """
    print("Testing WhatsApp webhook handler...")

    # Load environment variables
    load_dotenv(".env.local")

    # Initialize logging service
    logging_service = LoggingService(
        log_dir=os.getenv('LOG_DIR', 'logs')  # Use LOG_DIR env var or default to 'logs'
    )

    # Initialize WhatsApp service
    whatsapp_service = WhatsAppService(
        max_session_history=20,   # Store up to 20 messages per chat
        max_sessions=1000,        # Support up to 1000 different chats
        logging_service=logging_service  # Add logging service
    )

    # Initialize order service for menu and orders
    order_service = OrderService(
        menu_file_path=os.getenv('MENU_CSV_PATH', 'data/menu.csv'),
        orders_file_path=os.getenv('ORDERS_CSV_PATH', 'data/orders.csv'),
        logging_service=logging_service  # Add logging service
    )

    # Initialize prompt service
    prompt_service = PromptService(order_service=order_service)

    # Initialize OpenAI service with prompt service
    openai_service = OpenAIService(
        api_key=os.getenv('OPENAI_API_KEY'),
        prompt_service=prompt_service,
        model=os.getenv('OPENAI_MODEL', 'gpt-4.1-nano')  # Load model from env var with default
    )

    # Keep the CSV service for backward compatibility
    csv_service = CSVService(
        menu_file_path=os.getenv('MENU_CSV_PATH', 'data/menu.csv'),
        orders_file_path=os.getenv('ORDERS_CSV_PATH', 'data/orders.csv')
    )

    # Initialize controller
    order_controller = OrderController(
        messaging_service=whatsapp_service,  # Using WhatsApp service
        openai_service=openai_service,
        sheets_service=csv_service  # Using CSV service instead of Google Sheets
    )

    # Define the WhatsApp update handler function
    async def handle_whatsapp_update(update_json):
        """
        Handle a WhatsApp update

        Args:
            update_json (dict): The update from WhatsApp

        Returns:
            dict: A response to WhatsApp
        """
        # Extract message data from the update
        chat_id, _, message_text, username = whatsapp_service.extract_message_data(update_json)  # Using _ for unused user_id

        # Log the extracted data
        print(f"Extracted WhatsApp message: '{message_text}' from {username} (chat_id: {chat_id})")

        # Check if we have a valid message
        if not chat_id or not message_text:
            print("Warning: Invalid WhatsApp message data")
            return {"status": "error", "message": "Invalid message data"}

        # Process the message and get a response
        response_text = order_controller.process_message(message_text, str(chat_id))

        # Print the response for debugging
        print(f"\n===== RESPONSE TO WHATSAPP USER =====\n{response_text}\n===========================\n")

        # Send the response
        await whatsapp_service.send_message(chat_id, response_text)
        return {"status": "ok"}

    # Test with the sample update
    print(f"Testing with sample update: {json.dumps(SAMPLE_WHATSAPP_UPDATE, indent=2)}")
    result = await handle_whatsapp_update(SAMPLE_WHATSAPP_UPDATE)
    print(f"Result: {json.dumps(result, indent=2)}")

if __name__ == "__main__":
    # Run the test
    asyncio.run(test_whatsapp_webhook())
