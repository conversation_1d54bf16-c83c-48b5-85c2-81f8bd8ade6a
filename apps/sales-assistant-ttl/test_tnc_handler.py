import os
import async<PERSON>
import json
from dotenv import load_dotenv
import sys

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the necessary classes
from src.services.telegram_service import TelegramService
from src.services.logging_service import LoggingService
from src.services.prompt_service import PromptService
from src.services.order_service import OrderService
from src.services.openai_service import OpenAIService
from src.services.csv_service import CSVService
from src.controllers.order_controller import OrderController

async def test_tnc_handler():
    """Test the TNC document handling"""
    # Load environment variables
    load_dotenv(".env.local")

    # Initialize logging service
    logging_service = LoggingService(
        log_dir=os.getenv('LOG_DIR', 'logs')
    )

    # Initialize services
    telegram_service = TelegramService(
        token=os.getenv('TELEGRAM_BOT_TOKEN'),
        connection_pool_size=1,
        connect_timeout=5.0,
        read_timeout=30.0,
        max_session_history=10,
        max_sessions=10,
        logging_service=logging_service
    )

    # Initialize order service for menu and orders
    order_service = OrderService(
        menu_file_path=os.getenv('MENU_CSV_PATH', 'data/menu.csv'),
        orders_file_path=os.getenv('ORDERS_CSV_PATH', 'data/orders.csv'),
        logging_service=logging_service
    )

    # Initialize prompt service
    prompt_service = PromptService(order_service=order_service)

    # Initialize OpenAI service with prompt service
    openai_service = OpenAIService(
        api_key=os.getenv('OPENAI_API_KEY'),
        prompt_service=prompt_service,
        model=os.getenv('OPENAI_MODEL', 'gpt-4.1-nano')
    )

    # Keep the CSV service for backward compatibility
    csv_service = CSVService(
        menu_file_path=os.getenv('MENU_CSV_PATH', 'data/menu.csv'),
        orders_file_path=os.getenv('ORDERS_CSV_PATH', 'data/orders.csv')
    )

    # Initialize controller
    order_controller = OrderController(
        messaging_service=telegram_service,
        openai_service=openai_service,
        sheets_service=csv_service
    )

    # Get the chat ID from the environment variable or use a default
    chat_id = os.getenv('TEST_CHAT_ID', '642972630')  # Default to Nicholas's chat ID
    if not chat_id:
        print("ERROR: TEST_CHAT_ID environment variable not set")
        return

    # Create a mock update
    update = {
        'message': {
            'chat': {'id': int(chat_id)},
            'from': {'id': int(chat_id), 'username': 'testuser'},
            'text': 'I want to buy atlas light tower 1 unit'
        }
    }

    # Process the message
    response = order_controller.process_message(update['message']['text'], str(chat_id))
    print(f"Response: {json.dumps(response, indent=2) if isinstance(response, dict) else response}")

    # If the response is a string, try to create a TNC document intent
    if isinstance(response, str):
        # Create a mock TNC document intent
        response = {
            "intent": "tnc_document",
            "function_call": {
                "name": "get_tnc_pdf",
                "result": {
                    "status": "success",
                    "type": "document",
                    "path": os.path.abspath(os.path.join('data', 'tnc.pdf')),
                    "caption": "Please review our Terms and Conditions before proceeding with your order. After reviewing, please reply with 'I agree' to confirm your order or 'cancel' to cancel it."
                }
            },
            "response": "Here's your order summary:\n\n• 1x Atlas Copco Light Tower 320W - Purchase: RM100.00\n\nTotal: RM100.00"
        }

    # Check if the response is a dictionary with a special intent
    if isinstance(response, dict) and 'intent' in response:
        intent = response.get('intent')

        print(f"\n===== PROCESSING INTENT: {intent} =====")

        # Handle TNC document intent
        if intent == 'tnc_document' and 'function_call' in response:
            function_result = response['function_call']['result']
            print(f"Function result: {json.dumps(function_result, indent=2)}")

            # First, send the order summary as a regular message
            order_summary = response.get('response', '')
            if order_summary:
                print(f"\n===== SENDING ORDER SUMMARY =====")
                print(f"Order summary: {order_summary}")

                # Send the order summary asynchronously
                await telegram_service.send_message(
                    chat_id=chat_id,
                    message_body=order_summary
                )
                print(f"Order summary sent successfully")

                # Now, immediately send the PDF document
                if 'path' in function_result and os.path.exists(function_result['path']):
                    document_path = function_result['path']
                    # Make sure the path is absolute
                    if not os.path.isabs(document_path):
                        document_path = os.path.abspath(document_path)

                    caption = function_result.get('caption', "Please review our Terms and Conditions before proceeding with your order. After reviewing, please reply with 'I agree' to confirm your order or 'cancel' to cancel it.")

                    print(f"\n===== SENDING TNC DOCUMENT IMMEDIATELY =====")
                    print(f"Sending document: {document_path}")
                    print(f"Caption: {caption}")
                    print(f"File exists: {os.path.exists(document_path)}")
                    print(f"File size: {os.path.getsize(document_path)} bytes")
                    print(f"File readable: {os.access(document_path, os.R_OK)}")

                    try:
                        # Send the document directly
                        result = await telegram_service.send_document(
                            chat_id=chat_id,
                            document_path=document_path,
                            caption=caption
                        )
                        print(f"Document sent successfully: {result}")

                        # Mark TNC as shown in the session
                        try:
                            user_id = str(chat_id)
                            session = order_controller._get_or_create_session(user_id)
                            session['tnc_shown'] = True
                            print(f"Marked TNC as shown for user {user_id}")
                        except Exception as e:
                            print(f"Error updating session: {e}")
                    except Exception as e:
                        print(f"ERROR sending document: {e}")
                        # Fallback to text message if document sending fails
                        error_msg = f"Could not send the Terms and Conditions document: {str(e)}. Please reply with 'I agree' to confirm your order or 'cancel' to cancel it."
                        await telegram_service.send_message(chat_id, error_msg)
                else:
                    print(f"PDF path not found in function result or file doesn't exist")
            else:
                print(f"WARNING: No order summary found in response: {response}")
        else:
            # For other intents, just send the response text
            await telegram_service.send_message(chat_id, response.get('response', 'Sorry, I could not process your request.'))
    else:
        # Regular text response
        await telegram_service.send_message(chat_id, response)

if __name__ == "__main__":
    # Run the async function
    asyncio.run(test_tnc_handler())
