FROM python:3.9-slim

# Set working directory
WORKDIR /app

# Set environment variables with defaults
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    LOG_DIR="/app/logs" \
    DATA_DIR="/app/data" \
    PORT=8080 \
    HOST="0.0.0.0" \
    OPENAI_MODEL="gpt-4.1-nano"

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    curl \
    build-essential \
    libffi-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements file
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy project files
COPY . .

# Create logs and data directories
RUN mkdir -p ${LOG_DIR} ${DATA_DIR}

# Expose port for the application
EXPOSE ${PORT}

# Command to run the application
CMD ["python", "run.py"]
