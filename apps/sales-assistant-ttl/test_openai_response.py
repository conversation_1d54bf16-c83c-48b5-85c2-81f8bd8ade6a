import os
import json
import asyncio
from dotenv import load_dotenv
import sys

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the necessary services
from src.services.openai_service import OpenAIService
from src.services.prompt_service import PromptService
from src.services.order_service import OrderService
from src.services.logging_service import LoggingService

async def test_openai_response():
    """Test the OpenAI response for a buy message"""
    # Load environment variables
    load_dotenv(".env.local")
    
    # Initialize logging service
    logging_service = LoggingService(
        log_dir=os.getenv('LOG_DIR', 'logs')
    )
    
    # Initialize order service for menu and orders
    order_service = OrderService(
        menu_file_path=os.getenv('MENU_CSV_PATH', 'data/menu.csv'),
        orders_file_path=os.getenv('ORDERS_CSV_PATH', 'data/orders.csv'),
        logging_service=logging_service
    )
    
    # Initialize prompt service
    prompt_service = PromptService(order_service=order_service)
    
    # Initialize OpenAI service with prompt service
    openai_service = OpenAIService(
        api_key=os.getenv('OPENAI_API_KEY'),
        prompt_service=prompt_service,
        model=os.getenv('OPENAI_MODEL', 'gpt-4.1-nano')
    )
    
    # Test message
    message = "I want to buy atlas light tower 1 unit"
    
    # Get menu items
    menu_items = order_service.get_menu_items()
    
    # Analyze the message using OpenAI
    print(f"Analyzing message: {message}")
    analysis_json = openai_service.analyze_message(
        message=message,
        menu_items=menu_items,
        conversation_history=[]
    )
    
    # Print the analysis
    print(f"Analysis: {json.dumps(analysis_json, indent=2)}")
    
    # Check if the analysis contains the TNC document intent
    if isinstance(analysis_json, dict) and analysis_json.get('intent') == 'tnc_document':
        print("SUCCESS: Analysis contains TNC document intent")
        
        # Check if the function call is correct
        if 'function_call' in analysis_json and analysis_json['function_call'].get('name') == 'get_tnc_pdf':
            print("SUCCESS: Function call is correct")
            
            # Check if the result is correct
            if 'result' in analysis_json['function_call']:
                print("SUCCESS: Result is present")
                
                # Check if the response is correct
                if 'response' in analysis_json:
                    print("SUCCESS: Response is present")
                    print(f"Response: {analysis_json['response']}")
                else:
                    print("ERROR: Response is missing")
            else:
                print("ERROR: Result is missing")
        else:
            print("ERROR: Function call is incorrect or missing")
    else:
        print("ERROR: Analysis does not contain TNC document intent")
        
        # Try to parse the response
        try:
            if isinstance(analysis_json, str):
                parsed = json.loads(analysis_json)
                print(f"Parsed response: {json.dumps(parsed, indent=2)}")
            else:
                print(f"Response is already a dict: {json.dumps(analysis_json, indent=2)}")
        except Exception as e:
            print(f"Error parsing response: {e}")

if __name__ == "__main__":
    # Run the async function
    asyncio.run(test_openai_response())
