version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "${PORT:-8080}:${PORT:-8080}"
    volumes:
      - ./data:/app/data  # Volume for menu.csv, orders.csv and other data files
      - ./logs:/app/logs  # Volume for log files
    env_file:
      - .env
    # You can override environment variables here if needed
    # environment:
    #   - OPENAI_MODEL=gpt-4.1-nano
    #   - LOG_LEVEL=DEBUG
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${PORT:-8080}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
