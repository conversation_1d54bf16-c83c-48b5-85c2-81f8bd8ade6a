/*******************************************************/
/**************** 21. Subscribe Section ****************/
/*******************************************************/
.subscribe-form
    padding: 2px
    border-radius: 5px
    +flexcenter(center)
    border: 2px solid #88a6dd
    +sm-d
        display: block
    input
        border: none
        color: white
        opacity: 0.8
        background: transparent
        padding: 12px 10px 12px 30px
        &::placeholder
            color: white
            opacity: 0.8
    button
        font-weight: 500
        background: white
        border-radius: 5px
        padding: 11px 45px
        color: $heading-color
        +sm-d
            width: 100%
        
/* Subscribe Form Two */
.subscribe-form-two
    padding: 5px
    display: flex
    background: white
    border-radius: 5px
    align-items: center
    +sm-d
        display: block
        margin-right: 0
    input
        border: none
        background: transparent
    .theme-btn
        flex: none
        padding: 13px 28px
        background: #FF6600
        margin-right: -50px
        +sm-d
            width: 100%
            margin-right: 0
.subscribe-two
    .shape-dots,
    .shape-stones
        width: 15%
        max-width: 85px
        position: absolute
    .shape-dots
        top: 50px
        right: 10%
        animation: bounce 10s infinite linear
    .shape-stones
        animation: shake 15s infinite linear
        
/* Newsletter */
.newsletter-inner
    background: #f4f4f4
    padding-right: 90px
    +tb-d()
        padding-top: 25px
    +mo-d
        padding-right: 25px
.newsletter-images
    text-align: left
    .shape.one
        left: 5%
        top: 15%
    .shape.four
        right: 15%
.newsletter-content
    +tb-d()
        padding: 25px 0 25px 90px
    +mo-d()
        padding-left: 25px
    p
        font-size: 18px
        padding: 5px 15px
        background: white
        border-radius: 5px
        margin-bottom: 40px
        color: $primary-color
        display: inline-block
        +sm-d()
            font-size: 16px
    form
        .form-group
            display: flex
            margin-bottom: 10px
            input
                border: none
                border-radius: 0
                margin-right: 30px
                background: transparent
                border-bottom: 1px solid #dddddf
                &::placeholder
                    color: $heading-color
            button
                flex: none
                +sm-d()
                    padding-left: 13px
                    span
                        display: none
        .radio
            display: flex
            align-items: center
            input
                margin-right: 7px
            label
                font-weight: 300
                margin-bottom: 0