/*******************************************************/
/***************** 10. Services Section ****************/
/*******************************************************/
.service-box
    margin-bottom: 30px
    border-radius: 5px
    position: relative
    overflow: hidden
    &:hover
        .service-normal
            transform: translateY(100%);
        .service-hover
           bottom: 0
.service-normal,
.service-hover
    padding: 25px
    border-radius: 5px
    
.service-normal
    transition: 0.5s
    border: 1px solid #e7edf8
    .icon
        i
            @extend %icon
    h6
        margin-bottom: 15px
    .btn-circle
        margin-top: 10px
        color: $primary-color
        display: inline-block
        +circle($light-color, 33px)

.service-hover
    position: absolute
    +size(100%)
    left: 0
    bottom: 100%
    flex-wrap: wrap
    +flexcenter(start)
    transition: all 500ms ease
    h3
        margin-bottom: 20px
    .theme-btn
        display: block
        margin-top: 30px
        padding: 10px 20px
        background: #285ec1
    
.service-normal.style-two
    text-align: center
    margin-bottom: 20px
    padding: 25px 15px 10px
    
/* Service box Style Two */
.service-box.style-two
    overflow: inherit
    margin-top: 20px
    margin-bottom: 0
    .service-normal
        border: none
        padding: 40px
        +xs-d()
            +gapboth(padding, 25px)
        .icon i
            font-size: 55px
            margin-bottom: 25px
        h3
            font-size: 24px
            margin-bottom: 15px
        .theme-btn
            margin-top: 15px
            padding: 12px 25px
            i
                font-size: 14px
            &:hover
                color: white
                background: $primary-color
                border-color: $primary-color
        &:after
            content: ''
            +size(112px, 120px)
            position: absolute
            top: 30px
            right: 30px
            z-index: -1
            opacity: 0
            transition: 0.5s
            background: url(../images/services/service-bg-shape.png)
            background-size: 100% 100%
    .circle-shapes-wrap
        width: 120%
        position: absolute
        padding-bottom: 120%
        z-index: -1
        left: -10%
        top: -10%
        opacity: 0
        transition: 0.5s
        animation: rounded 30s linear infinite
        &:after,
        &:before
            content: ''
            +size(10px)
            position: absolute
            border-radius: 50%
            animation: zoomInOutTwo 4s infinite
        &:before
            left: 15%
            top: 3%
            background: #fd01f4
            animation-delay: 1s
        &:after
            right: 1%
            top: 20%
            animation-delay: 3s
            background: $primary-color
        .circle-shape
            +size(100%)
            position: absolute
            border-radius: 50%
            border: 1px solid #e0e0e1
            animation-duration: 1s
            animation-fill-mode: both
            &:after,
            &:before
                content: ''
                +size(10px)
                position: absolute
                border-radius: 50%
                animation: zoomInOutTwo 4s infinite
            &:before
                left: 2%
                bottom: 15%
                background: $orange-color
            &:after
                bottom: 6%
                right: 15%
                animation-delay: 2s
                background: #fd01f4
    &:hover,
    &.active
        .service-normal
            background: white
            transform: translateY(0)
            box-shadow: 10px 0 60px rgba(109, 109, 109, 0.15)
            &:after
                opacity: 1
        .circle-shapes-wrap
            opacity: 1
            .circle-shape
                animation-name: zoomIn
        
/* Service Style Two */
.service-two-wrap
    z-index: 1
    position: relative
    margin-bottom: -40px
    transform: translateY(-40%)
    +md-d
        transform: translateY(-80px)
.service-style-two
    background: white
    text-align: center
    border-radius: 5px
    margin-bottom: 30px
    padding: 25px 15px 15px
    .icon
        i
            @extend %icon
        
/* Service Style Three */
.services-three
    z-index: 1
    position: relative
    &:after
        content: ''
        height: 90%
        width: 40%
        right: 0
        bottom: 0
        z-index: -1
        position: absolute
        background: url(../images/about/about-bg-shape.png) no-repeat bottom
        +tb-d
            transform: translateY(100px)
    
.service-three-item
    margin-bottom: 35px
    .icon
        +circle(white, 105px)
        border-radius: 5px
        margin-bottom: 30px
        i
            @extend %icon
            margin: 10px 0 0
    h4
        margin-bottom: 20px
    p
        opacity: 0.6
        
/* Service Style Four */
.service-item-four
    z-index: 1
    position: relative
    margin-bottom: 30px
    padding: 0 25px 20px
    img
        width: 100%
        border-radius: 5px
    &:before
        position: absolute
        content: ''
        height: 90%
        width: 100%
        left: 0
        bottom: 0
        z-index: -1
        border-radius: 5px
        background: white
        box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.07)
.service-four-content
    margin-top: 20px
    +flexcenter(space-between)
    i
        font-size: 50px
        color: $primary-color
.service-title-area
    margin-right: 15px
    .category
        color: $primary-color
    h3
        font-size: 24px
        margin-bottom: 0
        font-weight: 500
        +mo-d
            font-size: 18px
        
/* Service Five */
.who-we-are.overlay
    &:before
        opacity: 1
        height: 130%
        background: $light-black-color
        +tb-d
            height: 110%
.service-item-five
    margin-bottom: 30px
    img
        width: 100%
    &:hover
        .service-content-five
            background: white
            border-color: white
            .icon i
                color: $primary-color
            h3
                color: $heading-color
            p
                color: $base-color
.service-content-five
    padding: 35px
    transition: 0.5s
    border: 1px solid #273540
    .icon
        font-size: 45px
        margin-bottom: 15px
    i, p, h3
        transition: 0.5s
    .theme-btn
        width: 100%
        margin-top: 8px
        padding-top: 9px
        padding-bottom: 9px
        justify-content: center
        
/* Service Six */
.service-item-six
    display: flex
    max-width: 400px
    margin-bottom: 30px
    .icon
        flex: none
        color: #2F97F7
        font-size: 25px
        padding-top: 3px
        margin-right: 20px
        +circle(#2F97F71f, 60px)
    .service-content
        h5
            margin-bottom: 15px
    &:last-child
        margin-bottom: 0
        .icon
            color: #9E59FF
            background: #9E59FF1f
        
/* Service Seven */
.services-seven
    background-position: center
    .circle-drop-one
        top: 15%
        left: 10%
        position: absolute
        +circle(#2969E6, 15px)
        animation: zoomInOut 3s linear infinite
    .circle-drop-two
        bottom: 10%
        left: 30%
        position: absolute
        +circle($orange-color, 10px)
        animation: zoomInOut 2s linear infinite
    .service-triangle
        top: 20%
        right: 20%
        position: absolute
        animation: shapeAnimationTwo 20s linear infinite
    .service-line
        bottom: 15%
        right: 10%
        position: absolute
        animation: down-up-one 5s linear infinite
.service-item-seven
    padding: 50px
    margin-bottom: 30px
    background: #FFFFFF
    box-shadow: 0px 20px 70px rgba(0, 15, 92, 0.07)
    +mo-d
        +gapboth(padding, 25px)
    h3
        font-size: 24px
        font-weight: 500
        margin: 30px 0 30px
        
.more-about-services
    font-weight: 600
    text-decoration: underline 
    
/* Service Eight */
.services-eight
    &:after,
    &:before
        position: absolute
        +size(100%)
        content: ''
        left: 0
        top: 0
        z-index: -1
    &:before
        filter: brightness(0.5)
        background: url(../images/services/service-eight-bg.png) no-repeat center/cover
    &:after
        opacity: 0.95
        background: #F8F8F8
.service-slider-wrap
    +gapboth(margin, -15px)
    .slick-dots
        margin-top: 20px
        +flexcenter(center)
        li
            cursor: pointer
            overflow: hidden
            transition: 0.5s
            position: relative
            +circle(transparent, 22px)
            border: 1px solid transparent
            button
                text-indent: 100px
                background: transparent
            &:before
                content: ''
                +size(5px)
                left: 50%
                top: 50%
                position: absolute
                border-radius: 50%
                background: $red-color
                transform: translate(-50%, -50%)
            &.slick-active
                border-color: $red-color
                &:before
                    background: $red-color
.service-item-eight
    padding: 30px
    transition: 0.5s
    background: white
    margin-bottom: 30px
    border-radius: 5px
    +gapboth(margin, 15px)
    .icon
        font-size: 50px
        color: $red-color
        margin-bottom: 22px
        +circle(#F8F8F8, 100px)
        img
            display: inline-block
        i:before
            line-height: inherit
    .learn-more
        font-weight: 600
        i
            float: right
            font-size: 16px
            color: $red-color
    p
        transition: 0.5s
    &:hover
        background: #030A15
        h3 a, p
            color: white
        .learn-more
            color: $red-color
            &:hover
                text-decoration: underline

/* Service Details */
.service-details-content
    h2, h3, h4, h5, h6, p
        margin-bottom: 15px
    h2
        font-size: 30px
        font-weight: 500
    h3
        font-size: 24px
        font-weight: 500
    .list-style-three
        flex-wrap: wrap
        +flexcenter(space-between)
        li
            width: 45%
            margin-bottom: 15px
            +sm-d
                width: 100%
    .row
        +colgap(20px)
        