/*******************************************************/
/************** 19. Testimonials Section ***************/
/*******************************************************/
.testimonial-wrap
    +gapboth(margin, -15px)
.testimonial-item
    background: white
    +gapboth(margin, 15px)
    padding: 40px 45px 20px
    box-shadow: 0px 0px 25px 0px rgba(0, 0, 0, 0.05)
    +sm-d
        +gapboth(padding, 15px)
.author-description
    +flexcenter(start)
    margin-bottom: 22px
    img
        flex: none
        +circle(white, 80px)
        margin-right: 22px
        +xs-d
            +size(50px)
    .designation
        margin-right: 20px
        h5
            margin: 3px 0 6px
    i
        opacity: 0.5
        font-size: 50px
        margin-left: auto
        color: $primary-color
        +xs-d
            display: none
        
/* Testimonial Two */
.testimonial-section-two
    .container
        max-width: 900px
.testimonial-two-wrap
    border: 1px solid rgba(0,0,0,.125)
    .row
        +colgap(0)
.testimonial-two-content
    +sm-d
        +gapboth(padding, 25px)
.testimonial-two-active
    .author-description
        margin-top: 22px
        img
            +size(60px)
            +xs-d
                +size(50px)
        .designation
            span
                font-weight: 500
                color: $primary-color
            h5
                +xs-d
                    font-size: 16px
    .slick-dots
        display: flex
        margin-top: 10px
        li
            +size(15px)
            overflow: hidden
            transition: 0.5s
            margin-right: 10px
            border-radius: 50%
            border: 3px solid #dee7f6
            button
                background: transparent
                text-indent: 999px
            &.slick-active
                border-color: $primary-color
    
/* Testimonial Three */
.testimonial-three-wrap
    overflow: hidden
    border-radius: 20px
.testimonial-three-content
    +sm-d
        +gapboth(padding, 25px)
.testimonial-three-active
    .designation
        padding-top: 110px
.testimonial-three-thumbs
    display: inline-block
    margin-top: -110px
    margin-bottom: 0
    bottom: 95px
    +mo-d
        bottom: 100px
    +sm-d
        bottom: 120px
    img
        +size(70px)
        margin: 10px
        cursor: pointer
        transition: 0.5s
        border-radius: 50%
        border: 2px solid transparent
        +sm-d
            +size(50px)
        &.slick-current
            border-color: $primary-color
            
/* Testimonial Four */
.testimonial-four-active
    +gapboth(margin, -15px)
    &.slick-initialized
        .slick-track
            display: flex
        .slick-slide
            display: flex
            flex-direction: column
    .slick-dots
        margin-top: 60px
        +flexcenter(center)
        +tb-d
            margin-top: 20px
        li
            margin: 5px
            +size(30px)
            overflow: hidden
            transition: 0.5s
            position: relative
            border-radius: 50%
            border: 1px solid transparentize(#000F5C, 0.8)
            &:before
                position: absolute
                content: ''
                +size(10px)
                left: 9px
                top: 9px
                opacity: 0
                transition: 0.5s
                border-radius: 50%
                background: #2969E6
            &.slick-active
                border-color: #2969E6
                &:before
                    opacity: 1
            button
                text-indent: 100px
                background-color: transparent
.testimonial-four-item
    margin: 15px
    float: none
    height: auto
    box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.08)
.testimonial-four-content
    padding: 50px 40px 40px
    +xs-d
        +gapboth(padding, 25px)
.testimonial-four-author
    margin-top: auto
    position: relative
    padding: 0 40px 0 15px
    display: flex
    align-items: end
    justify-content: space-between
    +xs-d
        display: block
        +gapboth(padding, 25px)
    &:after
        top: -15px
        right: 40px
        opacity: 0.1
        color: #000F5C
        font-weight: 600
        content: "\f10e"
        font-size: 75px
        position: absolute
        font-family: "Font Awesome 5 Free"
    img
        width: 50%
    h4
        margin-bottom: 0
    span
        font-size: 14px
        font-weight: 600
.testimonial-four-author-designation
    padding: 45px 0 15px 10px
    +xs-d
        padding: 20px 0 45px
    
/* Testimonial Five */
.testimonial-five
    +overlay(#030A15, 0.75)
.testimonial-five-wrap
    z-index: 1
    margin-bottom: -55px
    box-shadow: 0px 10px 70px rgba(0, 0, 0, 0.1)
    &:before
        top: 50px
        z-index: -1
        right: 100px
        opacity: 0.05
        line-height: 1
        color: #030A15
        content: "\f10e"
        font-weight: 600
        font-size: 150px
        position: absolute
        font-family: 'Font Awesome 5 Free'
        +ms-d
            right: 50px
            font-size: 100px
    +ms-d
        +gapboth(padding, 25px)
    .slick-arrow
        z-index: 1
        right: 70px
        bottom: 90px
        font-size: 20px
        transition: 0.5s
        position: absolute
        +circle(#F8F8F8, 60px)
        &:hover, &:focus
            color: white
            background: $red-color
        &.prev
            right: 140px
        +ms-d
            +size(40px)
            right: 25px
            font-size: 18px
            line-height: 40px
            &.prev
                right: 75px
.testimonial-five-item
    p
        font-size: 20px
        line-height: 1.7
        +xs-d
            font-size: 16px
    .author-description
        margin-bottom: 0
        padding-top: 25px
        padding-right: 150px
        +ms-d
            padding-right: 100px
        +sm-d
            display: block
        img
            +size(100px)
            +sm-d
                margin-bottom: 10px
        h3
            font-size: 24px
            margin-bottom: 0
            +sm-d
                font-size: 20px
        span
            font-size: 14px
            color: $red-color
            
/* Testimonial Six */
.testimonial-six-item
    padding: 40px
    position: relative
    border-radius: 5px
    margin-bottom: 30px
    background: #1B2429
    border: 1px solid rgba(255, 255, 255, 0.15)
    +xs-d
        +gapboth(padding, 25px)
    .author-description
        margin-bottom: 0
        margin-top: 33px
        justify-content: space-between
        h3
            margin-bottom: 0
            +xs-d
                font-size: 18px
        span
            color: #CACCCF
            font-size: 12px
        img
            margin-right: 0
    &:before
        right: 25%
        bottom: 22%
        opacity: 0.1
        line-height: 1
        font-size: 55px
        content: "\f10e"
        font-weight: 600
        position: absolute
        font-family: 'Font Awesome 5 Free'
.testi-author-images
    display: flex
    > *
        +size(60px)
        border-radius: 50%
        border: 4px solid #1B2429
        &:not(:first-child)
            margin-left: -30px
    .plus
        color: white
        +flexcenter(center)
        background: transparentize($primary-color, 0.2)
        
/* Testimonial Seven */
.testimonials-area-seven
    +overlay(#3146f5, 0.92)
.testi-seven-left
    height: 100%
    min-height: 400px
.testimonial-seven-slider
    background: white
    padding: 55px 60px
    +sm-d()
        +gapboth(padding, 25px)
    .slick-arrow
        +size(55px)
        line-height: 55px
        text-align: center
        color: $heading-color
        background: #e6e5ec
        position: absolute
        top: 0
        right: 0
        transition: 0.5s
        &.prev
            right: 56px
        &:focus,
        &:hover
            color: white
            background: $heading-color
    .slick-dots
        bottom: -70px
        display: flex
        position: absolute
        li
            +size(8px)
            cursor: pointer
            background: white
            position: relative
            border-radius: 50%
            +gapboth(margin, 10px)
            button
                overflow: hidden
                text-indent: -200px
                background: transparent
            &:after
                content: ''
                +size(25px)
                left: -9px
                top: -9px
                transition: 0.5s
                position: absolute
                border-radius: 50%
                transform: scale(0)
                border: 2px solid white
            &.slick-active
                &:after
                    transform: scale(1)
        
.testimonial-seven-item
    .image
        margin-bottom: 20px
        img
            +size(77px)
            border-radius: 50%
    .author
        display: flex
        .icon
            line-height: 1
            font-size: 45px
            margin-top: 5px
            margin-right: 20px
            color: $primary-color
            +xs-d()
                font-size: 40px
                margin-right: 15px
        .title
            h4
                margin-bottom: 0
                +xs-d()
                    font-size: 18px
            span
                font-size: 14px
                
.testimonials-shapes
    .shape
        position: absolute
        z-index: -1
        max-width: 10%
        &.one
            left: 10%
            top: 20%
            animation: down-up-two 10s infinite
        &.two
            left: 8%
            bottom: 15%
            animation: moveLeftRight 10s infinite
        &.three
            top: 15%
            right: 8%
            animation: zoomInOutTwo 10s infinite
        &.four
            right: 6%
            bottom: 25%
            animation: down-up-one 10s infinite