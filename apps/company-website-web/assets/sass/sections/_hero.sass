/*******************************************************/
/******************* 07. Hero Section ******************/
/*******************************************************/
.hero-section
    z-index: 1
    overflow: hidden
    position: relative
    .left-circle
        position: absolute
        left: 0
        bottom: 0
        z-index: -1
        border: 3px solid #39446b
        +circle(transparent, 400px)
        transform: translate(-40%, 40%)
        animation: hero_circle 30s infinite
        +md-d
            left: auto
            right: -150px
        &:after
            position: absolute
            content: ''
            top: 50%
            right: -32px
            +circle($primary-color, 65px)
.hero-content
    .sub-title
        font-size: 18px
        font-weight: 500
    h1
        +md-d
            font-size: 60px
        +tb-d
            font-size: 50px
        +mo-d
            font-size: 40px
        +sm-d
            font-size: 35px
        +xs-d
            font-size: 30px
        
.hero-image
    max-width: 450px
    margin-left: -65px
    position: relative
    +md-d
        margin-left: 0
    img
        border-radius: 200px 200px 0 0
    &:before
        content: ''
        +size(100%)
        right: -30px
        position: absolute
        border-radius: 200px 200px 0 0
        border: 4px solid $primary-color
    &:after
        content: ''
        right: 0
        top: 50%
        opacity: 0.05
        position: absolute
        transform: translate(50%)
        +circle(#e5e5e5, 175px)
    .circle-one
        +circle($primary-color, 90px)
        position: absolute
        left: 30px
        top: 15px
    .circle-two
        top: 35%
        right: -45px
        position: absolute
        +circle(white, 30px)

/* Hero Two */
.hero-section-two
    &:before
        background: $dark-blue-color
    
/* Hero Three */
.hero-section-three
    &:before
        opacity: 1
        background: linear-gradient(to right, #060020, transparent)
.hero-line-shape
    position: absolute
    right: 0
    bottom: 0
    width: 30%
    z-index: -1
.hero-section-form
    +sm-d
        +gapboth(padding, 25px)
    
/* Hero Five*/
.hero-content-five
    .sub-title
        font-weight: 500
        color: $primary-color
    h1
        +xl-d
            line-height: 1
            font-size: 85px
        +tb-d
            font-size: 62px
        +mo-d
            font-size: 45px
        +sm-d
            font-size: 40px
    .list-style-one
        li
            font-size: 18px
            font-weight: 400
            +mo-d
                font-size: 16px
            &:before
                +size(25px)
                margin-top: 0
                font-size: 14px
                line-height: 25px
.hero-five-image
    position: absolute
    +size(50vw, 100%)
    top: 0
    right: 0
    z-index: -1
    +ms-d
        display: none
/* Hero Six */
.hero-section-six
    +xl-d
        padding-top: 180px
        padding-bottom: 100px
    +mo-d
        padding-bottom: 65px
.hero-content-six
    +mo-d
        padding-bottom: 35px
    .sub-title
        font-size: 18px
        text-transform: uppercase
        color: $primary-color-two
    p
        font-size: 18px
        line-height: 30px
    h1
        text-transform: capitalize
        +md-d
            font-size: 60px
        +tb-d
            font-size: 52px
        +ms-d
            font-size: 44px
        +sm-d
            font-size: 38px
    .video-play
        i
            font-size: 16px
            transition: 0.5s
            background: transparent
            color: $primary-color-two
            border: 1px solid $primary-color-two
        &:hover
            i
                color: white
                background: $primary-color-two
.hero-six-images
    padding-left: 5%
    position: relative
    +xl-d
        margin-right: -175px
    img
        animation-duration: 0.5s
        animation-fill-mode: both
        &:nth-child(1)
            width: 96%
            animation: bounce 15s infinite linear
        &:nth-child(2)
            width: 52%
            display: block
            margin: -35% 5% 0 auto
            animation: shake 15s infinite linear
        &:nth-child(3)
            left: 0
            width: 39%
            bottom: 20%
            position: absolute
            animation: bounce 5s infinite linear
        &:nth-child(4)
            top: 8%
            right: 0
            width: 80%
            z-index: -1
            position: absolute
            animation: down-up-two 5s infinite linear
.hero-shapes
    img
        z-index: -1
        position: absolute
        &:nth-child(1)
            left: 0
            top: 35%
            max-width: 10%
            animation-delay: 0.5s
            animation: down-up-one 5s infinite linear
        &:nth-child(2)
            left: 18%
            bottom: 12%
            animation: shapeAnimationTwo 20s infinite linear
        &:nth-child(3)
            top: 20%
            left: 45%
            animation: shapeAnimationThree 20s infinite linear
        &:nth-child(4)
            right: 33%
            bottom: 12%
            animation-delay: 0.5s
            animation: shapeAnimationFour 20s infinite linear
        &:nth-child(5)
            top: 15%
            right: 10%
            animation-delay: 0.5s
            animation: shapeAnimationFive 20s infinite linear
            
/* Hero Seven */
.hero-section-seven
    background-position: center
    +lg-d
        padding-top: 100px
        padding-bottom: 50px
    +tb-d
        padding-bottom: 150px
.hero-content-seven
    max-width: 585px
    +md-d
        max-width: 500px
    +tb-d
        max-width: 635px
        padding-top: 75px
        margin-bottom: 35px
    h1
        font-size: 90px
        font-weight: 500
        line-height: 1.11
        margin-bottom: 25px
        +lg-d
            font-size: 80px
        +md-d
            font-size: 70px
        +mo-d
            font-size: 60px
        +ms-d
            font-size: 55px
        +sm-d
            font-size: 50px
        +xs-d
            font-size: 42px
    p
        font-size: 20px
        line-height: 1.7
.hero-right-part
    position: absolute
    bottom: 50%
    width: 45vw
    right: 0
    transform: translateY(50%)
    +md-d
        width: 50vw
    +tb-d
        width: 90%
        position: relative
        transform: translate(15px)
    .data-item
        display: flex
        max-width: 260px
        background: white
        padding: 25px 30px
        position: absolute
        align-items: center
        border-radius: 10px
        box-shadow: 0px 10px 50px rgba(0, 0, 0, 0.05)
        +sm-d
            max-width: 215px
            padding: 15px 20px
        &.one
            left: 15%
            top: 0
            +xs-d
                top: -25px
        &.two
            left: 0
            top: 42%
        &.three
            left: 15%
            top: 95%
        img
            flex: none
            margin-right: 20px
        h4
            margin-bottom: 0
            +sm-d
                font-size: 16px
            
/* Hero Eight */
.hero-section-eight
    position: relative
    z-index: 1
    &:before
        left: 0
        top: 0
        content: ''
        opacity: 0.2
        +size(500px)
        z-index: -1
        position: absolute
        filter: blur(250px)
        background: $green-color
        transform: translate(-25%, -50%)
.hero-content-eight
    h1
        font-size: 60px
        +md-d
            font-size: 50px
        +mo-d
            font-size: 45px
        +sm-d
            font-size: 35px
        +xs-d
            font-size: 30px
    p
        line-height: 2
        font-size: 18px
    .hero-btns
        .theme-btn
            margin-right: 12px
    
            
.hero-eight-image
    z-index: 1
    text-align: right
    position: relative
    margin-right: -100px
    +md-d
        margin-right: 0
    +tb-d
        text-align: center
    &:before
        right: 0
        content: ''
        bottom: -20%
        +size(500px)
        z-index: -1
        opacity: 0.1;
        background: white
        position: absolute
        filter: blur(100px)
        +tb-d
            right: 50%
            transform: translate(50%)
            
.hero-shape-one
    position: absolute
    bottom: 15%
    left: 10%
    z-index: -1
    animation: shapeAnimationTwo 20s infinite linear
.hero-shape-two
    position: absolute
    top: 25%
    left: 50%
    z-index: -1
    animation: shapeAnimationThree 20s infinite linear
    
/* Hero Ten */
.hero-content-ten
    .sub-title
        font-size: 18px
        font-weight: 500
        color: $primary-color
    h1
        font-size: 65px
        +md-d
            font-size: 50px
        +mo-d
            font-size: 45px
        +sm-d
            font-size: 35px
        +xs-d
            font-size: 30px
    p
        font-size: 18px
        max-width: 450px
        line-height: 1.75
        
.images-with-shapes
    z-index: 1
    position: relative
    text-align: center
    .mobile
        width: 44%
        border-radius: 40px
        box-shadow: 10px 0 60px rgba(109, 109, 109, 0.15)
    .shape
        position: absolute
        &.one
            left: -12%
            top: 30%
            max-width: 35%
            animation: down-up-two 8s infinite
        &.two
            left: 8%
            bottom: 22%
            max-width: 25%
            animation: down-up-one 8s infinite
        &.three
            top: 22%
            right: 0
            max-width: 25%
            animation: down-up-two 8s infinite
            animation-delay: 4s
        &.four
            right: 0
            bottom: 20%
            max-width: 35%
            animation: moveLeftRight 10s infinite
    .circle-shapes-wrap
        width: 70%
        position: absolute
        border-radius: 50%
        padding-bottom: 70%
        background: #ededed
        z-index: -1
        left: 50%
        top: 50%
        transform: translate(-50%, -50%)
        &:before
            content: ''
            +size(10px)
            position: absolute
            border-radius: 50%
            left: 0
            top: 8%
            background: $primary-color
            animation: shapeAnimationFive 20s infinite
        .circle-shape
            +size(100%)
            position: absolute
            border-radius: 50%
            border: 1px solid #e0e0e1
            left: 15%
            top: -1%
            animation: rounded 30s linear infinite
            &:after,
            &:before
                content: ''
                +size(17px)
                position: absolute
                border-radius: 50%
                animation: zoomInOutTwo 4s infinite
            &:before
                right: 15%
                top: 11%
                background: #e1754c
            &:after
                bottom: 6%
                right: 25%
                animation-delay: 2s
                background: $orange-color
.hero-ten-images
    margin-right: -20%
    +md-d()
        margin-right: 0
    +mo-d()
        margin-top: 0
.hero-section-ten
    .hero-ten-shapes
        position: absolute
        max-width: 50%
        left: -100px
        bottom: 0
        z-index: -1
        animation: moveLeftRight 10s infinite