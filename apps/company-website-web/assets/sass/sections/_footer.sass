/*******************************************************/
/**************** 32. Main Footer Style ****************/
/*******************************************************/
.footer-widget-area
    background-color: lighten($primary-color, 5%)
    
.footer-logo
    margin-top: -10px
    
.footer-widget
    margin-bottom: 50px

.footer-title
    margin-bottom: 28px

.newsletter-widget
    form
        padding: 7px
        max-width: 310px
        margin-top: 30px
        +flexcenter(center)
        border-radius: 35px
        border: 1px solid #3a4a65
        input
            color: white
            height: auto
            border: none
            background: transparent
            padding: 5px 5px 5px 20px
            &::placeholder
                color: white
        button
            flex: none
            +circle(lighten($primary-color, 20%), 45px)
    p br
        +md-d
            display: none
            
.contact-info
    li
        display: flex
        i
            flex: none
            color: #ced1d5
            margin: 5px 12px 0 0
            +circle(#223452, 40px)
        
.widget-news-item
    max-width: 300px
    +flexcenter(start)
    margin-bottom: 25px
    &:last-child
        margin-bottom: 0
    img
        max-width: 75px
        margin-right: 22px
.widget-news-content
    h6, h5
        margin-bottom: 5px

.footer-top
    .contact-info
        display: flex
        flex-wrap: wrap
        +mo-d
            justify-content: space-between
        li
            width: 33.33%
            padding-top: 15px
            justify-content: center
            border-right: 1px solid #223452
            &:first-child
                justify-content: start
            &:last-child
                margin-right: 0
                border-right: none
            +mo-d
                width: auto
                margin-right: 25px
                border-right: none
            i
                +color(white, $primary-color)
                
/** Footer Copyright **/
.copyright-inner
    flex-wrap: wrap
    padding: 18px 0 3px
    +flexcenter(space-between)
    p
        opacity: 0.7
        font-size: 14px
        margin-bottom: 10px
    .social-style-one
        a
            margin-right: 33px
            
/* Footer One */
.footer-one
    .newsletter-widget
        padding: 40px 30px
        text-align: center
        border-radius: 5px
        background: lighten($primary-color, 7%)
        form
            border: 1px solid #4d79cc
/* Footer Two */
.footer-two
    background-color: $light-black-color
    .footer-widget-area
        background: transparent
    .copyright-area
        border-top: 1px solid #223452
    p,
    .text,
    .list-style-two,
    .contact-info span,
    .newsletter-widget input::placeholder
        opacity: 0.75
        
/* Footer Three */
.footer-three
    p,
    .date,
    .text,
    .list-style-three,
    .contact-info span,
    .list-style-two *:not(i),
        opacity: 0.75
.main-footer
    .list-style-three li:before
        color: white;
        font-size: 14px
        content: "\f054"
        
/* Footer Four */
.footer-sign-up
    transform: translateY(-90px)
    .footer-signup-inner
        +mo-d
            +gapboth(padding, 25px)
        +xs-d
            +gapboth(padding, 15px)
    .container
        max-width: 1330px
    .sign-in-form
        display: flex
        background: white
        position: relative
        border-radius: 5px
        align-items: center
        padding: 10px 15px 10px 0
        +sm-d
            padding: 5px
        input
            border: none
            background: transparent
            +xs-d
                padding: 10px 0 10px 10px
            &::placeholder
                font-weight: 500
                color: $heading-color
        button
            flex: none
            +xs-d
                padding: 10px 15px
.footer-four
    .social-style-two
        a
            opacity: 1
            color: #454545
            background: white
            &:hover
                color: white
                background: $primary-color
    .list-style-three
        li
            &:before
                color: #454545
    .list-style-two
        i
            color: $primary-color
            
/* Footer Five */
.footer-five
    .copyright-inner
        border-top: 1px solid #CED0D4
        p
            opacity: 1
            color: #0A1426
            font-size: 16px
            padding: 10px 0
    .list-style-two
        i
            font-size: 18px
            color: $primary-color-two
    .social-style-two
        a
            opacity: 1
            background: white
            color: $heading-color
            &:hover
                color: white
                background: $primary-color-two
            
/* Footer Six */
.footer-six
    font-size: 16px
    p
        font-weight: 600
    .footer-widget
        .social-style-two
            flex-wrap: wrap
            a
                opacity: 1
                color: #31394C
                border-radius: 7px
                margin: 0 10px 10px 0
                background: rgba(55, 114, 255, 0.1)
                &:hover
                    color: white
                    background: #3772FF
    .link-widget
        li
            a
                color: #333F7D
                font-weight: 600
                &:hover
                    color: $orange-color
    .newsletter-widget
        form
            padding: 0
            max-width: none
            border-color: #CCCFDE
            border-radius: 5px 0 0 5px
            input
                color: #666F9D
                &::placeholder
                    color: #666F9D
            button
                +size(60px)
                color: white
                border-radius: 0
    .copyright-inner
        background: #F8FBFF
        padding: 25px 40px 20px
        +xs-d
            +gapboth(padding, 25px)
        p, a
            color: #000F5C
            font-size: 18px
        .footer-menu
            display: flex
            flex-wrap: wrap
            li
                margin: 0 10px 5px 0
                font-family: 'Circular Std'
                
/* Footer Seven */
.footer-seven
    .footer-title
        font-size: 27px
        font-weight: 700
        margin-bottom: 22px
    .social-style-two
        a
            opacity: 1
            background: rgba(255, 255, 255, 0.1)
            &:hover
                background: $red-color
    .list-style-three
        li
            font-size: 18px
            font-weight: 400
            position: relative
            margin-bottom: 15px
            &:last-child
                margin-bottom: 0
            &:before
                display: none
            a
                &:before
                    content: "\f101"
                    position: absolute
                    left: 0
                    top: 0
                    opacity: 0
                    transition: 0.5s
                    font-weight: 600
                    color: $red-color
                    font-family: "Font Awesome 5 Free"
                &:hover
                    padding-left: 25px
                    &:before
                        opacity: 1
    .list-style-two
        li
            font-size: 18px
            margin-bottom: 20px
            i
                font-size: 20px
                color: $red-color
                margin-right: 15px
            b
                font-weight: 500
                margin-right: 5px
            &:last-child
                margin-bottom: 0
    .copyright-inner
        padding-top: 10px
        padding-bottom: 0
        flex-wrap: nowrap
        border-top: 1px solid rgba(232, 232, 233, 0.1)
        p
            opacity: 1
            font-size: 16px
        .scroll-top
            right: 0
            flex: none
            +size(60px)
            bottom: 10px
            font-size: 20px
            position: relative
            border-radius: 50%
            background: $red-color
            
/* Footer Nine */
.footer-nine
    &:before
        top: 0
        right: 0
        content: ''
        z-index: -1
        opacity: 0.1
        position: absolute
        +size(600px, 400px)
        filter: blur(100px)
        background: $green-color
    .about-widget
        max-width: 303px
    .social-style-two
        a
            opacity: 1
            z-index: 1
            position: relative
            &:before
                +size(100%)
                content: ''
                left: 0
                top: 0
                opacity: 0
                z-index: -1
                transition: 0.5s
                position: absolute
                border-radius: 50%
                background: linear-gradient(90deg, #A146E8 -21.46%, #6C63D0 36.39%, #387DB8 100%)
            &:hover
                &:before
                    opacity: 1
    .list-style-two
        a
            color: #A0A4A8
            &:hover
                color: $green-color
    .newsletter-widget
        p
            color: #A0A4A8
        form
            padding: 0
            border: none
            border-radius: 0
            background: #1B2429
            input
                &::placeholder
                    font-size: 14px
            button
                +size(56px)
                border-radius: 0
                background: linear-gradient(90deg, #A146E8 -21.46%, #6C63D0 36.39%, #387DB8 100%)
    .copyright-area
        background: #1B2429
        .copyright-inner
            padding-top: 15px
        .scroll-top
            bottom: 5px
            +size(50px)
            right: 0
            font-size: 22px
            position: relative
            border-radius: 50%
            background: linear-gradient(90deg, #A146E8 -21.46%, #6C63D0 36.39%, #387DB8 100%)
            
/* Footer Ten */
.footer-ten
    .social-style-two
        a
            &:not(:hover)
                opacity: 0.75
                background: #d6dafd
                color: $heading-color
    .list-style-three
        li
            &:before
                margin-top: 1px
                color: $base-color