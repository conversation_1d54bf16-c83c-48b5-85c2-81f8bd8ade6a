/*******************************************************/
/***************** 12. Feature Section *****************/
/*******************************************************/
.feature-item
    padding: 25px
    display: flex
    transition: 0.5s
    background: white
    border-radius: 5px
    position: relative
    margin-bottom: 30px
    border: 2px solid #e6ecf7
    &:hover
        border-color: white
        box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.08%)
    +xs-d
        display: block
    .icon
        margin-right: 25px
        i
            @extend %icon
    p
        margin-bottom: 0
    .learn-more
        margin-top: 12px
    .feature-btn
        +circle(white, 45px)
        color: $primary-color
        position: absolute
        right: 30px
        top: -20px
        box-shadow: 10px 0px 60px 0px rgba(16, 76, 186, 0.3)
        
/* Feature Style Two */
.feature-item-two
    background: white
    padding: 40px 35px
    margin-bottom: 30px
    box-shadow: 0px 10px 50px rgba(0, 0, 0, 0.07);
    .icon
        i
            color: #FF6600
            font-size: 30px
            padding-top: 3px
            display: inline-block
            +circle(#ff47571f, 70px)
    .feature-line
        +size(80px, 1px)
        position: relative
        background: #FF6600
        margin: 20px 0 15px
        display: inline-block
        .animate-bar
            height: 100%
            width: 10px
            left: -5px
            top: 0
            background: white
            position: absolute
            animation-duration: 5s
            animation-timing-function: linear;
            animation-iteration-count: infinite;
            animation-name: moveLeftRight
    p
        margin-bottom: 0
.feature-item-two.color-two
    .icon i
        color: #2F97F7
        background: #2F97F71f
    .feature-line
        background: #2F97F7
.feature-item-two.color-three
    .icon i
        color: #12B571
        background: #12B5711f
    .feature-line
        background: #12B571
.feature-item-two.color-four
    .icon i
        color: #3938B9
        background: #3938B91f
    .feature-line
        background: #3938B9
        
/* Feature Three */
.feature-item-three
    display: flex
    margin-bottom: 50px
    border-right: 1px dashed transparentize(#030A15, 0.8)
    +md-d
        border-right: none
    +xs-d
        display: block
    .icon
        flex: none
        font-size: 50px
        color: $red-color
        margin-right: 25px
        margin-bottom: 15px
        +circle(white, 100px)
        box-shadow:  0px 0px 50px rgba(0, 0, 0, 0.07)
        i:before
            line-height: inherit
    h3
        margin-bottom: 6px
    p
        margin-bottom: 0
        
/* Feature Four */
.feature-item-four
    margin-top: 30px
    transition: 0.5s
    background: white
    text-align: center
    border-radius: 5px
    padding: 30px 20px 15px
    border: 1px solid #e5e5e7
    .icon
        color: white
        font-size: 12px
        margin-bottom: 16px
        +gapboth(margin, auto)
        +circle($primary-color, 35px)
    &:hover
        border-color: white
        box-shadow: 10px 0 60px rgba(109, 109, 109, 0.15)
    
/* Feature Five */
.feature-item-five
    display: flex
    margin-bottom: 15px
    +xs-d()
        display: block
    .icon
        +size(85px)
        flex: none
        margin-top: 6px
        line-height: 85px
        margin-right: 25px
        border-radius: 50%
        text-align: center
        margin-bottom: 10px
        +flexcenter(center)
        border: 1px solid #dcddf4
        i
            +size(65px)
            color: white
            font-size: 25px
            line-height: 65px
            border-radius: 50%
            display: inline-block
            background: $primary-color
            &:before
                line-height: inherit
    p
        margin-bottom: 0