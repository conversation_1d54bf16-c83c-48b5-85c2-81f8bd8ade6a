/*******************************************************/
/********************* 17. Wedgets *********************/
/*******************************************************/
.widget
    background: white
    padding: 35px 30px
    margin-bottom: 55px
    +xs-d
        +gapboth(padding, 20px)
    &:last-child
        margin-bottom: 0
    .list-style-two
        li
            justify-content: space-between
            &:last-child
                margin-bottom: 0
            span
                margin-left: 10px
.widget-title
    font-size: 24px
    font-weight: 500
    margin-bottom: 35px
    position: relative
    &:after
        content: ''
        width: 45px
        height: 2px
        left: 0
        position: absolute
        top: calc(100% + 10px)
        background: $primary-color
    &:before
        content: ''
        width: 5px
        height: 2px
        left: 10px
        z-index: 1
        background: white
        position: absolute
        top: calc(100% + 10px)
    
/* Search Widget */
.widget-search
    padding: 5px 0
    form
        display: flex
        input
            border: none
            padding-right: 0
        button
            flex: none
            padding: 5px 15px
            margin-right: 10px
            color: $primary-color
            background: transparent
        
/* Recent Post */
.widget-recent-post
    .widget-news-item
        padding-bottom: 25px
        border-bottom: 1px solid #d7d7d7
        &:last-child
            padding-bottom: 0
            border-bottom: none
        img
            +size(70px)
            border-radius: 50%
            
/* Tag Cloud */
.tags
    display: flex
    flex-wrap: wrap
    margin: 0 -10px -10px 0
    a
        color: #8a8a8a
        padding: 5px 20px
        border-radius: 5px
        background: #eef3f9
        margin: 0 10px 10px 0
        &:hover
            color: white
            background: $primary-color
        
/* Call Action */
.widget-call-action
    text-align: center
    padding: 100px 50px
    +overlay($primary-color, 0.85)
    background: url(../images/news/call-action-widget.jpg) no-repeat center/cover
    +md-d
        +gapboth(padding, 40px)
    h2
        color: white
        font-size: 45px
        font-weight: 500
        margin-bottom: 35px
        +xs-d
            font-size: 35px
    p
        color: white
        margin-bottom: 35px
        
/* Portfolio Info */
.widget-portfolio-info
    li
        margin-bottom: 20px
        h4
            margin-bottom: 5px
        p
            font-size: 18px
            margin-bottom: 0
            +xs-d
                font-size: 16px
        &:last-child
            margin-bottom: 0
    +xs-d
        +gapboth(padding, 30px)
    
/* Service Lise */
.widget-services
    padding: 0
    li
        margin-bottom: 10px
        a
            display: flex
            font-size: 18px
            padding: 20px 30px
            border-radius: 5px
            border: 2px solid #e6ecf7
            +xs-d
                font-size: 16px
            &:after
                color: #8e959b
                content: "\f061"
                font-size: 16px
                font-weight: 700
                margin-left: auto
                font-family: 'Font Awesome 5 Free'
        &:hover a,
        &.active a
            border-color: $primary-color
            
/* Shop Widget */
.widget-product-item
    display: flex
    margin-bottom: 25px
    > a
        flex: none
        width: 80px
        margin-right: 15px
        box-shadow: 0px 0px 33px 0px rgba(173, 173, 173, 0.28)
    &:last-child
        margin-bottom: 0
    .widget-product-content
        h6
            margin-bottom: 8px
            text-transform: capitalize
        .rating
            margin-bottom: 5px
.shop-price
    font-size: 14px
    .price,
    .current-price
        &:before
            content: '$'
        &:after
            content: '.00'
    .current-price
        color: $primary-color
            
/* Shop Sidebar */
.shop-sidebar
    .widget
        background-color: #eff2f6
    .widget-search
        padding: 35px 30px
        form
            background: white
            border-radius: 5px
            input
                padding-right: 0
                padding-left: 20px