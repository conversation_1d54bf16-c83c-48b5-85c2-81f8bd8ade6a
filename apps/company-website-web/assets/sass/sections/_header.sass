/*******************************************************/
/******************* 06. Header style ******************/
/*******************************************************/
.main-header
    position: relative
    left: 0px
    top: 0px
    z-index: 999
    width: 100%
    transition: all 500ms ease
    .header-upper
        z-index: 5
        width: 100%
        position: relative
        transition: all 500ms ease
        .container-fluid
            padding: 0
    .logo-outer
        +tb-d
            display: none
    .logo
        z-index: 9
        padding: 2px 0
        position: relative
    &.fixed-header
        .header-upper
            top: 0
            left: 0
            position: fixed
            animation: sticky  1s
            box-shadow: 0px 0px 30px 0px rgba(87, 95, 245, .10)

.header-top
    color: $heading-color
    +flexcenter(space-between)
    
.top-left
    +tb-d
        display: none
    ul
        +flexcenter(start)
        li
            margin-right: 50px
            +lg-d
                margin-right: 15px
.top-right
    +flexcenter(start)
    +tb-d
        margin: auto
    .office-time
        margin-right: 75px
        +xs-d
            display: none
        i
            margin-right: 10px
            color: $primary-color
        +lg-d
            margin-right: 15px
                
.nav-outer
    width: 100%
    position: relative
    +tb-d
        position: static
    .menu-btn
        margin-left: auto
        +tb-d
            display: none
 
/** Header Main Menu **/
.main-menu
    +tb-d
        width: 100%
    .mobile-logo
        +sm-d
            max-width: 150px
    .collapse
        +tb-d
            overflow: auto
    .navbar-collapse
        > ul
            +tb-d
                margin: 25px 0
                max-height: 80vh
                border-bottom: 1px solid #f3f3f3
                    
        padding: 0px
        +tb-d
            left: 0
            width: 100%
            padding: 0 15px
            position: absolute
            background: #FFFFFF
            border-bottom: 1px solid #e0e0e0
        li
            float: left
            padding: 34px 25px
            +lg-d
                padding: 34px 16px
            +md-d
                padding: 30px 15px
            +tb-d
                float: none
                padding: 0 20px
                background: white
                border-top: 1px solid #f3f3f3
            &.dropdown .dropdown-btn
                position: absolute
                right: 10px
                top: 0
                width: 50px
                height: 43px
                border-left: 1px solid #f2f2f2
                text-align: center
                line-height: 43px
                cursor: pointer
                display: none
                +tb-d
                    display: block
                    background: white
            a
                position: relative
                display: block
                font-size: 16px
                opacity: 1
                font-weight: 500
                color: $heading-color
                text-transform: capitalize
                transition: all 500ms ease
                +tb-d
                    padding: 10px 10px
                    line-height: 22px
            a:hover, &.current a, &.current-menu-item a
                color: $heading-color
            .btn-style-two
                text-decoration: none
                color: $heading-color
            .theme-btn
                color: white
                padding: 14px 20px
                background: $heading-color
                &:hover
                    color: white
                    background: $heading-color
            &.for-mega
                position: static
                animation-fill-mode: backwards
                +tb-d
                    position: relative
            .megamenu
                position: absolute
                left: 0px
                top: 100%
                width: 100%
                z-index: 100
                display: none
                padding: 20px 0
                background: #ffffff
                box-shadow: 2px 2px 5px 1px rgba(0, 0, 0, 0.05), -2px 0px 5px 1px rgba(0, 0, 0, 0.05)
                +clearfix
                +tb-d
                    position: relative
                    box-shadow: none
                    width: 100%
                    padding: 0
                    .container
                        max-width: 100%
                    .row
                        margin: 0px
                &+.dropdown-btn
                    z-index: 100
                    border-bottom: 1px solid #f2f2f2
                ul
                    display: block
                    position: relative
                    top: 0
                    width: 100%
                    box-shadow: none
                .mega-title
                    margin-left: 20px
                    +tb-d
                        margin-top: 12px
                        margin-left: 25px
            ul
                position: absolute
                left: inherit
                top: 100%
                min-width: 220px
                z-index: 100
                display: none
                background: #ffffff
                box-shadow: 2px 2px 5px 1px rgba(0, 0, 0, 0.05), -2px 0px 5px 1px rgba(0, 0, 0, 0.05)
                +tb-d
                    position: relative
                    display: none
                    width: 100%
                    box-shadow: none
                    +clearfix
                    +xs-d
                        min-width: 200px
                li
                    width: 100%
                    padding: 7px 20px
                    border-bottom: 1px solid rgb(242, 242, 242)
                    +tb-d
                        padding: 0 15px
                    ul
                        left: 100%
                        top: 0%
                        +tb-d
                            left: auto

    .navbar-header
        display: none
        +tb-d
            +flexcenter(space-between)
        .navbar-toggle
            float: right
            padding: 4px 0
            cursor: pointer
            background: transparent
            .icon-bar
                background: $heading-color
                height: 2px
                width: 30px
                display: block
                margin: 7px 5px

.text-white
    .navbar-collapse
        > ul > li > a,
        > ul > li > a:hover,
        > ul > li.current > a
            color: white
            +tb-d
                color: black
    .top-right .office-time i
        color: white
    .navbar-header .navbar-toggle .icon-bar
        background: white
    .dropdown-btn span
        color: black
    .mega-title
        color: $heading-color
                
/* Menu Icons */
.menu-icons
    display: flex
    align-items: center
    > button
        margin: 0 15px
        background: transparent
        
/* Header Search */
.nav-search
    position: relative
    button
        background: transparent
    form
        position: absolute
        width: 320px
        top: 100%
        right: 0
        z-index: 777
        padding: 10px
        border-radius: 5px
        +flexcenter(center)
        background-color: #fff
        box-shadow: 0 0 5px 5px rgba(0, 0, 0, 0.05)
        &.hide
            display: none
        input
            border: none
            padding: 15px 5px 15px 25px
        button
            padding: 15px
            color: #454545
            border-left: 1px solid #cfdbf1
            
/* Header Number */
.header-number
    display: flex
    align-items: center
    > i
        font-size: 18px
        margin-right: 20px
        border: 1px solid #E5E7EF
        +circle(transparent, 50px)
    .number-content
        font-family: 'Circular Std'
        span
            color: #333F7D
            display: block
            font-size: 14px
        a
            color: #000F5C
            font-size: 20px
            text-decoration: underline
    
/* Header One */
.header-one
    .logo-outer
        margin-right: 45px
        +md-d
            margin-right: 25px
            padding: 25px
    .header-top
        padding-left: 85px
        padding-right: 230px
        +md-d
            font-size: 14px
            padding-left: 25px
    .header-inner
        padding-right: 230px
    .header-inner,
    .header-top
        +lg-d
            padding-right: 85px
        +md-d
            padding-right: 25px
    
/* Header Two */
.header-two
    display: flex
    border-bottom: 1px solid #4e597b
    .header-top
        padding-left: 30px
        padding-right: 70px
        border-bottom: 1px solid #4e597b
        +lg-d
            padding-right: 30px
        +mo-d
            padding-left: 15px
            padding-right: 15px
        .top-left
            +md-d
                display: none
            ul li:last-child
                +lg-d
                    display: none
    .logo-outer
        border-right: 1px solid #4e597b
        +lg-d
            padding-left: 50px
            padding-right: 50px
    .header-upper
        background: $dark-blue-color
    .header-inner
        padding-left: 5px
        padding-right: 70px
        +lg-d
            padding: 0 30px 0 15px
    &.fixed-header
        top: 0
        position: fixed
        animation: sticky  1s
        .header-upper
            position: relative
            animation: none
        .header-top
            display: none
    .main-menu
        .navbar-collapse li
            +md-d
                padding-right: 10px

.header-wrap
    width: 100%
    
/* Header Three */
.header-three
    .header-upper
        position: absolute
        border-bottom: 1px solid #485165
    &.fixed-header
        .header-upper
            background: $dark-blue-color
    .top-left
        display: block
        +mo-d
            display: none
    .top-right
        margin-right: 0
        +mo-d
            margin-right: auto
        .office-time
            display: block
            margin-right: 0
    .main-menu
        margin-left: auto
    .menu-btn
        margin-left: 15px
            
/* Header Four */
.header-four
    .container
        max-width: 1450px
    .office-time
        margin-right: 40px
        +lg-d
            margin-right: 15px
    .header-upper
        position: absolute
    &.fixed-header
        .header-upper
            background: $dark-blue-color
    .nav-search,
    .top-left li:last-child
        +md-d
            display: none
    .menu-btn
        margin-left: 15px
            
/* Header Five */
.header-five
    position: absolute
    .container
        max-width: 1450px
    .header-top-wrap
        +xl-d
            margin-right: 40px
    .header-top
        +xl-d
            margin-right: -20px
            padding-left: 20px
    .office-time
        margin-right: 40px
        +lg-d
            margin-right: 15px
    .header-inner
        position: relative
        padding-right: 15px
        +tb-d
            padding-right: 0
        &:after
            content: ''
            top: 0
            right: 0
            z-index: -1
            background: white
            +size(100vw, 100%)
            position: absolute
            box-shadow: 0px 10px 60px rgba(16, 76, 186, 0.1)
            +tb-d
                right: -15px
    .main-menu
        .navigation
            > li
                padding-top: 26px
                padding-bottom: 26px
                +tb-d
                    padding: 0
    .nav-search,
    .top-left li:last-child
        +md-d
            display: none
    .menu-btn
        margin-left: 15px
    &.fixed-header
        .header-upper
            box-shadow: none
            
/* Header Six */
.header-six
    position: absolute
    +xl-d
        padding-top: 10px
        padding-bottom: 10px
    .menu-icons
        +md-d
            display: none
        button
            color: $primary-color-two
    &.fixed-header
        .header-upper
            background: white
    .main-menu
        margin-left: auto
        .navbar-collapse li
            +xl-d
                +gapboth(padding, 20px)
    .menu-btn
        margin-left: 20px
        
/* Header Seven */
.header-seven
    position: absolute
    border-bottom: 1px solid #E5E7EF
    .header-upper
        padding-left: 110px
        padding-right: 80px
        +lx-d
            +gapboth(padding, 50px)
        +lg-d
            +gapboth(padding, 25px)
    .main-menu
        margin-right: auto
        margin-left: 100px
        +lg-d
            margin-left: 50px
        +md-d
            margin-right: 0
            margin-left: auto
        .navbar-collapse
            .navigation > li
                +xl-d
                    +gapboth(padding, 15px)
            li
                +flexcenter(space-between)
                +tb-d
                    display: block
                a
                    font-size: 18px
                    font-weight: 500
                    font-family: 'Circular Std'
                .dropdown-btn
                    right: 0
                    +size(auto)
                    display: block
                    line-height: 1
                    border-left: none
                    position: relative
                    margin-left: 2px
                    &:before
                        content: '+'
                        color: #000F5C
                        font-size: 18px
                        font-weight: 500
                        font-family: 'Circular Std'
                    +tb-d
                        top: 0
                        +size(50px, 42px)
                        line-height: 42px
                        position: absolute
                        border-left: 1px solid #f2f2f2
                    span
                        display: none
                .dropdown
                    +tb-d
                        display: block
    .header-number
        margin-right: 100px
        +lx-d
            margin-right: 50px
        +lg-d
            margin-right: 0
        +md-d
            display: none
    .menu-btn
        margin-left: 0
        +lg-d
            display: none
    &:not(.fixed-header)
        .header-upper
            padding-top: 20px
            padding-bottom: 20px
            +lg-d
                padding-top: 0
                padding-bottom: 0
    &.fixed-header
        .header-upper
            background: white
            
/* Header Eight */
.header-eight
    .container
        max-width: 1530px
    .logo-outer
        flex: none
        z-index: 500
        transition: none
        margin-top: -66px
        position: relative
        background: $red-color
        border-radius: 0 0 50px 50px
        +tb-d
            margin-top: 0
            display: block
            margin-bottom: -15px
        a
            transition: none
            padding: 40px 60px
            display: inline-block
            +md-d
                max-width: 200px
                +gapboth(padding, 50px)
            +tb-d
                max-width: 175px
                padding: 25px 40px
    .header-top
        padding-left: 300px
        +tb-d
            padding-left: 0
    .top-left
        +md-d
            display: none
        li
            display: flex
            align-items: center
            margin-right: 30px
            i
                color: $red-color
                margin-right: 10px
            &:not(:last-child)
                padding-right: 30px
                border-right: 1px solid rgba(255, 255, 255, 0.3)
        select
            padding: 0
            color: white
            border: none
            cursor: pointer
            background: transparent
            option
                color: $base-color
    .top-right
        .hotline
            margin-right: 30px
            padding-right: 30px
            border-right: 1px solid rgba(255, 255, 255, 0.3)
            +ms-d
                display: none
            i, span
                margin-right: 5px
        .social-style-one
            i
                transition: 0.5s
            a:hover i
                color: $red-color
    .menu-btn
        margin-left: 35px
        +lg-d
            display: none
    .main-menu
        .navbar-collapse
            +tt-d
                .navigation
                    > li
                        display: flex
                        align-items: center
                        > .dropdown-btn
                            +size(auto)
                            right: 0
                            line-height: 1
                            margin-left: 3px
                            border-left: none
                            position: relative
            li.dropdown
                .dropdown-btn
                    display: block
                    font-size: 10px
                    span
                        &:before
                            content: "\f067"
    .nav-search
        +lg-d
            margin-right: 0
        +tb-d
            display: none
    .navbar-header
        button
            margin-left: auto
    .mobile-logo
        max-width: 100px
        
    &.fixed-header
        .logo-outer
            margin-top: 0
            margin-bottom: -18px
            +tb-d
                margin-bottom: -15px
            a
                +gapTB(padding, 35px)
                +tb-d
                    +gapTB(padding, 25px)
                
/* Header Nine */
.header-nine
    border-bottom: 1px solid rgba(255, 255, 255, 0.1)
    +xl-d
        +gapTB(padding, 20px)
    &.fixed-header
        .header-upper
            background: #080F17
    .container
        max-width: 1550px
    .logo-outer
        flex: none
    .main-menu
        +xl-d
            margin-right: 50px
        .navbar-collapse
            li
                ul,
                .megamenu
                    background: #182633
                a
                    color: white
                    font-size: 14px
                    text-transform: uppercase
                    font-family: 'Urbanist', sans-serif
                &.dropdown
                    .dropdown-btn
                        display: block
                        font-size: 8px
                        border-bottom: none
                        background: transparent
                        border-color: rgba(255, 255, 255, 0.1)
                        span
                            color: white
                            &:before
                                content: "\f067"
                ul
                    li
                        border-color: rgba(255, 255, 255, 0.1)
            +tt-d
                .navigation
                    > li
                        display: flex
                        align-items: center
                        +gapboth(padding, 18px)
                        > .dropdown-btn
                            +size(auto)
                            right: 0
                            top: -1px
                            line-height: 1
                            margin-left: 4px
                            border-left: none
                            position: relative
            +tb-d
                border: none
                background: #182633
                > ul
                    border-color: rgba(255, 255, 255, 0.1)
                    li
                        background: transparent
                        border-color: rgba(255, 255, 255, 0.1)
                        > a:hover,
                        &.current > a
                            color: $green-color
    .mega-title
        color: white
    .menu-icons,
    .nav-search
        > button
            +size(40px)
            position: relative
            border-radius: 50%
            border: 2px solid rgba(255, 255, 255, 0.2)
            .number
                top: -4px
                right: -3px
                position: absolute
                +circle(linear-gradient(90deg, #A146E8 -21.46%, #6C63D0 36.39%, #387DB8 100%), 20px)
        +lg-d
            display: none
    .menu-btn
        margin-left: 25px
        +md-d
            display: none
        
/* Header Ten */
.header-ten
    .header-top
        p
            margin-bottom: 0
            +xs-d()
                font-size: 14px
                line-height: 1.5
            a
                border-bottom: 1px solid
    .container
        +tt-d()
            max-width: 1620px
    .header-upper
        position: absolute
        .menu-btn
            +md-d()
                display: none
            .login
                font-size: 18px
                +lg-d()
                    display: none
                i
                    margin-right: 10px
            .theme-btn.style-six
                margin-left: 30px
                padding: 10px 30px
                i
                    font-size: 14px
    .nav-search form
        +xs-d()
            max-width: 260px
    &.fixed-header
        .header-upper
            background: white
    .main-menu
        margin-left: auto
        .navbar-collapse
            .navigation > li
                +xl-d
                    +gapboth(padding, 15px)
            li
                +flexcenter(space-between)
                +tb-d
                    display: block
                a
                    font-size: 18px
                    +tt-d()
                        border-bottom: 1px solid transparent
                    &:hover
                        color: $primary-color
                        border-color: $primary-color
                .dropdown-btn
                    right: 0
                    +size(auto)
                    display: block
                    line-height: 1
                    border-left: none
                    position: relative
                    margin-left: 2px
                    &:before
                        display: none
                    +tb-d
                        top: 0
                        +size(50px, 42px)
                        line-height: 42px
                        position: absolute
                        border-left: 1px solid #f2f2f2
                li
                    a
                        border: none