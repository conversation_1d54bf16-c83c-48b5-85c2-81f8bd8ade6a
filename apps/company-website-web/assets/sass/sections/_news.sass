/*******************************************************/
/****************** 16. News Section *******************/
/*******************************************************/
.news-item
    overflow: hidden
    border-radius: 5px
    margin-bottom: 30px
    box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.07)
    img
        width: 100%
    
.news-content
    padding: 22px 22px 2px
    border-bottom: 1px solid #e3e3e3
    
.post-meta-item
    flex-wrap: wrap
    +flexcenter(start)
    margin-bottom: 12px
    li
        font-size: 15px
        +flexcenter(start)
        margin: 0 30px 5px 0
        &:last-child
            margin-right: 0
        i
            margin-right: 10px
            color: $primary-color
        b
            font-weight: 500
    
.news-author
    font-size: 15px
    font-weight: 500
    +flexcenter(start)
    padding: 15px 22px
    img
        +circle(white, 35px)
        margin-right: 15px
        
/* News Style Two */
.news-section-two
    background-position: 0 100%
    background-repeat: no-repeat
.news-item.style-two
    overflow: visible
    box-shadow: none
    .news-content
        background: white
        position: relative
        border-radius: 5px
        border-bottom: none
        padding: 0 30px 10px
        margin: -25px 25px 0 0
        box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.07)
        +xs-d
            margin-right: 15px
        .news-author
            padding: 0
            transform: translateY(-15px)
            
/* News Style Three */
.news-item.style-three
    transition: 0.5s
    *
        transition: 0.5s
    .news-content
        border-bottom: none
        padding: 33px 30px 18px
        +xs-d
            +gapboth(padding, 15px)
    &:hover
        background: $primary-color
        *
            color: white
        p,
        .post-meta-item a
            opacity: 0.5
        
/* News Style Four */
.blog-four-left
    h3
        font-size: 24px
        font-weight: 400
.blog-carousel
    +gapboth(margin, -15px)
.blog-style-four
    margin: 15px
    border-radius: 10px 10px 0px 0px
    .image
        img
            width: 100%
            border-radius: 10px 10px 0px 0px
    .blog-four-content
        padding: 25px 40px 40px
        border-radius: 0px 0px 10px 10px
        box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.05)
        +xs-d
            +gapboth(padding, 25px)
        .date
            font-size: 14px
            display: block
            font-weight: 600
            margin-bottom: 10px
            i
                margin-right: 8px
        h3
            font-size: 24px
            margin-bottom: 20px
            +xs-d
                font-size: 20px
.blog-next-prev
    button
        transition: 0.5s
        margin-right: 10px
        +circle(white, 60px)
        box-shadow: 0px 10px 50px rgba(0, 0, 0, 0.1)
        &:focus,
        &:hover
            color: white
            background: #2969E6
        
/* News Style Five */
.news-five-item
    position: relative
    margin-bottom: 30px
    img
        width: 100%
        border-radius: 5px
    .content
        position: absolute
        +size(100%)
        left: 0
        top: 0
        display: flex
        padding: 30px 40px
        border-radius: 5px
        justify-content: end
        flex-direction: column
        background: linear-gradient(180deg, rgba(3, 10, 21, 0.4) 0%, #030A15 100%)
        +mt-d
            +gapboth(padding, 25px)
        +xs-d
            +gapboth(padding, 25px)
        *
            color: white
        .date
            +size(65px)
            line-height: 1.2
            padding-top: 10px
            text-align: center
            border-radius: 10px
            background: $red-color
            font-family: $heading-font
            margin: 0px -10px auto auto
            span
                display: block
                font-size: 20px
                font-weight: 700
        h3
            font-size: 24px
            +xs-d
                font-size: 20px
        .learn-more
            text-decoration: underline
            i
                float: right
                font-size: 16px
                color: $red-color
    .post-meta-item
        margin-bottom: 10px
        padding-bottom: 8px
        border-bottom: 1px solid rgba(248, 248, 248, 0.1)
        +xs-d
            display: none
        li
            font-size: 16px
            i
                color: $red-color
                
/* News Style Six */
.news-six-area
    z-index: 1
    position: relative
    background: #040B11
    &:before
        top: 10%
        left: 0
        content: ''
        z-index: -1
        opacity: 0.1
        position: absolute
        +size(800px, 400px)
        filter: blur(200px)
        background: $green-color
.news-six-item
    margin-top: 80px
    border-radius: 5px
    margin-bottom: 30px
    background: #1B2429
    padding: 0 40px 18px
    border: 1px solid rgba(255, 255, 255, 0.15)
    +mo-d
        +gapboth(padding, 25px)
    .image
        margin: -80px -25px 25px
        +mo-d
            +gapboth(margin, -15px)
        img
            width: 100%
            border-radius: 5px
    .post-meta-item
        margin-top: 15px
        i
            color: #6C63D0
            font-size: 16px
        a
            color: #CACCCF
        
/* Blog Page */
.blog-page-area,
.blog-details-area
    position: relative
    z-index: 1
    .container > .row
        +colgap(60px)
.blog-details-content,
.blog-standard-content
    &:after
        content: ''
        top: 0
        right: 0
        z-index: -1
        height: 100%
        width: 110vw
        background: white
        position: absolute
.blog-header,
.blog-footer
    flex-wrap: wrap
    +flexcenter(space-between)
    .social-style-one,
    .social-style-two
        font-size: 14px
        margin-bottom: 15px
        span
            font-size: 16px
            margin-right: 15px
    .social-style-one
        a:not(:hover)
            color: #8a8a8a
    .social-style-two
        a
            +size(30px)
            opacity: 1
            color: white
            line-height: 30px
            background: $primary-color
.blog-footer
    .popular-tags
        b
            font-weight: 500
            color: $heading-color
        a
            &:after
                content: ','
            &:last-child:after
                content: ''
.blog-standard-item,
.blog-details-content
    .image
        margin-bottom: 35px
        img
            width: 100%
    h3
        font-size: 30px
        font-weight: 500
        +ms-d
            font-size: 25px
        +xs-d
            font-size: 20px
    p
        margin-bottom: 25px
.blog-standard-item
    margin-bottom: 55px
    .video-blog
        position: relative
        .video-play
            position: absolute
            left: 50%
            top: 50%
            transform: translate(-50%, -50%)
            i
                color: white
                font-size: 20px
                +circle($primary-color, 100px)
                +sm-d
                    +size(60px)
                    line-height: 60px
    &.without-image
        padding: 35px 25px
        border: 1px solid #e9e9e9
    &.quote-blog
        z-index: 1
        padding: 50px
        position: relative
        background: $primary-color
        +xs-d
            +gapboth(padding, 25px)
        h3
            margin-bottom: 0
            a
                color: white
        &:after
            position: absolute
            content: "\f10e"
            font-weight: 700
            left: 50%
            top: 50%
            z-index: -1
            color: white
            opacity: 0.25
            font-size: 100px
            font-family: "Font Awesome 5 Free"
            transform: translate(-50%, -50%)
        
/* Blog Details */
blockquote
    font-size: 20px
    line-height: 1.5
    padding-left: 30px
    margin: 30px 0 35px
    color: $heading-color
    border-left: 5px solid $primary-color
    +sm-d
        font-size: 18px
        padding-left: 20px
    +xs-d
        font-size: 16px
    .author
        display: block
        font-size: 16px
        margin-top: 10px
        font-weight: 500
.next-prev-blog
    z-index: 0
    display: block
    transition: 0.5s
    margin-bottom: 15px
    padding: 30px 40px 20px
    background: $light-color
    h4
        transition: 0.5s
        +xs-d
            font-size: 18px
    &:hover
        background: transparentize($primary-color, 0.1)
        h4
            color: white
/* Comments */
.comments-wrap
    max-width: 700px
.comment-title
    position: relative
    font-size: 30px
    font-weight: 500
    +xs-d
        font-size: 25px
    &:after
        content: ''
        width: 45px
        height: 2px
        left: 0
        position: absolute
        top: calc(100% + 10px)
        background: $primary-color
    &:before
        content: ''
        width: 5px
        height: 2px
        left: 10px
        z-index: 1
        background: white
        position: absolute
        top: calc(100% + 10px)
.comment-item
    display: flex
    margin-bottom: 40px
    +sm-d
        display: block
    .author-image
        flex: none
        margin: 5px 30px 20px 0
        img
            +size(100px)
            border-radius: 50%
    .comment-details
        .name-date
            display: flex
            h5
                font-weight: 500
                margin-bottom: 5px
            .date
                font-size: 14px
                font-weight: 500
                margin: 0 0 5px 25px
                display: inline-block
                color: $primary-color
                
        p
            margin-bottom: 5px
        .reply
            font-size: 14px
            font-weight: 500
    &.child-comment
        margin-left: 55px
        +xs-d
            margin-left: 25px
        
/* Comment Form */
.comment-form
    .form-group
        position: relative
        label
            position: absolute
            right: 15px
            top: 12px
            padding: 5px 10px
            color: $primary-color