/*******************************************************/
/*****************  02. Common Classes *****************/
/*******************************************************/
.page-wrapper
    position: relative
    z-index: 9
    width: 100%
    margin: 0 auto
    overflow: hidden
    min-width: 300px
    background: #FFFFFF
    
.container
    max-width: 1200px
    &.container-1250
        max-width: 1280px
    &.container-1635
        max-width: 1665px
    &.container-1000
        max-width: 1030px

.small-gap
    +colgap(10px)
    
.col-small
    +xs-d
        flex: 0 0 100%
        max-width: 100%
    
/** Section Title style **/
.section-title
    .sub-title
        font-weight: 500
        margin-bottom: 20px
        color: $primary-color
        display: inline-block
    h2
        +sm-d
            font-size: 30px
        +xs-d
            font-size: 25px
        br
            +sm-d
                display: none
    .sub-title-two
        font-size: 20px
        font-weight: 500
        padding: 10px 52px
        color: $primary-color
        display: inline-block
        background-image: url(../images/shapes/subtitle-bg.png)
        background-size: 100% 100%
        +xs-d()
            font-size: 16px
.text-white
    .sub-title,
    .sub-title-two
        color: white
    .sub-title-two
        background-image: url(../images/shapes/subtitle-bg-white.png)
        
.section-title-with-btn
    flex-wrap: wrap
    +flexcenter(space-between)
    h2
        margin-right: 25px
        
/* Author Comment */
.author-comment
    display: flex
    margin-left: 32px
    .author
        flex: none
        max-width: 65px
        position: relative
        margin: -25px -35px 0 -32px
        img
            +size(65px)
            border-radius: 50%
    .text
        padding: 28px 55px
        background: #f4f8ff
        +xs-d()
            +gapboth(padding, 20px)
        
/** Button style **/
.theme-btn,
a.theme-btn
    background: $primary-color
    font-size: $base-size
    color: white
    cursor: pointer
    font-weight: 500
    text-align: center
    border-radius: 5px
    padding: 11px 28px
    align-items: center
    display: inline-flex
    text-transform: capitalize
    i
        transition: 0.5s
        margin-left: 10px
    &.btn-circle
        border-radius: 50px
        padding-left: 35px
        padding-right: 35px
    &:hover
        color: white
        background: $heading-color
        i
            margin-left: 15px
            margin-right: -5px
        
    &.style-two
        padding: 11px 35px
        background: #0850b0
        border: 1px solid #88a6dd
        &:hover
            background: $heading-color
            border-color: $heading-color
    
    &.style-three
        padding: 11px 40px
        color: $primary-color
        background: $light-color
        &:hover
            color:  $light-color
            background: $primary-color

    &.style-four
        padding: 11px 35px
        background: #24323d
        &:hover
            background: $primary-color

    &.style-five
        padding: 10px 35px
        background: transparent
        border: 1px solid white
        &:hover
            background: white
            color: $primary-color

    &.style-six
        padding: 10px 35px
        color: $heading-color
        background: transparent
        border: 1px solid #e5e5e5
        &:hover
            background: $light-color

    &.style-seven
        padding: 13px 33px
        color: $primary-color-two
        background: transparent
        border: 2px solid $primary-color-two
        &:hover
            color: white
            background: $primary-color-two

    &.style-eight
        border: none
        font-size: 18px
        padding: 16px 40px
        background: #2969E6
        font-family: 'Circular Std'
        +xs-d
            padding: 15px 28px
        i
            float: right
        &:hover
            text-decoration: underline
            background: $primary-color-two
    &.style-nine
        background: $black-color
        &:hover
            color: white
            background: $red-color
        &.hover-two
            border: 1px solid $black-color
    &.style-ten
        background: $red-color
        &:hover
            background: white
            color: $heading-color
            i
                color: $heading-color
        &.hover-two:hover
            color: white
            background: $black-color
            i
                color: white
    &.style-eleven
        background: white
        color: $heading-color
        i
            color: $heading-color
        &:hover
            color: white
            background: $red-color
            i
                color: white
    &.style-twelve
        background: $green-color
        &:hover
            background: white
            color: $green-color
    &.prev-icon
        i
            margin-left: 0
            margin-right: 10px
        &:hover
            i
                margin-right: 15px
    &.gradient-btn-one,
    &.gradient-btn-two
        z-index: 1
        position: relative
        border-radius: 4px
        padding: 14px 36px
        text-transform: uppercase
        background: linear-gradient(90deg, #A146E8 -21.46%, #6C63D0 36.39%, #387DB8 100%)
        +sm-d
            padding: 12px 25px
        &:before
            content: ''
            z-index: -1
            left: 50%
            top: 50%
            transition: 0.5s
            border-radius: 4px
            position: absolute
            background: #030511
            transform: translate(-50%, -50%)
    &.gradient-btn-one:hover,
    &.gradient-btn-two:not(hover)
        &:before
            opacity: 1
            +size(calc(100% - 3px))
    &.gradient-btn-two:hover,
    &.gradient-btn-one:not(hover)
        &:before
            +size(0)
            opacity: 0
    +sm-d
        font-size: 14px
        padding: 12px 25px
    
.learn-more
    display: inline-flex
    align-items: center
    &:hover
        i
            margin-left: 10px
    i
        font-size: 13px
        transition: 0.3s
        margin: 2px 0 0 8px
        
.read-more
    color: #000F5C
    font-size: 16px
    font-weight: 500
    align-items: center
    display: inline-flex
    text-decoration: underline
    font-family: 'Circular Std'
    i
        float: right
        transition: 0.5s
        margin-left: 5px
        color: $orange-color
    &:hover
        color: #2969E6
        text-decoration: underline
        i
            color: #2969E6
            margin-left: 8px
         
/** List style **/
.list-style-one
    li
        display: flex
        font-weight: 500
        margin-top: 20px
        &:before
            color: white
            font-size: 14px
            content: "\f00c"
            font-weight: 600
            margin-top: -4px
            margin-right: 15px
            +circle($primary-color, 35px)
            font-family: 'Font Awesome 5 Free'
    
.list-style-two
    li
        display: flex
        margin-bottom: 10px
        text-transform: capitalize
    i
        margin: 6px 20px 0 0
.list-style-three
    li
        display: flex
        margin-bottom: 7px
        text-transform: capitalize
        &:before
            font-size: 14px
            content: "\f00c"
            font-weight: 700
            margin-right: 20px
            color: $primary-color
            font-family: 'Font Awesome 5 Free'
        
        
/** Social Link Style One **/
.social-style-one
    display: inline-block
    a
        margin-right: 15px
        color: $heading-color
        &:last-child
            margin-right: 0
        &:hover
            color: $primary-color
   
/** Social Link Style two **/
.social-style-two
    display: inline-flex
    a
        color: white
        opacity: 0.4
        margin-right: 7px
        +circle(#162b3b, 40px)
        &:last-child
            margin-right: 0
        &:hover
            opacity: 1
            background: $primary-color

/*** Preloader style ***/
.preloader
    position: fixed
    width: 100%
    height: 100vh
    background: #ffffff
    z-index: 9999999
    .theme-loader
        margin: auto
        height: 70px
        width: 70px
        right: 0
        left: 0
        top: 0
        bottom: 0
        z-index: 999
        position: absolute
        transform-style: preserve-3d
        animation: animate 4.59s ease-in-out infinite

    .theme-loader:before,
    .theme-loader:after
        content: ""
        width: 50px
        height: 50px
        border-radius: 50%
        position: absolute
        border: 15px solid #1B76FF
        animation: animate_1 1.73s ease-in-out infinite both reverse

    .theme-loader:after
        border-color: #0D47A1
        animation-delay: -0.86s

/* Pagination */    
.pagination
    li
        margin: 0 10px 10px 0
        a,
        .page-link
            padding: 0
            color: #859aaa
            box-shadow: none
            +circle(white, 60px)
            border: 1px solid #e5e5e5
            +sm-d
                +size(45px)
                line-height: 45px
        &.disabled,
        &:last-child
            .page-link
                border-radius: 50%
        &:hover:not(.disabled),
        &.active
            .page-link
                color: white
                background: $primary-color
                border-color: $primary-color
        
/* Rating */
.rating
    display: flex
    i
        font-size: 14px
        color: $primary-color
                
/*** Scroll Top style ***/
.scroll-top
    position: fixed
    bottom: 30px
    right: 30px
    z-index: 99
    +size(40px)
    color: white
    display: none
    cursor: pointer
    border-radius: 5px
    animation: pulse 2s infinite
    background: lighten($primary-color, 8%)
  
/* Text White */
.text-white *,
.text-white a
    color: white
    
/* Overlay */
.overlay
    +overlay(#0d004c, 0.4)
    
/* Video Play Btn */
.video-play
    display: inline-block
    i
        +circle(white, 55px)
        color: $primary-color
    span
        font-size: 18px
        font-weight: 500
        margin-left: 10px
        
/* Position */
.rel
    position: relative
@for $i from 0 through 5
    .z-#{0 + $i}
        z-index: 0 + $i
    
/* Backgruond Size */
.bgs-cover
    background-size: cover
    background-position: center

/* Border None */
.no-border
    border: none !important
    
.box-shadow
    box-shadow: 0px 0px 33px 0px rgba(0, 0, 0, 0.07)
    
/* Background Colors */
.bg-blue
    background-color: $primary-color
.bg-black
    background-color: $black-color
.bg-red
    background-color: $red-color
.bg-light-black
    background-color: $light-black-color
.bg-dark-blue
    background-color: $dark-blue-color
.bg-lighter
    background-color: $light-color
    
/* Border Radius */
@for $i from 1 through 6
    .br-#{5 * $i}
        border-radius: 5px * $i
    
.home-six
    a:hover
        color: $primary-color-two
    .theme-btn,
    .scroll-top,
    .theme-btn.style-three:hover
        background: $primary-color-two
        &:hover
            color: white
    .theme-btn
        &.style-three
            padding-top: 15px
            padding-bottom: 15px
            color: $heading-color
            text-transform: uppercase
    .bg-lighter
        background-color: #F4F7FA
    .section-title
        .sub-title
            color: $primary-color-two
        h2
            text-transform: capitalize
    
.home-seven
    color: #666F9D
    font-size: 18px
    font-family: 'Nunito'
    p
        color: #666F9D
    h1, h2, h3, h4, h5, h6
        color: #000F5C
        font-family: 'Circular Std'
    h2
        font-size: 48px
        +ms-d
            font-size: 40px
        +sm-d
            font-size: 35px
        +xs-d
            font-size: 30px
    .sub-title
        color: white
        font-size: 16px
        font-weight: 600
        padding: 5px 15px
        border-radius: 5px
        display: inline-block
        background: $orange-color
            
.portfolio-sidebar
    .bg-lighter
        background-color: #F4F7FA
        
.home-eight
    .theme-btn, a.theme-btn
        padding: 15px 35px
        +sm-d
            padding: 12px 25px
    a:hover,
    .section-title .sub-title
        color: $red-color
    .list-style-one
        li
            &:before
                +size(30px)
                color: $red-color
                line-height: 30px
                background: #F8F8F8
    .list-style-three
        li
            font-weight: 500
            &:before
                color: $red-color

.home-nine
    .page-wrapper
        background: #080F17
    .sub-title,
    h1, h2, h3, h4, h5, h6
        font-weight: 700
        font-family: 'Urbanist', sans-serif
    .sub-title
        font-size: 18px
        color: $green-color
        margin-bottom: 15px
    h2
        font-size: 48px
        +mo-d
            font-size: 40px
        +ms-d
            font-size: 35px
        +xs-d
            font-size: 30px
    .slick-dots
        margin-top: 25px
        +flexcenter(center)
        li
            margin: 0 8px 0
            cursor: pointer
            position: relative
            +flexcenter(center)
            button
                +size(6px)
                overflow: hidden
                text-indent: 100px
                border-radius: 50%
                background: transparent
                background: linear-gradient(90deg, #A146E8 -21.46%, #6C63D0 36.39%, #387DB8 100%)
            &:before
                content: ''
                +size(20px)
                left: 50%
                top: 50%
                opacity: 0
                transition: 0.3s
                position: absolute
                border-radius: 50%
                border: 2px solid #6C63D0
                transform: translate(-50%, -50%)
            &.slick-active
                &:before
                    opacity: 1
    .slick-arrow
        z-index: 1
        position: relative
        display: inline-block
        +circle(#1B2429, 56px)
        border: 1.5px solid rgba(255, 255, 255, 0.1)
        &:before
            content: ''
            +size(100%)
            left: 0
            top: 0
            opacity: 0
            z-index: -1
            transition: 0.5s
            position: absolute
            border-radius: 50%
            background: linear-gradient(90deg, #A146E8 -21.46%, #6C63D0 36.39%, #387DB8 100%)
        &:first-child
            margin-right: 5px
        &:focus,
        &:hover
            &:before
                opacity: 1
        
.home-ten
    .bg-lighter
        background: #f4f4f4
    .theme-btn
        +xl-d()
            padding: 16px 36px
            &.style-six
                +gapTB(padding, 15px)
    .section-title
        h2
            +tt-d()
                font-size: 40px