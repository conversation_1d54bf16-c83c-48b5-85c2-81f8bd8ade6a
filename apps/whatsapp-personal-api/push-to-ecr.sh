#!/bin/bash
echo "=== Pushing WhatsApp API Docker Image to ECR ==="

# Set variables
AWS_PROFILE="whatsapp-api-sso"
AWS_REGION="us-east-1"
ECR_REPO_NAME="whatsapp-personal-api"
IMAGE_TAG="latest"

# Get AWS account ID
echo "Retrieving AWS account ID..."
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query "Account" --output text --profile $AWS_PROFILE)

if [ -z "$AWS_ACCOUNT_ID" ]; then
    echo "Failed to retrieve AWS account ID. Please check your AWS SSO session."
    echo "Run 'aws sso login --profile $AWS_PROFILE' to start a new session."
    exit 1
fi

echo "AWS Account ID: $AWS_ACCOUNT_ID"

# Build Docker image
echo "Building Docker image..."
docker build -t $ECR_REPO_NAME:$IMAGE_TAG .

if [ $? -ne 0 ]; then
    echo "Failed to build Docker image."
    exit 1
fi

# Log in to ECR
echo "Logging in to ECR..."
aws ecr get-login-password --region $AWS_REGION --profile $AWS_PROFILE | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com

if [ $? -ne 0 ]; then
    echo "Failed to log in to ECR. Please check your AWS SSO session."
    echo "Run 'aws sso login --profile $AWS_PROFILE' to start a new session."
    exit 1
fi

# Check if repository exists, create if it doesn't
echo "Checking if ECR repository exists..."
aws ecr describe-repositories --repository-names $ECR_REPO_NAME --profile $AWS_PROFILE --region $AWS_REGION > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "Creating ECR repository..."
    aws ecr create-repository --repository-name $ECR_REPO_NAME --profile $AWS_PROFILE --region $AWS_REGION
    
    if [ $? -ne 0 ]; then
        echo "Failed to create ECR repository."
        exit 1
    fi
fi

# Tag Docker image
echo "Tagging Docker image..."
docker tag $ECR_REPO_NAME:$IMAGE_TAG $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPO_NAME:$IMAGE_TAG

# Push Docker image to ECR
echo "Pushing Docker image to ECR..."
docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPO_NAME:$IMAGE_TAG

if [ $? -ne 0 ]; then
    echo "Failed to push Docker image to ECR."
    exit 1
fi

echo "=== Successfully pushed WhatsApp API Docker image to ECR ==="
echo "Repository: $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPO_NAME"
echo "Tag: $IMAGE_TAG"

# Make the script executable with: chmod +x push-to-ecr.sh
