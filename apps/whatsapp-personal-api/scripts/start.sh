#!/bin/bash
set -e

# Function to check if required environment variables are set
check_required_env_vars() {
  local missing_vars=()
  
  # Check for OpenAI API key
  if [ -z "$OPENAI_API_KEY" ]; then
    missing_vars+=("OPENAI_API_KEY")
  fi
  
  # Check for Telegram Bot Token
  if [ -z "$TELEGRAM_BOT_TOKEN" ]; then
    missing_vars+=("TELEGRAM_BOT_TOKEN")
  fi
  
  # If any required variables are missing, print error and exit
  if [ ${#missing_vars[@]} -gt 0 ]; then
    echo "ERROR: The following required environment variables are missing:"
    for var in "${missing_vars[@]}"; do
      echo "  - $var"
    done
    echo "Please set these variables in your .env file or environment."
    exit 1
  fi
}

# Function to ensure directories exist
ensure_directories() {
  # Create logs directory if it doesn't exist
  if [ ! -d "$LOG_DIR" ]; then
    echo "Creating logs directory at $LOG_DIR"
    mkdir -p "$LOG_DIR"
  fi
  
  # Create data directory if it doesn't exist
  if [ ! -d "$DATA_DIR" ]; then
    echo "Creating data directory at $DATA_DIR"
    mkdir -p "$DATA_DIR"
  fi
  
  # Ensure menu.csv exists
  MENU_CSV_PATH=${MENU_CSV_PATH:-"$DATA_DIR/menu.csv"}
  MENU_DIR=$(dirname "$MENU_CSV_PATH")
  if [ ! -d "$MENU_DIR" ]; then
    echo "Creating directory for menu CSV at $MENU_DIR"
    mkdir -p "$MENU_DIR"
  fi
  
  # Ensure orders.csv exists
  ORDERS_CSV_PATH=${ORDERS_CSV_PATH:-"$DATA_DIR/orders.csv"}
  ORDERS_DIR=$(dirname "$ORDERS_CSV_PATH")
  if [ ! -d "$ORDERS_DIR" ]; then
    echo "Creating directory for orders CSV at $ORDERS_DIR"
    mkdir -p "$ORDERS_DIR"
  fi
}

# Main execution
echo "Starting Anchor Food Ordering System..."

# Check required environment variables
check_required_env_vars

# Ensure directories exist
ensure_directories

# Print configuration
echo "Configuration:"
echo "  - PORT: $PORT"
echo "  - HOST: $HOST"
echo "  - ENVIRONMENT: ${ENVIRONMENT:-production}"
echo "  - OPENAI_MODEL: $OPENAI_MODEL"
echo "  - LOG_DIR: $LOG_DIR"
echo "  - DATA_DIR: $DATA_DIR"
echo "  - MENU_CSV_PATH: ${MENU_CSV_PATH:-$DATA_DIR/menu.csv}"
echo "  - ORDERS_CSV_PATH: ${ORDERS_CSV_PATH:-$DATA_DIR/orders.csv}"

# Start the application
echo "Starting application..."
exec python run.py
