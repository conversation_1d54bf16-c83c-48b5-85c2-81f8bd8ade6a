# WhatsApp API - Docker Build Script (PowerShell)
# This script builds the WhatsApp API Docker image locally

# Exit on error
$ErrorActionPreference = "Stop"

# Configuration
$IMAGE_NAME = "whatsapp-api"
$IMAGE_TAG = "latest"

# Optional: Use git commit hash as tag for versioning
if (Get-Command git -ErrorAction SilentlyContinue) {
    if (Test-Path .git) {
        $GIT_COMMIT = git rev-parse --short HEAD
        $IMAGE_TAG = "${IMAGE_TAG}-${GIT_COMMIT}"
    }
}

Write-Host ""
Write-Host "🔨 WhatsApp API - Building Docker Image" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green
Write-Host ""

# Check if Docker is installed
if (-not (Get-Command docker -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Docker is not installed. Please install it first." -ForegroundColor Red
    Write-Host "   Visit: https://docs.docker.com/get-docker/" -ForegroundColor Yellow
    exit 1
}

# Check if Docker daemon is running
try {
    docker info | Out-Null
} catch {
    Write-Host "❌ Docker daemon is not running. Please start Docker first." -ForegroundColor Red
    exit 1
}

Write-Host "📦 Image name: ${IMAGE_NAME}:${IMAGE_TAG}" -ForegroundColor White
Write-Host ""

# Build the Docker image
Write-Host "🔨 Building Docker image..." -ForegroundColor Cyan
Write-Host "   Building image: ${IMAGE_NAME}:${IMAGE_TAG}" -ForegroundColor Yellow
docker build -t "${IMAGE_NAME}:${IMAGE_TAG}" .

# Also tag as latest
docker tag "${IMAGE_NAME}:${IMAGE_TAG}" "${IMAGE_NAME}:latest"

Write-Host ""
Write-Host "✅ Docker image built successfully!" -ForegroundColor Green
Write-Host "📦 Image: ${IMAGE_NAME}:${IMAGE_TAG}" -ForegroundColor White
Write-Host "📦 Latest: ${IMAGE_NAME}:latest" -ForegroundColor White
Write-Host ""
Write-Host "🐳 To run this container locally:" -ForegroundColor Cyan
Write-Host "docker run -p 3030:3030 \\" -ForegroundColor White
Write-Host "  -e NODE_ENV=production \\" -ForegroundColor White
Write-Host "  -e PORT=3030 \\" -ForegroundColor White
Write-Host "  -e HOST=0.0.0.0 \\" -ForegroundColor White
Write-Host "  -e AWS_ACCESS_KEY_ID=your_aws_access_key \\" -ForegroundColor White
Write-Host "  -e AWS_SECRET_ACCESS_KEY=your_aws_secret_key \\" -ForegroundColor White
Write-Host "  -e AWS_REGION=ap-southeast-1 \\" -ForegroundColor White
Write-Host "  -e WEBHOOK_ENABLED=false \\" -ForegroundColor White
Write-Host "  -e WEBHOOK_URL=https://your-webhook-url.com/webhook \\" -ForegroundColor White
Write-Host "  -e WEBHOOK_SECRET=your_webhook_secret \\" -ForegroundColor White
Write-Host "  -e DEFAULT_COUNTRY_CODE=60 \\" -ForegroundColor White
Write-Host "  -v `$(pwd)/whatsapp-session:/app/whatsapp-session \\" -ForegroundColor White
Write-Host "  ${IMAGE_NAME}:${IMAGE_TAG}" -ForegroundColor White
Write-Host ""
Write-Host "📝 Note: Make sure to create a whatsapp-session directory and mount it" -ForegroundColor Yellow
Write-Host "    to preserve WhatsApp authentication between container restarts." -ForegroundColor Yellow
