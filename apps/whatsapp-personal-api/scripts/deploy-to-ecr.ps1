# WhatsApp API - Docker Build and Deploy to ECR Script (PowerShell)
# This script builds the WhatsApp API Docker image and pushes it to Amazon ECR

# Exit on error
$ErrorActionPreference = "Stop"

# Configuration - CHANGE THESE VALUES
$AWS_REGION = "ap-southeast-1"  # Change to your AWS region
$ECR_REPOSITORY_NAME = "whatsapp-api"
$IMAGE_TAG = "latest"

# Optional: Use git commit hash as tag for versioning
if (Get-Command git -ErrorAction SilentlyContinue) {
    if (Test-Path .git) {
        $GIT_COMMIT = git rev-parse --short HEAD
        $IMAGE_TAG = "${IMAGE_TAG}-${GIT_COMMIT}"
    }
}

Write-Host ""
Write-Host "🚀 WhatsApp API - Docker Build and Deploy to ECR" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green
Write-Host ""

# Check if AWS CLI is installed
if (-not (Get-Command aws -ErrorAction SilentlyContinue)) {
    Write-Host "❌ AWS CLI is not installed. Please install it first." -ForegroundColor Red
    Write-Host "   Visit: https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html" -ForegroundColor Yellow
    exit 1
}

# Check if Docker is installed
if (-not (Get-Command docker -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Docker is not installed. Please install it first." -ForegroundColor Red
    Write-Host "   Visit: https://docs.docker.com/get-docker/" -ForegroundColor Yellow
    exit 1
}

# Check if Docker daemon is running
try {
    docker info | Out-Null
} catch {
    Write-Host "❌ Docker daemon is not running. Please start Docker first." -ForegroundColor Red
    exit 1
}

# Get AWS account ID
Write-Host "🔍 Getting AWS account information..." -ForegroundColor Cyan
try {
    $AWS_ACCOUNT_ID = aws sts get-caller-identity --query Account --output text
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to get AWS account ID"
    }
} catch {
    Write-Host "❌ Failed to get AWS account ID. Make sure you're authenticated with AWS CLI." -ForegroundColor Red
    Write-Host "   Run: aws configure" -ForegroundColor Yellow
    exit 1
}

# ECR repository URI
$ECR_REPOSITORY_URI = "${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPOSITORY_NAME}"

Write-Host ""
Write-Host "📦 Repository: $ECR_REPOSITORY_URI" -ForegroundColor White
Write-Host "🏷️  Image tag: $IMAGE_TAG" -ForegroundColor White
Write-Host "🌍 AWS Region: $AWS_REGION" -ForegroundColor White
Write-Host "👤 AWS Account: $AWS_ACCOUNT_ID" -ForegroundColor White
Write-Host ""

# Step 1: Create ECR repository if it doesn't exist
Write-Host "📋 Step 1: Creating ECR repository if it doesn't exist..." -ForegroundColor Cyan
try {
    aws ecr describe-repositories --repository-names $ECR_REPOSITORY_NAME --region $AWS_REGION | Out-Null
} catch {
    Write-Host "   Creating new ECR repository: $ECR_REPOSITORY_NAME" -ForegroundColor Yellow
    aws ecr create-repository --repository-name $ECR_REPOSITORY_NAME --region $AWS_REGION --image-scanning-configuration scanOnPush=true
}
Write-Host "✅ ECR repository ready" -ForegroundColor Green

# Step 2: Authenticate Docker to ECR
Write-Host ""
Write-Host "🔐 Step 2: Authenticating Docker with ECR..." -ForegroundColor Cyan
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin "${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com"
Write-Host "✅ Docker authenticated with ECR" -ForegroundColor Green

# Step 3: Build the Docker image
Write-Host ""
Write-Host "🔨 Step 3: Building Docker image..." -ForegroundColor Cyan
Write-Host "   Building image: ${ECR_REPOSITORY_NAME}:${IMAGE_TAG}" -ForegroundColor Yellow
docker build -t "${ECR_REPOSITORY_NAME}:${IMAGE_TAG}" .
Write-Host "✅ Docker image built successfully" -ForegroundColor Green

# Step 4: Tag the image for ECR
Write-Host ""
Write-Host "🏷️  Step 4: Tagging image for ECR..." -ForegroundColor Cyan
docker tag "${ECR_REPOSITORY_NAME}:${IMAGE_TAG}" "${ECR_REPOSITORY_URI}:${IMAGE_TAG}"
docker tag "${ECR_REPOSITORY_NAME}:${IMAGE_TAG}" "${ECR_REPOSITORY_URI}:latest"
Write-Host "✅ Image tagged for ECR" -ForegroundColor Green

# Step 5: Push the image to ECR
Write-Host ""
Write-Host "📤 Step 5: Pushing image to ECR..." -ForegroundColor Cyan
Write-Host "   Pushing: ${ECR_REPOSITORY_URI}:${IMAGE_TAG}" -ForegroundColor Yellow
docker push "${ECR_REPOSITORY_URI}:${IMAGE_TAG}"
Write-Host "   Pushing: ${ECR_REPOSITORY_URI}:latest" -ForegroundColor Yellow
docker push "${ECR_REPOSITORY_URI}:latest"
Write-Host "✅ Image pushed to ECR successfully" -ForegroundColor Green

Write-Host ""
Write-Host "🎉 === Deployment Complete ===" -ForegroundColor Green
Write-Host "📦 Image URI: ${ECR_REPOSITORY_URI}:${IMAGE_TAG}" -ForegroundColor White
Write-Host "📦 Latest URI: ${ECR_REPOSITORY_URI}:latest" -ForegroundColor White
Write-Host ""
Write-Host "🐳 To run this container locally with required environment variables:" -ForegroundColor Cyan
Write-Host "docker run -p 3030:3030 \\" -ForegroundColor White
Write-Host "  -e NODE_ENV=production \\" -ForegroundColor White
Write-Host "  -e PORT=3030 \\" -ForegroundColor White
Write-Host "  -e HOST=0.0.0.0 \\" -ForegroundColor White
Write-Host "  -e AWS_ACCESS_KEY_ID=your_aws_access_key \\" -ForegroundColor White
Write-Host "  -e AWS_SECRET_ACCESS_KEY=your_aws_secret_key \\" -ForegroundColor White
Write-Host "  -e AWS_REGION=ap-southeast-1 \\" -ForegroundColor White
Write-Host "  -e WEBHOOK_ENABLED=false \\" -ForegroundColor White
Write-Host "  -e WEBHOOK_URL=https://your-webhook-url.com/webhook \\" -ForegroundColor White
Write-Host "  -e WEBHOOK_SECRET=your_webhook_secret \\" -ForegroundColor White
Write-Host "  -e DEFAULT_COUNTRY_CODE=60 \\" -ForegroundColor White
Write-Host "  -v `$(pwd)/whatsapp-session:/app/whatsapp-session \\" -ForegroundColor White
Write-Host "  ${ECR_REPOSITORY_URI}:${IMAGE_TAG}" -ForegroundColor White

Write-Host ""
Write-Host "☁️  To deploy to ECS, create a task definition with these environment variables:" -ForegroundColor Cyan
Write-Host "  - NODE_ENV=production" -ForegroundColor White
Write-Host "  - PORT=3030" -ForegroundColor White
Write-Host "  - HOST=0.0.0.0" -ForegroundColor White
Write-Host "  - AWS_ACCESS_KEY_ID (for S3 access)" -ForegroundColor White
Write-Host "  - AWS_SECRET_ACCESS_KEY (for S3 access)" -ForegroundColor White
Write-Host "  - AWS_REGION=ap-southeast-1" -ForegroundColor White
Write-Host "  - WEBHOOK_ENABLED=true/false" -ForegroundColor White
Write-Host "  - WEBHOOK_URL=https://your-webhook-url.com/webhook" -ForegroundColor White
Write-Host "  - WEBHOOK_SECRET=your_webhook_secret" -ForegroundColor White
Write-Host "  - DEFAULT_COUNTRY_CODE=60" -ForegroundColor White
Write-Host "  - SESSIONS_DIR=whatsapp-session" -ForegroundColor White
Write-Host ""
Write-Host "📝 Note: Make sure to mount a persistent volume for the whatsapp-session directory" -ForegroundColor Yellow
Write-Host "    to preserve WhatsApp authentication between container restarts." -ForegroundColor Yellow
