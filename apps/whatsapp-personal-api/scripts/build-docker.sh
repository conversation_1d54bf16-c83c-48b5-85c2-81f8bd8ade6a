#!/bin/bash

# WhatsApp API - Docker Build Script
# This script builds the WhatsApp API Docker image locally

# Exit on error
set -e

# Configuration
IMAGE_NAME="whatsapp-api"
IMAGE_TAG="latest"

# Optional: Use git commit hash as tag for versioning
if command -v git &> /dev/null && [ -d .git ]; then
    GIT_COMMIT=$(git rev-parse --short HEAD)
    IMAGE_TAG="${IMAGE_TAG}-${GIT_COMMIT}"
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install it first."
    echo "   Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Docker daemon is running
if ! docker info &> /dev/null; then
    echo "❌ Docker daemon is not running. Please start Docker first."
    exit 1
fi

echo ""
echo "🔨 === WhatsApp API - Building Docker Image ==="
echo "📦 Image name: ${IMAGE_NAME}:${IMAGE_TAG}"
echo ""

# Build the Docker image
echo "🔨 Building Docker image..."
echo "   Building image: ${IMAGE_NAME}:${IMAGE_TAG}"
docker build -t ${IMAGE_NAME}:${IMAGE_TAG} .

# Also tag as latest
docker tag ${IMAGE_NAME}:${IMAGE_TAG} ${IMAGE_NAME}:latest

echo ""
echo "✅ Docker image built successfully!"
echo "📦 Image: ${IMAGE_NAME}:${IMAGE_TAG}"
echo "📦 Latest: ${IMAGE_NAME}:latest"
echo ""
echo "🐳 To run this container locally:"
echo "docker run -p 3030:3030 \\"
echo "  -e NODE_ENV=production \\"
echo "  -e PORT=3030 \\"
echo "  -e HOST=0.0.0.0 \\"
echo "  -e AWS_ACCESS_KEY_ID=your_aws_access_key \\"
echo "  -e AWS_SECRET_ACCESS_KEY=your_aws_secret_key \\"
echo "  -e AWS_REGION=ap-southeast-1 \\"
echo "  -e WEBHOOK_ENABLED=false \\"
echo "  -e WEBHOOK_URL=https://your-webhook-url.com/webhook \\"
echo "  -e WEBHOOK_SECRET=your_webhook_secret \\"
echo "  -e DEFAULT_COUNTRY_CODE=60 \\"
echo "  -v \$(pwd)/whatsapp-session:/app/whatsapp-session \\"
echo "  ${IMAGE_NAME}:${IMAGE_TAG}"
echo ""
echo "📝 Note: Make sure to create a whatsapp-session directory and mount it"
echo "    to preserve WhatsApp authentication between container restarts."
