# WhatsApp API - Build and Deployment Scripts

This directory contains scripts for building and deploying the WhatsApp API application.

## Scripts Overview

| Script | Platform | Purpose |
|--------|----------|---------|
| `build-docker.sh` | Linux/macOS | Build Docker image locally |
| `build-docker.ps1` | Windows | Build Docker image locally |
| `deploy-to-ecr.sh` | Linux/macOS | Build and deploy to Amazon ECR |
| `deploy-to-ecr.ps1` | Windows | Build and deploy to Amazon ECR |
| `start.sh` | Linux/macOS | Start the application locally |

## Prerequisites

### For Local Development
- Node.js 16 or higher
- npm or yarn

### For Docker
- Docker Desktop installed and running
- Docker CLI available in PATH

### For AWS ECR Deployment
- AWS CLI installed and configured
- Docker Desktop installed and running
- AWS credentials with ECR permissions

## Usage

### 1. Build Docker Image Locally

**Linux/macOS:**
```bash
chmod +x scripts/build-docker.sh
./scripts/build-docker.sh
```

**Windows (PowerShell):**
```powershell
.\scripts\build-docker.ps1
```

This will:
- Build the Docker image with tag `whatsapp-api:latest`
- Optionally add git commit hash to the tag
- Provide instructions for running the container

### 2. Deploy to Amazon ECR

**Linux/macOS:**
```bash
chmod +x scripts/deploy-to-ecr.sh
./scripts/deploy-to-ecr.sh
```

**Windows (PowerShell):**
```powershell
.\scripts\deploy-to-ecr.ps1
```

This will:
- Check for required tools (AWS CLI, Docker)
- Create ECR repository if it doesn't exist
- Build the Docker image
- Tag the image for ECR
- Push the image to ECR
- Provide deployment instructions

### 3. Start Application Locally

**Linux/macOS:**
```bash
chmod +x scripts/start.sh
./scripts/start.sh
```

## Configuration

### Environment Variables

The following environment variables are used by the application:

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `NODE_ENV` | No | `development` | Node.js environment |
| `PORT` | No | `3030` | Application port |
| `HOST` | No | `0.0.0.0` | Application host |
| `AWS_ACCESS_KEY_ID` | Yes* | - | AWS access key for S3 |
| `AWS_SECRET_ACCESS_KEY` | Yes* | - | AWS secret key for S3 |
| `AWS_REGION` | No | `ap-southeast-1` | AWS region |
| `WEBHOOK_ENABLED` | No | `false` | Enable webhook notifications |
| `WEBHOOK_URL` | No | - | Webhook endpoint URL |
| `WEBHOOK_SECRET` | No | - | Webhook secret for authentication |
| `DEFAULT_COUNTRY_CODE` | No | `60` | Default country code for phone numbers |
| `SESSIONS_DIR` | No | `whatsapp-session` | WhatsApp session directory |

\* Required only if using S3 URLs for document sending

### AWS Configuration

Before deploying to ECR, make sure your AWS CLI is configured:

```bash
aws configure
```

You'll need:
- AWS Access Key ID
- AWS Secret Access Key
- Default region (e.g., `ap-southeast-1`)
- Default output format (e.g., `json`)

### ECR Repository Configuration

The scripts will automatically create an ECR repository named `whatsapp-api` in your configured AWS region. You can modify the repository name by editing the `ECR_REPOSITORY_NAME` variable in the deployment scripts.

## Running the Container

### Local Development

```bash
docker run -p 3030:3030 \
  -e NODE_ENV=development \
  -e PORT=3030 \
  -e HOST=0.0.0.0 \
  -e AWS_ACCESS_KEY_ID=your_aws_access_key \
  -e AWS_SECRET_ACCESS_KEY=your_aws_secret_key \
  -e AWS_REGION=ap-southeast-1 \
  -e WEBHOOK_ENABLED=false \
  -e DEFAULT_COUNTRY_CODE=60 \
  -v $(pwd)/whatsapp-session:/app/whatsapp-session \
  whatsapp-api:latest
```

### Production Deployment

```bash
docker run -p 3030:3030 \
  -e NODE_ENV=production \
  -e PORT=3030 \
  -e HOST=0.0.0.0 \
  -e AWS_ACCESS_KEY_ID=your_aws_access_key \
  -e AWS_SECRET_ACCESS_KEY=your_aws_secret_key \
  -e AWS_REGION=ap-southeast-1 \
  -e WEBHOOK_ENABLED=true \
  -e WEBHOOK_URL=https://your-webhook-url.com/webhook \
  -e WEBHOOK_SECRET=your_webhook_secret \
  -e DEFAULT_COUNTRY_CODE=60 \
  -v /path/to/persistent/whatsapp-session:/app/whatsapp-session \
  your-account-id.dkr.ecr.ap-southeast-1.amazonaws.com/whatsapp-api:latest
```

## Troubleshooting

### Common Issues

1. **Docker daemon not running**
   - Start Docker Desktop
   - Verify with `docker info`

2. **AWS CLI not configured**
   - Run `aws configure`
   - Verify with `aws sts get-caller-identity`

3. **Permission denied on scripts**
   - Make scripts executable: `chmod +x scripts/*.sh`

4. **ECR authentication failed**
   - Check AWS credentials
   - Verify ECR permissions in IAM

5. **Container fails to start**
   - Check environment variables
   - Verify volume mounts
   - Check container logs: `docker logs <container-id>`

### Logs and Debugging

To view container logs:
```bash
docker logs <container-name-or-id>
```

To run container in interactive mode:
```bash
docker run -it --entrypoint /bin/bash whatsapp-api:latest
```

## Security Considerations

1. **Environment Variables**: Never commit sensitive environment variables to version control
2. **AWS Credentials**: Use IAM roles when possible instead of access keys
3. **Webhook Secret**: Use a strong, random webhook secret
4. **Network Security**: Restrict container network access in production
5. **Volume Permissions**: Ensure proper permissions on mounted volumes

## Support

For issues related to:
- **Docker**: Check Docker documentation
- **AWS ECR**: Check AWS ECR documentation
- **WhatsApp API**: Check the main application README
