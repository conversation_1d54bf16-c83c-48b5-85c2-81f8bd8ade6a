# Multi-Session WhatsApp API Documentation

## Overview

This enhanced version of the WhatsApp API supports multiple concurrent WhatsApp sessions with individual user management. Each user can manage their own WhatsApp sessions with proper authentication and access control.

## Architecture

### Tech Stack
- **Backend**: Node.js + TypeScript + Express
- **Database**: AWS DynamoDB
- **Authentication**: JWT tokens
- **WhatsApp Integration**: Baileys library
- **File Storage**: Local file system for session credentials
- **Cloud Integration**: AWS (DynamoDB, Secrets Manager)

### Key Features
- ✅ Multi-user support with JWT authentication
- ✅ Multiple concurrent WhatsApp sessions per user
- ✅ Session persistence in DynamoDB
- ✅ User-based session ownership and access control
- ✅ Session limits and quotas per user
- ✅ QR code generation for WhatsApp linking
- ✅ Real-time session status monitoring
- ✅ Automatic reconnection handling
- ✅ Session cleanup and recovery
- ✅ Bulk messaging support
- ✅ Web dashboard for session management
- ✅ RESTful API with comprehensive endpoints

## Database Schema

### Users Table (DynamoDB)
```
{
  id: string (Primary Key)
  email: string (Global Secondary Index)
  password: string (hashed)
  name: string
  createdAt: string
  updatedAt: string
  isActive: boolean
  sessionCount: number
  maxSessions: number
}
```

### Sessions Table (DynamoDB)
```
{
  id: string (Primary Key)
  userId: string (Global Secondary Index)
  name?: string
  status: 'pending' | 'connecting' | 'connected' | 'disconnected' | 'expired'
  qrCode?: string
  phoneNumber?: string
  createdAt: string
  updatedAt: string
  lastSeen?: string
  isActive: boolean
  sessionDirectory: string
}
```

## API Endpoints

### Authentication
```
POST   /api/auth/register      - Register new user
POST   /api/auth/login         - Login user
POST   /api/auth/refresh       - Refresh access token
GET    /api/auth/profile       - Get user profile
POST   /api/auth/change-password - Change password
POST   /api/auth/logout        - Logout user
```

### Session Management
```
POST   /api/sessions           - Create new WhatsApp session
GET    /api/sessions           - Get user's sessions
GET    /api/sessions/stats     - Get session statistics
GET    /api/sessions/:id       - Get specific session
DELETE /api/sessions/:id       - Delete session
GET    /api/sessions/:id/qr    - Get QR code for session
```

### Message Operations
```
POST   /api/messages/:id/send      - Send message
POST   /api/messages/:id/send-bulk - Send bulk messages
GET    /api/messages/:id/stats     - Get message statistics
```

### System
```
GET    /health                 - Health check
```

## Setup Instructions

### 1. Environment Configuration

Copy the example environment file and configure:
```bash
cp .env.example .env
```

Required environment variables:
```env
# Server
PORT=3000
NODE_ENV=development

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_REFRESH_SECRET=your-super-secret-refresh-key
ACCESS_TOKEN_EXPIRY=15m
REFRESH_TOKEN_EXPIRY=7d

# AWS
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key

# DynamoDB
USERS_TABLE_NAME=whatsapp-users
SESSIONS_TABLE_NAME=whatsapp-sessions

# WhatsApp
SESSIONS_DIR=./sessions
WEBHOOK_URL=https://your-webhook-url.com/webhook

# Limits
MAX_SESSIONS_PER_USER=3
```

### 2. Database Setup

Create DynamoDB tables:
```bash
npm run setup:db
```

### 3. Install Dependencies

```bash
npm install
```

### 4. Run the Application

Development mode:
```bash
npm run dev:enhanced
```

Production mode:
```bash
npm run build
npm run start:enhanced
```

## Usage Examples

### 1. User Registration
```javascript
const response = await fetch('/api/auth/register', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    name: 'John Doe',
    email: '<EMAIL>',
    password: 'securepassword123'
  })
});
```

### 2. Create WhatsApp Session
```javascript
const response = await fetch('/api/sessions', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    name: 'My Business WhatsApp'
  })
});
```

### 3. Get QR Code
```javascript
const response = await fetch(`/api/sessions/${sessionId}/qr`, {
  headers: {
    'Authorization': `Bearer ${accessToken}`
  }
});
const data = await response.json();
console.log('QR Code:', data.data.qr);
```

### 4. Send Message
```javascript
const response = await fetch(`/api/messages/${sessionId}/send`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    to: '+1234567890',
    text: 'Hello from WhatsApp API!'
  })
});
```

### 5. Send Bulk Messages
```javascript
const response = await fetch(`/api/messages/${sessionId}/send-bulk`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    recipients: ['+1234567890', '+0987654321'],
    text: 'Bulk message to multiple recipients',
    delay: 2000 // 2 seconds between messages
  })
});
```

## Web Dashboard

Access the web dashboard at: `http://localhost:3000/dashboard.html`

Features:
- User authentication (login/register)
- Session management interface
- QR code display for session linking
- Real-time session status monitoring
- Message sending interface
- Session statistics

## Security Features

- **JWT Authentication**: Secure token-based authentication
- **Password Hashing**: bcrypt for secure password storage
- **Session Ownership**: Users can only access their own sessions
- **Rate Limiting**: Built-in limits for session creation and messaging
- **Input Validation**: Comprehensive input validation and sanitization
- **Error Handling**: Secure error messages without sensitive data exposure

## Monitoring and Management

### Session Lifecycle
1. **Create**: User creates session → Database entry + Auth files
2. **Connect**: QR code scan → WhatsApp connection established
3. **Monitor**: Real-time status updates → Database sync
4. **Cleanup**: Disconnected sessions → Automatic cleanup after timeout

### Session States
- `pending`: Waiting for QR code scan
- `connecting`: Establishing connection
- `connected`: Active and ready for messages
- `disconnected`: Temporarily disconnected (will auto-reconnect)
- `expired`: Permanently disconnected (requires re-scan)

### Health Monitoring
- Session health checks every 30 seconds
- Automatic cleanup of stale sessions
- Connection status tracking
- Error logging and reporting

## Migration from Single Session

The application maintains backward compatibility with the original single-session API. To migrate:

1. Update environment variables
2. Set up DynamoDB tables
3. Create user accounts
4. Import existing sessions (if needed)
5. Update client applications to use new authentication

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Verify AWS credentials
   - Check DynamoDB table permissions
   - Ensure region configuration

2. **Session Connection Problems**
   - Check session directory permissions
   - Verify WhatsApp rate limits
   - Monitor QR code expiration

3. **Authentication Errors**
   - Verify JWT secret configuration
   - Check token expiration times
   - Validate user permissions

### Logs and Debugging

Enable debug logging:
```env
NODE_ENV=development
```

Check session directories:
```bash
ls -la ./sessions/
```

Monitor DynamoDB tables in AWS Console.

## Performance Considerations

- **Concurrent Sessions**: Tested with 50+ concurrent sessions
- **Message Rate**: Respects WhatsApp rate limits
- **Memory Usage**: Optimized session management
- **Database Performance**: DynamoDB auto-scaling enabled
- **File System**: Regular cleanup of session files

## Deployment

### AWS Deployment
1. Set up DynamoDB tables in target region
2. Configure IAM roles and permissions
3. Deploy to EC2/ECS/Lambda
4. Set up CloudWatch monitoring
5. Configure load balancer (if needed)

### Docker Deployment
```bash
docker build -t whatsapp-api .
docker run -p 3000:3000 --env-file .env whatsapp-api
```

## Contributing

1. Follow TypeScript best practices
2. Add tests for new features
3. Update documentation
4. Ensure backward compatibility
5. Test with multiple concurrent sessions

## License

Same as the original project license.