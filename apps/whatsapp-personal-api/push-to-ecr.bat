@echo off
echo === Pushing WhatsApp API Docker Image to ECR ===

REM Set variables
set AWS_PROFILE=PlaygroundAdmin-************
set AWS_REGION=ap-southeast-1
set ECR_REPO_NAME=whatsapp-api-poc
set IMAGE_TAG=latest

REM Get AWS account ID
echo Retrieving AWS account ID...
for /f "tokens=*" %%i in ('aws sts get-caller-identity --query "Account" --output text --profile %AWS_PROFILE%') do set AWS_ACCOUNT_ID=%%i

if "%AWS_ACCOUNT_ID%"=="" (
    echo Failed to retrieve AWS account ID. Please check your AWS SSO session.
    echo Run 'aws sso login --profile %AWS_PROFILE%' to start a new session.
    exit /b 1
)

echo AWS Account ID: %AWS_ACCOUNT_ID%

REM Build Docker image
echo Building Docker image...
docker build -t %ECR_REPO_NAME%:%IMAGE_TAG% .

if %ERRORLEVEL% neq 0 (
    echo Failed to build Docker image.
    exit /b 1
)

REM Log in to ECR
echo Logging in to ECR...
echo ECR Registry: %AWS_ACCOUNT_ID%.dkr.ecr.%AWS_REGION%.amazonaws.com
echo AWS Profile: %AWS_PROFILE%
echo AWS Region: %AWS_REGION%

REM Test AWS credentials
echo Testing AWS credentials...
aws sts get-caller-identity --profile %AWS_PROFILE%

if %ERRORLEVEL% neq 0 (
    echo ERROR: AWS credentials test failed. Your AWS session may have expired.
    echo Run 'aws sso login --profile %AWS_PROFILE%' to start a new session.
    exit /b 1
)

REM Check if ECR service is available in the region
echo Checking if ECR service is available in region %AWS_REGION%...
aws ecr get-authorization-token --region %AWS_REGION% --profile %AWS_PROFILE%

if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to get ECR authorization token.
    echo This could be because:
    echo 1. ECR service is not available in region %AWS_REGION%
    echo 2. Your AWS profile does not have permission to access ECR
    echo 3. Your AWS session has expired
    echo Try running 'aws sso login --profile %AWS_PROFILE%' to refresh your session.
    exit /b 1
)

REM Perform ECR login
echo Performing ECR login...
aws ecr get-login-password --region %AWS_REGION% --profile %AWS_PROFILE% > ecr-password.tmp
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to get ECR login password.
    del ecr-password.tmp 2>nul
    exit /b 1
)

type ecr-password.tmp | docker login --username AWS --password-stdin %AWS_ACCOUNT_ID%.dkr.ecr.%AWS_REGION%.amazonaws.com
set LOGIN_RESULT=%ERRORLEVEL%
del ecr-password.tmp

if %LOGIN_RESULT% neq 0 (
    echo ERROR: Failed to log in to ECR.
    echo This could be because:
    echo 1. Docker daemon is not running
    echo 2. The ECR registry URL is incorrect
    echo 3. Network connectivity issues
    echo Please check your Docker installation and network connection.
    exit /b 1
)

echo Successfully logged in to ECR.

REM Check if repository exists, create if it doesn't
echo Checking if ECR repository exists...
aws ecr describe-repositories --repository-names %ECR_REPO_NAME% --profile %AWS_PROFILE% --region %AWS_REGION% > nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Creating ECR repository...
    aws ecr create-repository --repository-name %ECR_REPO_NAME% --profile %AWS_PROFILE% --region %AWS_REGION%

    if %ERRORLEVEL% neq 0 (
        echo Failed to create ECR repository.
        exit /b 1
    )
)

REM Tag Docker image
echo Tagging Docker image...
docker tag %ECR_REPO_NAME%:%IMAGE_TAG% %AWS_ACCOUNT_ID%.dkr.ecr.%AWS_REGION%.amazonaws.com/%ECR_REPO_NAME%:%IMAGE_TAG%

REM Push Docker image to ECR
echo Pushing Docker image to ECR...
docker push %AWS_ACCOUNT_ID%.dkr.ecr.%AWS_REGION%.amazonaws.com/%ECR_REPO_NAME%:%IMAGE_TAG%

if %ERRORLEVEL% neq 0 (
    echo Failed to push Docker image to ECR.
    exit /b 1
)

echo === Successfully pushed WhatsApp API Docker image to ECR ===
echo Repository: %AWS_ACCOUNT_ID%.dkr.ecr.%AWS_REGION%.amazonaws.com/%ECR_REPO_NAME%
echo Tag: %IMAGE_TAG%
