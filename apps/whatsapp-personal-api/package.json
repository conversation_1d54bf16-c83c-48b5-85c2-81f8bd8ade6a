{"name": "whatsapp-api", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node whatsapp-web-server.js", "dev": "nodemon whatsapp-web-server.js", "build": "tsc", "start:enhanced": "node dist/index.enhanced.js", "dev:enhanced": "npx ts-node src/index.enhanced.ts", "setup:db": "npx ts-node src/database/setup-tables.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@adiwajshing/keyed-db": "^0.2.4", "@aws-sdk/client-dynamodb": "^3.840.0", "@aws-sdk/client-secrets-manager": "^3.830.0", "@aws-sdk/lib-dynamodb": "^3.840.0", "@whiskeysockets/baileys": "^6.2.1", "aws-sdk": "^2.1692.0", "axios": "^1.6.7", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "qrcode": "^1.5.3", "qrcode-terminal": "^0.12.0", "ws": "^8.16.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^22.15.14", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}