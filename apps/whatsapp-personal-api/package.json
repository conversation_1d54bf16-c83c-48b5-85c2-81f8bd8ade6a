{"name": "whatsapp-api", "version": "1.0.0", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --testPathPattern=integration", "test:unit": "jest --testPathPattern=unit", "start": "node dist/index.js", "start:dev": "node whatsapp-web-server.js", "dev": "nodemon src/index.ts", "build": "tsc", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@adiwajshing/keyed-db": "^0.2.4", "@aws-sdk/client-dynamodb": "^3.400.0", "@aws-sdk/client-secrets-manager": "^3.830.0", "@aws-sdk/lib-dynamodb": "^3.400.0", "@hapi/boom": "^10.0.1", "@whiskeysockets/baileys": "^6.2.1", "aws-sdk": "^2.1692.0", "axios": "^1.6.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "joi": "^17.9.2", "node-cron": "^3.0.2", "qrcode": "^1.5.3", "qrcode-terminal": "^0.12.0", "winston": "^3.10.0", "ws": "^8.16.0"}, "devDependencies": {"@types/express": "^5.0.1", "@types/jest": "^29.5.5", "@types/node": "^22.15.14", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "eslint": "^8.49.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}