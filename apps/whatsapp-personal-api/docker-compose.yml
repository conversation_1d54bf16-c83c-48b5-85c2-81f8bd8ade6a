version: '3.8'

services:
  whatsapp-api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "${PORT:-3030}:${PORT:-3030}"
    volumes:
      - ./whatsapp-session:/app/whatsapp-session  # Volume for WhatsApp session data
      - whatsapp_node_modules:/app/node_modules  # Volume for node_modules to preserve patched files
    env_file:
      - .env
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - PORT=${PORT:-3030}
      - HOST=${HOST:-0.0.0.0}
      - WEBHOOK_URL=${WEBHOOK_URL:-}
      - WEBHOOK_ENABLED=${WEBHOOK_ENABLED:-false}
      - WEBHOOK_SECRET=${WEBHOOK_SECRET:-}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${PORT:-3030}/api/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

volumes:
  whatsapp_node_modules:  # Named volume for node_modules
