# WhatsApp API with Webhook Integration

A WhatsApp API built with Bailey<PERSON> that allows you to send and receive WhatsApp messages through a web interface and webhook integration.

## Features

- Connect to WhatsApp via QR code scanning
- Send text and media messages
- Receive messages and forward them to a webhook
- WhatsApp Web-like UI
- Session management with automatic reconnection
- Webhook configuration for integration with external chatbot applications

## Prerequisites

- Node.js (v14 or higher)
- npm or yarn

## Installation

1. Clone the repository:
   ```
   git clone <your-bitbucket-repo-url>
   cd whatsapp-api
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Start the server:
   ```
   node whatsapp-web-server.js
   ```

4. Open your browser and navigate to:
   ```
   http://localhost:3030
   ```

## Usage

### Connecting to WhatsApp

1. Open the application in your browser
2. Scan the QR code with your WhatsApp mobile app
3. Once connected, you can start sending and receiving messages

### Sending Messages

1. Navigate to the Send Message page
2. Enter the recipient's phone number
3. Type your message
4. Click "Send Message"

### Webhook Integration

1. Navigate to the Send Message page
2. Enable the webhook toggle
3. Enter your webhook URL
4. Optionally add a secret key for security
5. Save the configuration
6. Test the webhook to ensure it's working properly

When someone sends a message to your WhatsApp account, your webhook will receive a notification with the following payload structure:

```json
{
  "event": "message",
  "timestamp": "2023-06-01T12:34:56.789Z",
  "data": {
    "id": "message-id",
    "from": "<EMAIL>",
    "pushName": "Sender Name",
    "timestamp": **********,
    "message": {
      "type": "text",
      "text": "Hello, world!"
    }
  }
}
```

## API Endpoints

- `GET /api/status` - Check connection status
- `POST /api/refresh-qr` - Refresh QR code
- `POST /api/logout` - Logout from WhatsApp
- `POST /api/send` - Send a message
- `POST /api/webhook/config` - Configure webhook
- `POST /api/webhook/test` - Test webhook

## License

ISC
