<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Send WhatsApp Message</title>
  <style>
    :root {
      --primary-color: #00a884;
      --secondary-color: #008069;
      --light-green: #dcf8c6;
      --background-color: #eae6df;
      --background-pattern: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGwSURBVEhL1ZVBbsIwEEUNKlKXLCohNt0jVeIGUE7QG3CFXoGtl2FdtBv2bPMTz+AkJKTNpqr0JM/YHv/xZ+LUNcbYRvTxOv5I1ByffuKVUSA1xY0vEzXHVQNVUkFmYFWfpwY9oAa9HUdFpj1eTFQeA2YgxfQI8PZtTm4uEOJ7Hq/TFiuOVE6o0wYrjgLTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o08bXGPMPu0E5mB/6h6EAAAAASUVORK5CYII=");
      --text-color: #41525d;
      --text-secondary: #667781;
      --border-color: #d1d7db;
    }

    body {
      font-family: Segoe <PERSON>I, Helvetica Neue, Helvetica, Lucida <PERSON>, Arial, Ubuntu, Cantarell, Fira <PERSON>, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 20px;
      background-color: var(--background-color);
      background-image: var(--background-pattern);
      color: var(--text-color);
    }

    .container {
      max-width: 900px;
      margin: 0 auto;
      background-color: #fff;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 17px 50px 0 rgba(11,20,26,.19), 0 12px 15px 0 rgba(11,20,26,.24);
    }

    h1 {
      color: var(--primary-color);
      margin-bottom: 20px;
      text-align: center;
      font-weight: 300;
    }

    .form-group {
      margin-bottom: 15px;
    }

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
      color: var(--text-color);
    }

    input[type="text"],
    input[type="url"],
    textarea {
      width: 100%;
      padding: 10px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      box-sizing: border-box;
      font-size: 16px;
      transition: border-color 0.3s;
    }

    input[type="text"]:focus,
    input[type="url"]:focus,
    textarea:focus {
      border-color: var(--primary-color);
      outline: none;
    }

    textarea {
      height: 150px;
      resize: vertical;
    }

    button {
      background-color: var(--primary-color);
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      display: inline-block;
      transition: background-color 0.3s;
    }

    button:hover {
      background-color: var(--secondary-color);
    }

    .button-group {
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
    }

    .alert {
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 15px;
    }

    .alert-success {
      background-color: #E8F5E9;
      color: #2E7D32;
      border: 1px solid #C8E6C9;
    }

    .alert-error {
      background-color: #FFEBEE;
      color: #C62828;
      border: 1px solid #FFCDD2;
    }

    .hidden {
      display: none;
    }

    .nav-link {
      color: var(--primary-color);
      text-decoration: none;
      margin-right: 15px;
      font-weight: 500;
    }

    .nav-link:hover {
      text-decoration: underline;
    }

    .nav-bar {
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;
    }

    .loading {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      border-top-color: var(--primary-color);
      animation: spin 1s ease-in-out infinite;
      margin-right: 10px;
      vertical-align: middle;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    #sendingIndicator {
      display: flex;
      align-items: center;
      margin-left: 15px;
    }

    .message-history {
      margin-top: 30px;
      border-top: 1px solid #eee;
      padding-top: 20px;
    }

    .message-item {
      padding: 10px;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
    }

    .message-item:last-child {
      border-bottom: none;
    }

    .message-details {
      flex: 1;
    }

    .message-recipient {
      font-weight: bold;
    }

    .message-text {
      color: #666;
    }

    .message-time {
      color: #999;
      font-size: 12px;
    }

    .tab-container {
      margin-bottom: 20px;
    }

    .tab-buttons {
      display: flex;
      border-bottom: 1px solid var(--border-color);
    }

    .tab-button {
      padding: 10px 20px;
      background-color: #f5f5f5;
      border: none;
      cursor: pointer;
      border-radius: 4px 4px 0 0;
      margin-right: 5px;
    }

    .tab-button.active {
      background-color: var(--primary-color);
      color: white;
    }

    .tab-content {
      display: none;
      padding: 20px;
      border: 1px solid var(--border-color);
      border-top: none;
      border-radius: 0 0 4px 4px;
    }

    .tab-content.active {
      display: block;
    }

    .webhook-section {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid var(--border-color);
    }

    .webhook-status {
      display: inline-block;
      padding: 5px 10px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
      margin-left: 10px;
    }

    .webhook-status.enabled {
      background-color: #E8F5E9;
      color: #2E7D32;
    }

    .webhook-status.disabled {
      background-color: #FFEBEE;
      color: #C62828;
    }

    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 50px;
      height: 24px;
      margin-left: 10px;
    }

    .toggle-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .toggle-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 24px;
    }

    .toggle-slider:before {
      position: absolute;
      content: "";
      height: 16px;
      width: 16px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }

    input:checked + .toggle-slider {
      background-color: var(--primary-color);
    }

    input:checked + .toggle-slider:before {
      transform: translateX(26px);
    }

    .webhook-form {
      margin-top: 15px;
    }

    .webhook-test-result {
      margin-top: 10px;
      padding: 10px;
      border-radius: 4px;
      font-size: 14px;
    }

    .webhook-test-result.success {
      background-color: #E8F5E9;
      color: #2E7D32;
    }

    .webhook-test-result.error {
      background-color: #FFEBEE;
      color: #C62828;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="nav-bar">
      <a href="/" class="nav-link">Home</a>
      <a href="/whatsapp-api/send-ui" class="nav-link">Send Message</a>
    </div>

    <h1>Send WhatsApp Message</h1>

    <div id="alertContainer" class="alert hidden"></div>

    <div class="webhook-section">
      <h2>
        Webhook Configuration
        <span id="webhookStatus" class="webhook-status disabled">Disabled</span>
        <label class="toggle-switch">
          <input type="checkbox" id="webhookToggle">
          <span class="toggle-slider"></span>
        </label>
      </h2>
      <p>Configure a webhook URL to receive notifications when messages are received from your contacts.</p>

      <div id="webhookForm" class="webhook-form hidden">
        <div class="form-group">
          <label for="webhookUrl">Webhook URL:</label>
          <input type="url" id="webhookUrl" placeholder="https://your-chatbot-app.com/webhook" required>
          <p style="font-size: 12px; color: var(--text-secondary); margin-top: 5px;">
            This URL will receive POST requests with message data when new messages are received.
          </p>
        </div>

        <div class="form-group">
          <label for="webhookSecret">Secret Key (Optional):</label>
          <input type="text" id="webhookSecret" placeholder="Your secret key for webhook verification">
          <p style="font-size: 12px; color: var(--text-secondary); margin-top: 5px;">
            If provided, this key will be sent in the X-Webhook-Secret header for verification.
          </p>
        </div>

        <div class="button-group">
          <button id="saveWebhookButton" type="button">Save Webhook</button>
          <button id="testWebhookButton" type="button">Test Webhook</button>
          <div id="webhookSavingIndicator" class="hidden">
            <div class="loading"></div>
            <span>Saving...</span>
          </div>
        </div>

        <div id="webhookTestResult" class="webhook-test-result hidden"></div>
      </div>
    </div>

    <div class="tab-container">
      <div class="tab-buttons">
        <button class="tab-button active" data-tab="text">Text Message</button>
        <button class="tab-button" data-tab="media">Media Message</button>
      </div>

      <div id="textTab" class="tab-content active">
        <form id="messageForm">
          <div class="form-group">
            <label for="phone">Phone Number:</label>
            <input type="text" id="phone" name="phone" placeholder="e.g., 628123456789 or 08123456789" required>
          </div>

          <div class="form-group">
            <label for="message">Message:</label>
            <textarea id="message" name="message" placeholder="Type your message here..." required></textarea>
          </div>

          <div class="button-group">
            <button type="submit">Send Message</button>
            <div id="sendingIndicator" class="hidden">
              <div class="loading"></div>
              <span>Sending...</span>
            </div>
          </div>
        </form>
      </div>

      <div id="mediaTab" class="tab-content">
        <form id="mediaForm">
          <div class="form-group">
            <label for="mediaPhone">Phone Number:</label>
            <input type="text" id="mediaPhone" name="phone" placeholder="e.g., 628123456789 or 08123456789" required>
          </div>

          <div class="form-group">
            <label for="mediaUrl">Media URL:</label>
            <input type="text" id="mediaUrl" name="mediaUrl" placeholder="https://example.com/image.jpg" required>
          </div>

          <div class="form-group">
            <label for="mediaCaption">Caption (optional):</label>
            <textarea id="mediaCaption" name="message" placeholder="Add a caption to your media..."></textarea>
          </div>

          <div class="button-group">
            <button type="submit">Send Media</button>
            <div id="mediaSendingIndicator" class="hidden">
              <div class="loading"></div>
              <span>Sending...</span>
            </div>
          </div>
        </form>
      </div>
    </div>

    <div class="message-history">
      <h2>Recent Messages</h2>
      <div id="messageHistory">
        <!-- Message history will be displayed here -->
        <p>No messages sent yet.</p>
      </div>
    </div>
  </div>

  <!-- Include the shared WebSocket connection script -->
  <script src="/js/websocket-connection.js"></script>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const messageForm = document.getElementById('messageForm');
      const mediaForm = document.getElementById('mediaForm');
      const alertContainer = document.getElementById('alertContainer');
      const sendingIndicator = document.getElementById('sendingIndicator');
      const mediaSendingIndicator = document.getElementById('mediaSendingIndicator');
      const messageHistory = document.getElementById('messageHistory');
      const tabButtons = document.querySelectorAll('.tab-button');
      const tabContents = document.querySelectorAll('.tab-content');

      // Get the shared WebSocket manager
      const wsManager = window.WebSocketManager.getInstance();

      // Webhook elements
      const webhookToggle = document.getElementById('webhookToggle');
      const webhookStatus = document.getElementById('webhookStatus');
      const webhookForm = document.getElementById('webhookForm');
      const webhookUrl = document.getElementById('webhookUrl');
      const webhookSecret = document.getElementById('webhookSecret');
      const saveWebhookButton = document.getElementById('saveWebhookButton');
      const testWebhookButton = document.getElementById('testWebhookButton');
      const webhookSavingIndicator = document.getElementById('webhookSavingIndicator');
      const webhookTestResult = document.getElementById('webhookTestResult');

      // Message history
      const messages = JSON.parse(localStorage.getItem('whatsappMessages') || '[]');

      // Load webhook configuration
      const webhookConfig = JSON.parse(localStorage.getItem('webhookConfig') || '{"enabled": false, "url": "", "secret": ""}');

      // Initialize webhook UI
      webhookToggle.checked = webhookConfig.enabled;
      webhookUrl.value = webhookConfig.url;
      webhookSecret.value = webhookConfig.secret;

      if (webhookConfig.enabled) {
        webhookStatus.textContent = 'Enabled';
        webhookStatus.className = 'webhook-status enabled';
        webhookForm.classList.remove('hidden');
      }

      // Tab switching
      tabButtons.forEach(button => {
        button.addEventListener('click', () => {
          const tabId = button.getAttribute('data-tab');

          // Update active tab button
          tabButtons.forEach(btn => btn.classList.remove('active'));
          button.classList.add('active');

          // Show active tab content
          tabContents.forEach(content => content.classList.remove('active'));
          document.getElementById(tabId + 'Tab').classList.add('active');
        });
      });

      // Toggle webhook configuration
      webhookToggle.addEventListener('change', () => {
        if (webhookToggle.checked) {
          webhookStatus.textContent = 'Enabled';
          webhookStatus.className = 'webhook-status enabled';
          webhookForm.classList.remove('hidden');
        } else {
          webhookStatus.textContent = 'Disabled';
          webhookStatus.className = 'webhook-status disabled';
          webhookForm.classList.add('hidden');

          // Update webhook configuration
          webhookConfig.enabled = false;
          localStorage.setItem('webhookConfig', JSON.stringify(webhookConfig));

          // Update server webhook configuration
          updateServerWebhookConfig();
        }
      });

      // Save webhook configuration
      saveWebhookButton.addEventListener('click', async () => {
        const url = webhookUrl.value.trim();

        if (!url) {
          showAlert('Please enter a valid webhook URL', 'error');
          return;
        }

        // Show saving indicator
        webhookSavingIndicator.classList.remove('hidden');

        try {
          // Update webhook configuration
          webhookConfig.enabled = true;
          webhookConfig.url = url;
          webhookConfig.secret = webhookSecret.value.trim();

          // Save to local storage
          localStorage.setItem('webhookConfig', JSON.stringify(webhookConfig));

          // Update server webhook configuration
          await updateServerWebhookConfig();

          showAlert('Webhook configuration saved successfully!', 'success');
        } catch (error) {
          showAlert(`Error: ${error.message}`, 'error');
        } finally {
          // Hide saving indicator
          webhookSavingIndicator.classList.add('hidden');
        }
      });

      // Test webhook
      testWebhookButton.addEventListener('click', async () => {
        const url = webhookUrl.value.trim();

        if (!url) {
          showAlert('Please enter a valid webhook URL', 'error');
          return;
        }

        // Show saving indicator
        webhookSavingIndicator.classList.remove('hidden');
        webhookTestResult.classList.add('hidden');

        try {
          const response = await fetch('/api/webhook/test', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              url: url,
              secret: webhookSecret.value.trim()
            }),
          });

          const data = await response.json();

          if (response.ok) {
            webhookTestResult.textContent = 'Webhook test successful! Your chatbot application received the test notification.';
            webhookTestResult.className = 'webhook-test-result success';
          } else {
            webhookTestResult.textContent = `Webhook test failed: ${data.message}`;
            webhookTestResult.className = 'webhook-test-result error';
          }

          webhookTestResult.classList.remove('hidden');
        } catch (error) {
          webhookTestResult.textContent = `Error testing webhook: ${error.message}`;
          webhookTestResult.className = 'webhook-test-result error';
          webhookTestResult.classList.remove('hidden');
        } finally {
          // Hide saving indicator
          webhookSavingIndicator.classList.add('hidden');
        }
      });

      // Update server webhook configuration
      async function updateServerWebhookConfig() {
        try {
          const response = await fetch('/api/webhook/config', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(webhookConfig),
          });

          if (!response.ok) {
            const data = await response.json();
            throw new Error(data.message || 'Failed to update webhook configuration');
          }
        } catch (error) {
          console.error('Error updating webhook configuration:', error);
          throw error;
        }
      }

      // Display message history
      function updateMessageHistory() {
        if (messages.length === 0) {
          messageHistory.innerHTML = '<p>No messages sent yet.</p>';
          return;
        }

        messageHistory.innerHTML = '';

        // Display last 5 messages
        const recentMessages = messages.slice(0, 5);

        recentMessages.forEach(msg => {
          const messageItem = document.createElement('div');
          messageItem.className = 'message-item';

          messageItem.innerHTML = `
            <div class="message-details">
              <div class="message-recipient">To: ${msg.phone}</div>
              <div class="message-text">${msg.message || (msg.mediaUrl ? 'Media message' : '')}</div>
            </div>
            <div class="message-time">${new Date(msg.time).toLocaleString()}</div>
          `;

          messageHistory.appendChild(messageItem);
        });
      }

      // Show alert
      function showAlert(message, type) {
        alertContainer.textContent = message;
        alertContainer.className = `alert alert-${type}`;

        // Hide alert after 5 seconds
        setTimeout(() => {
          alertContainer.className = 'alert hidden';
        }, 5000);
      }

      // Handle text message form submission
      messageForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const phone = document.getElementById('phone').value.trim();
        const message = document.getElementById('message').value.trim();

        if (!phone || !message) {
          showAlert('Please fill in all fields', 'error');
          return;
        }

        // Show sending indicator
        sendingIndicator.classList.remove('hidden');

        try {
          const response = await fetch('/whatsapp-api/send', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ phone, message }),
          });

          const data = await response.json();

          if (response.ok) {
            showAlert('Message sent successfully!', 'success');
            messageForm.reset();

            // Add to message history
            messages.unshift({
              phone,
              message,
              time: new Date().toISOString(),
            });

            // Save to local storage
            localStorage.setItem('whatsappMessages', JSON.stringify(messages));

            // Update message history display
            updateMessageHistory();
          } else {
            showAlert(`Error: ${data.message}`, 'error');
          }
        } catch (error) {
          showAlert(`Error: ${error.message}`, 'error');
        } finally {
          // Hide sending indicator
          sendingIndicator.classList.add('hidden');
        }
      });

      // Handle media message form submission
      mediaForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const phone = document.getElementById('mediaPhone').value.trim();
        const mediaUrl = document.getElementById('mediaUrl').value.trim();
        const message = document.getElementById('mediaCaption').value.trim();

        if (!phone || !mediaUrl) {
          showAlert('Please fill in all required fields', 'error');
          return;
        }

        // Show sending indicator
        mediaSendingIndicator.classList.remove('hidden');

        try {
          const response = await fetch('/whatsapp-api/send', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ phone, message, mediaUrl }),
          });

          const data = await response.json();

          if (response.ok) {
            showAlert('Media message sent successfully!', 'success');
            mediaForm.reset();

            // Add to message history
            messages.unshift({
              phone,
              message: message || 'Media message',
              mediaUrl,
              time: new Date().toISOString(),
            });

            // Save to local storage
            localStorage.setItem('whatsappMessages', JSON.stringify(messages));

            // Update message history display
            updateMessageHistory();
          } else {
            showAlert(`Error: ${data.message}`, 'error');
          }
        } catch (error) {
          showAlert(`Error: ${error.message}`, 'error');
        } finally {
          // Hide sending indicator
          mediaSendingIndicator.classList.add('hidden');
        }
      });

      // Initialize message history
      updateMessageHistory();

      // Try to update server webhook configuration on page load
      if (webhookConfig.enabled) {
        updateServerWebhookConfig().catch(error => {
          console.error('Error updating webhook configuration on page load:', error);
        });
      }

      // Add WebSocket event listener to check connection status
      wsManager.addListener((data) => {
        if ((data.type === 'status' || data.type === 'connection') && !data.connected) {
          // If disconnected, show a warning
          showAlert('WhatsApp connection lost. Please return to the home page to reconnect.', 'error');
        }
      });

      // Check connection status on page load
      const currentState = wsManager.getStatus();
      if (!currentState.isConnected) {
        showAlert('Not connected to WhatsApp. Please return to the home page to connect.', 'error');
      }
    });
  </script>
</body>
</html>
