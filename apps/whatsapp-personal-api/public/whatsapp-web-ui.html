<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WhatsApp Web</title>
  <link rel="icon" href="https://static.whatsapp.net/rsrc.php/v3/yP/r/rYZqPCBaG70.png" type="image/png">
  <style>
    :root {
      --primary-color: #00a884;
      --secondary-color: #008069;
      --light-green: #dcf8c6;
      --background-color: #eae6df;
      --background-pattern: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGwSURBVEhL1ZVBbsIwEEUNKlKXLCohNt0jVeIGUE7QG3CFXoGtl2FdtBv2bPMTz+AkJKTNpqr0JM/YHv/xZ+LUNcbYRvTxOv5I1ByffuKVUSA1xY0vEzXHVQNVUkFmYFWfpwY9oAa9HUdFpj1eTFQeA2YgxfQI8PZtTm4uEOJ7Hq/TFiuOVE6o0wYrjgLTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o0wYrjlROqNMGK45UTqjTBiuOVE6o08bXGPMPu0E5mB/6h6EAAAAASUVORK5CYII=");
      --text-color: #41525d;
      --text-secondary: #667781;
      --border-color: #d1d7db;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: Segoe UI, Helvetica Neue, Helvetica, Lucida Grande, Arial, Ubuntu, Cantarell, Fira Sans, sans-serif;
      background-color: var(--background-color);
      background-image: var(--background-pattern);
      color: var(--text-color);
      height: 100vh;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    /* Header */
    .header {
      background-color: var(--secondary-color);
      height: 127px;
      width: 100%;
      position: relative;
    }

    /* Main content */
    .main-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      flex: 1;
      padding: 20px;
      position: relative;
      margin-top: -40px;
    }

    .card {
      background-color: white;
      border-radius: 3px;
      box-shadow: 0 17px 50px 0 rgba(11,20,26,.19), 0 12px 15px 0 rgba(11,20,26,.24);
      width: 100%;
      max-width: 1000px;
      display: flex;
      overflow: hidden;
      height: 500px;
    }

    .left-panel {
      width: 60%;
      padding: 60px 40px;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .right-panel {
      width: 40%;
      background-color: #f9f9fa;
      padding: 60px 40px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-left: 1px solid #e9edef;
    }

    .title {
      font-size: 28px;
      font-weight: 300;
      color: var(--text-color);
      margin-bottom: 20px;
    }

    .subtitle {
      font-size: 16px;
      color: var(--text-secondary);
      margin-bottom: 40px;
      line-height: 1.6;
    }

    .steps {
      list-style-type: decimal;
      padding-left: 20px;
      margin-bottom: 40px;
    }

    .steps li {
      margin-bottom: 20px;
      color: var(--text-secondary);
      font-size: 16px;
      line-height: 1.6;
    }

    .qr-container {
      position: relative;
      margin-bottom: 30px;
    }

    #qrCode {
      width: 264px;
      height: 264px;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      padding: 8px;
      box-sizing: content-box;
      border: 1px solid var(--border-color);
      border-radius: 5px;
      background-color: white;
    }

    #qrCode img {
      width: 264px;
      height: 264px;
      display: block;
    }

    .whatsapp-logo {
      position: absolute;
      bottom: -20px;
      left: 50%;
      transform: translateX(-50%);
      width: 40px;
      height: 40px;
      background-color: white;
      border-radius: 50%;
      padding: 5px;
      box-sizing: border-box;
      z-index: 2;
      box-shadow: 0 1px 3px rgba(0,0,0,0.12);
    }

    .whatsapp-logo img {
      width: 100%;
      height: 100%;
    }

    .status {
      margin: 0 auto 20px;
      padding: 8px 16px;
      border-radius: 16px;
      font-size: 14px;
      display: inline-flex;
      align-items: center;
      font-weight: 500;
    }

    .status.connecting {
      background-color: #FFF8E1;
      color: #FF9800;
    }

    .status.connected {
      background-color: #E8F5E9;
      color: #2E7D32;
    }

    .status.disconnected {
      background-color: #FFEBEE;
      color: #C62828;
    }

    .loading {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid rgba(18, 140, 126, 0.2);
      border-radius: 50%;
      border-top-color: var(--primary-color);
      animation: spin 1s ease-in-out infinite;
      margin-right: 8px;
      vertical-align: middle;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    .status-icon {
      margin-right: 8px;
      display: inline-flex;
    }

    .help-text {
      font-size: 14px;
      color: var(--text-secondary);
      text-align: center;
      margin-top: 20px;
    }

    .refresh-button {
      background-color: var(--primary-color);
      color: white;
      border: none;
      padding: 10px 24px;
      border-radius: 3px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      margin-top: 20px;
      display: none; /* Hidden by default, shown when needed */
    }

    .refresh-button:hover {
      background-color: var(--secondary-color);
    }

    .connected-container {
      text-align: center;
      display: none; /* Hidden by default, shown when connected */
    }

    .connected-icon {
      width: 80px;
      height: 80px;
      margin-bottom: 20px;
    }

    .connected-title {
      font-size: 24px;
      font-weight: 500;
      color: var(--primary-color);
      margin-bottom: 10px;
    }

    .connected-subtitle {
      font-size: 16px;
      color: var(--text-secondary);
      margin-bottom: 30px;
    }

    .action-button {
      background-color: var(--primary-color);
      color: white;
      border: none;
      padding: 10px 24px;
      border-radius: 3px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      text-decoration: none;
      display: inline-block;
    }

    .action-button:hover {
      background-color: var(--secondary-color);
    }

    .logout-button {
      background-color: transparent;
      color: var(--text-secondary);
      border: 1px solid var(--border-color);
      padding: 10px 24px;
      border-radius: 3px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      margin-top: 15px;
    }

    .logout-button:hover {
      background-color: #f0f2f5;
    }

    /* Footer */
    .footer {
      text-align: center;
      padding: 20px;
      color: var(--text-secondary);
      font-size: 14px;
    }

    /* Responsive adjustments */
    @media (max-width: 900px) {
      .card {
        flex-direction: column;
        height: auto;
      }

      .left-panel, .right-panel {
        width: 100%;
        padding: 30px 20px;
      }

      .right-panel {
        border-left: none;
        border-top: 1px solid #e9edef;
      }
    }
  </style>
</head>
<body>
  <div class="header"></div>

  <div class="main-content">
    <div class="card">
      <div class="left-panel">
        <h1 class="title">Use WhatsApp on your computer</h1>
        <p class="subtitle">Connect your WhatsApp account to send and receive messages from this web application.</p>

        <ol class="steps">
          <li>Open WhatsApp on your phone</li>
          <li>Tap <strong>Menu</strong> <span style="font-size: 20px; vertical-align: middle;">⋮</span> or <strong>Settings</strong> <span style="font-size: 20px; vertical-align: middle;">⚙️</span></li>
          <li>Select <strong>Linked Devices</strong></li>
          <li>Point your phone at this screen to capture the QR code</li>
        </ol>

        <p class="help-text">Need help to get started? <a href="https://faq.whatsapp.com/web/download-and-installation/how-to-use-whatsapp-web" target="_blank" style="color: var(--primary-color); text-decoration: none;">Learn more</a></p>
      </div>

      <div class="right-panel">
        <div id="qr-section">
          <div id="statusContainer" class="status connecting">
            <span class="status-icon"><span class="loading"></span></span>
            <span id="statusText">Waiting for QR code...</span>
          </div>

          <div class="qr-container">
            <div id="qrCode">
              <div class="loading"></div>
              <p>Waiting for QR code...</p>
              <div class="whatsapp-logo">
                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/6/6b/WhatsApp.svg/512px-WhatsApp.svg.png" alt="WhatsApp Logo">
              </div>
            </div>
          </div>

          <p class="help-text">Keep your phone connected to the internet</p>
          <button id="refreshQrButton" class="refresh-button">Refresh QR Code</button>
        </div>

        <div id="connected-section" class="connected-container">
          <svg class="connected-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z" fill="#00a884"/>
          </svg>
          <h2 class="connected-title">Connected</h2>
          <p class="connected-subtitle">WhatsApp is now connected to this device</p>
          <a href="/whatsapp-api/send-ui" class="action-button">Send Messages</a>
          <button id="logoutButton" class="logout-button">Log out</button>
        </div>
      </div>
    </div>
  </div>

  <div class="footer">
    <p>© 2023 WhatsApp API. All rights reserved.</p>
  </div>

  <!-- Include the shared WebSocket connection script -->
  <script src="/js/websocket-connection.js"></script>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const qrCodeElement = document.getElementById('qrCode');
      const statusContainer = document.getElementById('statusContainer');
      const statusText = document.getElementById('statusText');
      const qrSection = document.getElementById('qr-section');
      const connectedSection = document.getElementById('connected-section');
      const refreshQrButton = document.getElementById('refreshQrButton');
      const logoutButton = document.getElementById('logoutButton');

      // Get the shared WebSocket manager
      const wsManager = window.WebSocketManager.getInstance();

      // Function to update status display
      function updateStatus(status) {
        statusContainer.className = `status ${status}`;

        if (status === 'connecting') {
          statusText.innerHTML = '<span class="loading"></span> Waiting for connection...';
          qrSection.style.display = 'block';
          connectedSection.style.display = 'none';
          refreshQrButton.style.display = 'none';
        } else if (status === 'connected') {
          statusText.innerHTML = '<span style="color: #2E7D32;">✓</span> Connected';
          qrSection.style.display = 'none';
          connectedSection.style.display = 'block';
        } else if (status === 'disconnected') {
          statusText.innerHTML = '<span style="color: #C62828;">✗</span> Disconnected from WhatsApp';
          qrSection.style.display = 'block';
          connectedSection.style.display = 'none';
          refreshQrButton.style.display = 'block';
        }
      }

      // Function to display QR code
      function displayQRCode(qrData, isDataUrl) {
        // Clear previous QR code
        qrCodeElement.innerHTML = '';

        console.log("Displaying QR code");

        // Create a new QR code element
        if (qrData) {
          try {
            // Check if it's a data URL or raw data
            if (isDataUrl || (typeof qrData === 'string' && qrData.startsWith('data:image'))) {
              // For data URL
              console.log("Displaying QR code from data URL");
              const img = document.createElement('img');
              img.src = qrData;
              img.alt = 'WhatsApp QR Code';
              img.width = 264;
              img.height = 264;

              // Create a container for the QR code
              const qrContainer = document.createElement('div');
              qrContainer.style.position = 'relative';

              // Add the image to the container
              qrContainer.appendChild(img);

              // Add WhatsApp logo
              const logoDiv = document.createElement('div');
              logoDiv.className = 'whatsapp-logo';
              logoDiv.innerHTML = '<img src="https://upload.wikimedia.org/wikipedia/commons/thumb/6/6b/WhatsApp.svg/512px-WhatsApp.svg.png" alt="WhatsApp Logo">';
              qrContainer.appendChild(logoDiv);

              // Add the container to the QR code element
              qrCodeElement.appendChild(qrContainer);
            } else {
              // For raw QR data
              console.log("Generating QR code from raw data");

              // Use QR Server API
              const img = document.createElement('img');
              img.src = 'https://api.qrserver.com/v1/create-qr-code/?size=264x264&margin=0&data=' + encodeURIComponent(qrData);
              img.alt = 'WhatsApp QR Code';
              img.width = 264;
              img.height = 264;

              // Create a container for the QR code
              const qrContainer = document.createElement('div');
              qrContainer.style.position = 'relative';

              // Add the image to the container
              qrContainer.appendChild(img);

              // Add WhatsApp logo
              const logoDiv = document.createElement('div');
              logoDiv.className = 'whatsapp-logo';
              logoDiv.innerHTML = '<img src="https://upload.wikimedia.org/wikipedia/commons/thumb/6/6b/WhatsApp.svg/512px-WhatsApp.svg.png" alt="WhatsApp Logo">';
              qrContainer.appendChild(logoDiv);

              // Add the container to the QR code element
              qrCodeElement.appendChild(qrContainer);
            }
          } catch (error) {
            console.error("Error displaying QR code:", error);
            qrCodeElement.innerHTML = `
              <p>Error generating QR code: ${error.message}</p>
              <p>Please refresh the page to try again.</p>
            `;
          }
        } else {
          qrCodeElement.innerHTML = '<p>Error: No QR code data received</p>';
        }

        // Hide the refresh button when a new QR code is received
        refreshQrButton.style.display = 'none';
      }

      // Function to refresh QR code
      function refreshQrCode() {
        fetch('/whatsapp-api/refresh-qr', { method: 'POST' })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              console.log('QR code refresh requested');
              qrCodeElement.innerHTML = '<div class="loading"></div><p>Generating new QR code...</p>';
              refreshQrButton.style.display = 'none';
            } else {
              console.error('Failed to refresh QR code:', data.message);
              alert('Failed to refresh QR code: ' + data.message);
            }
          })
          .catch(error => {
            console.error('Error refreshing QR code:', error);
            alert('Error refreshing QR code. Please try again.');
          });
      }

      // Function to logout
      function logout() {
        if (confirm('Are you sure you want to log out? This will disconnect your WhatsApp account from this device.')) {
          fetch('/whatsapp-api/logout', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
              if (data.success) {
                console.log('Logout successful');
                // Reload the page to show the QR code again
                window.location.reload();
              } else {
                console.error('Logout failed:', data.message);
                alert('Logout failed: ' + data.message);
              }
            })
            .catch(error => {
              console.error('Error during logout:', error);
              alert('Error during logout. Please try again.');
            });
        }
      }

      // Add WebSocket event listener
      wsManager.addListener((data) => {
        console.log('WebSocket event received:', data);

        if (data.type === 'qr') {
          displayQRCode(data.qr, data.isDataUrl);
          updateStatus('connecting');
        } else if (data.type === 'status' || data.type === 'connection') {
          if (data.connected) {
            updateStatus('connected');
          } else {
            updateStatus('disconnected');
          }
        }
      });

      // Initialize UI based on current state
      const currentState = wsManager.getStatus();
      if (currentState.isConnected) {
        updateStatus('connected');
      } else if (currentState.currentQR) {
        displayQRCode(currentState.currentQR, currentState.currentQR.startsWith('data:image'));
        updateStatus('connecting');
      } else {
        updateStatus('disconnected');
      }

      // Event listeners
      refreshQrButton.addEventListener('click', refreshQrCode);
      logoutButton.addEventListener('click', logout);

      // Check connection status on page load
      fetch('/whatsapp-api/status')
        .then(response => response.json())
        .then(data => {
          if (data.connected) {
            updateStatus('connected');
          } else {
            updateStatus('connecting');
          }
        })
        .catch(error => {
          console.error('Error checking connection status:', error);
          updateStatus('disconnected');
        });
    });
  </script>
</body>
</html>
