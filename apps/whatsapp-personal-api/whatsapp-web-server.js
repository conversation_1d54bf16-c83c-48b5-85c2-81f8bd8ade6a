// Import required modules
const { default: makeWASocket, useMultiFileAuthState, DisconnectReason } = require('@whiskeysockets/baileys');
const { Boom } = require('@hapi/boom');
const express = require('express');
const http = require('http');
const path = require('path');
const fs = require('fs');
const qrcode = require('qrcode');
const WebSocket = require('ws');
const axios = require('axios');
const dotenv = require('dotenv');
const getSecret = require('./src/utils/getSecret');
const createApiKeyAuth = require('./src/middleware/apiKeyAuth');

// Load environment variables
dotenv.config({ path: ".env" });

// Create Express app
const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server, path: '/ws' });

// Middleware
app.use(express.static(path.join(__dirname, 'public')));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Additional health check endpoint to match ECS config
app.get('/whatsapp-api/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'whatsapp-personal-api'
  });
});

// --- API Key Retrieval and Middleware Setup ---
let cachedApiKey = null;
let apiKeySource = 'secrets-manager';

async function loadAllowedApiKey() {
  if (cachedApiKey) return cachedApiKey;
  try {
    const secret = await getSecret(process.env.ALLOWED_API_KEY_SECRET_NAME);
    try {
      const parsed = JSON.parse(secret);
      cachedApiKey = parsed.api_key;
    } catch {
      cachedApiKey = secret;
    }
    if (!cachedApiKey) throw new Error('API key not found in secret');
    apiKeySource = 'secrets-manager';
    console.log('API key loaded from AWS Secrets Manager');
  } catch (err) {
    // Fallback to env var
    cachedApiKey = await getSecret(process.env.FALLBACK_API_KEY);
    apiKeySource = 'env';
    if (cachedApiKey) {
      console.warn('Falling back to FALLBACK_API_KEY from environment variable');
    } else {
      console.error('Failed to load API key from Secrets Manager and no FALLBACK_API_KEY set:', err.message);
      throw new Error('No API key available');
    }
  }
  return cachedApiKey;
}

// Global API key middleware, skip /health and static files
app.use(async (req, res, next) => {
  if (
    req.path === '/health' ||
    req.path === '/whatsapp-api/health' ||
    req.path === '/whatsapp-api/status' ||
    req.path === '/whatsapp-api/webhook/*' ||
    req.path === '/whatsapp-api/refresh-qr' ||
    req.path === '/whatsapp-api/logout' ||
    req.path === '/whatsapp-api/send-ui' ||
    req.path === '/whatsapp-web' ||
    req.path.startsWith('/public') ||
    req.path.startsWith('/static')
  ) {
    return next();
  }
  // Use the cached key loader for efficiency
  return createApiKeyAuth(() => Promise.resolve(cachedApiKey || loadAllowedApiKey()))(req, res, next);
});

// Webhook configuration from environment variables
const webhookConfig = {
  enabled: process.env.WEBHOOK_ENABLED === 'true',
  url: process.env.WEBHOOK_URL || '',
  secret: process.env.WEBHOOK_SECRET || ''
};

// Store active connections
const clients = new Set();
let currentQR = null;
let isConnected = false;
let whatsappClient = null;

// WebSocket connection
wss.on('connection', (ws) => {
  // Add client to set
  clients.add(ws);
  console.log('WebSocket client connected');

  // Send current QR code if available
  if (currentQR) {
    ws.send(JSON.stringify({ type: 'qr', qr: currentQR }));
  }

  // Send connection status
  ws.send(JSON.stringify({ type: 'status', connected: isConnected }));

  // Handle client disconnect
  ws.on('close', () => {
    clients.delete(ws);
    console.log('WebSocket client disconnected');
  });
});

// Broadcast to all clients
function broadcast(data) {
  clients.forEach(client => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(JSON.stringify(data));
    }
  });
}

// Serve the HTML pages
app.get('/whatsapp-web', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'whatsapp-web-ui.html'));
});

app.get('/whatsapp-api/send-ui', (req, res) => {
  if (!isConnected) {
    return res.redirect('/?error=not_connected');
  }
  res.sendFile(path.join(__dirname, 'public', 'send-message-with-webhook.html'));
});

// API endpoints
// Get connection status
app.get('/whatsapp-api/status', (req, res) => {
  res.json({
    success: true,
    connected: isConnected,
    hasClient: !!whatsappClient
  });
});

// Refresh QR code
app.post('/whatsapp-api/refresh-qr', async (req, res) => {
  try {
    if (isConnected) {
      return res.status(400).json({
        success: false,
        message: 'Already connected to WhatsApp'
      });
    }

    // Delete the session folder to force a new QR code
    const sessionsDir = path.join(__dirname, process.env.SESSIONS_DIR || 'whatsapp-session');
    if (fs.existsSync(sessionsDir)) {
      try {
        fs.rmSync(sessionsDir, { recursive: true, force: true });
        console.log('Session folder deleted successfully');
      } catch (error) {
        console.error('Error deleting session folder:', error);
      }
    }

    // Disconnect current client if exists
    if (whatsappClient) {
      try {
        whatsappClient.close();
        whatsappClient = null;
        console.log('Closed existing WhatsApp connection');
      } catch (error) {
        console.error('Error closing WhatsApp connection:', error);
      }
    }

    // Reset state
    currentQR = null;
    isConnected = false;

    // Broadcast disconnection to all clients
    broadcast({
      type: 'status',
      connected: false
    });

    // Reconnect to WhatsApp to get a new QR code
    setTimeout(() => {
      connectToWhatsApp().catch(err => console.log('Error reconnecting:', err));
    }, parseInt(process.env.QR_CODE_REFRESH_DELAY) || 1000);

    return res.status(200).json({
      success: true,
      message: 'QR code refresh initiated'
    });
  } catch (error) {
    console.error('Error refreshing QR code:', error);
    return res.status(500).json({
      success: false,
      message: 'Error refreshing QR code: ' + error.message
    });
  }
});

// Logout
app.post('/whatsapp-api/logout', async (req, res) => {
  try {
    if (!isConnected || !whatsappClient) {
      return res.status(400).json({
        success: false,
        message: 'Not connected to WhatsApp'
      });
    }

    // Delete the session folder
    const sessionsDir = path.join(__dirname, process.env.SESSIONS_DIR || 'whatsapp-session');
    if (fs.existsSync(sessionsDir)) {
      try {
        fs.rmSync(sessionsDir, { recursive: true, force: true });
        console.log('Session folder deleted successfully');
      } catch (error) {
        console.error('Error deleting session folder:', error);
      }
    }

    // Disconnect client
    try {
      await whatsappClient.logout();
      whatsappClient.close();
      whatsappClient = null;
      console.log('Logged out and closed WhatsApp connection');
    } catch (error) {
      console.error('Error during logout:', error);
    }

    // Reset state
    currentQR = null;
    isConnected = false;

    // Broadcast disconnection to all clients
    broadcast({
      type: 'status',
      connected: false
    });

    return res.status(200).json({
      success: true,
      message: 'Logged out successfully'
    });
  } catch (error) {
    console.error('Error during logout:', error);
    return res.status(500).json({
      success: false,
      message: 'Error during logout: ' + error.message
    });
  }
});

// Webhook configuration endpoint
app.post('/whatsapp-api/webhook/config', (req, res) => {
  try {
    const { enabled, url, secret } = req.body;

    // Validate URL if enabled
    if (enabled && !url) {
      return res.status(400).json({
        success: false,
        message: 'Webhook URL is required when enabled'
      });
    }

    // Update webhook configuration
    webhookConfig.enabled = !!enabled;
    webhookConfig.url = url || '';
    webhookConfig.secret = secret || '';

    console.log('Webhook configuration updated:', {
      enabled: webhookConfig.enabled,
      url: webhookConfig.url,
      hasSecret: !!webhookConfig.secret
    });

    return res.status(200).json({
      success: true,
      message: 'Webhook configuration updated successfully'
    });
  } catch (error) {
    console.error('Error updating webhook configuration:', error);
    return res.status(500).json({
      success: false,
      message: 'Error updating webhook configuration: ' + error.message
    });
  }
});

// Test webhook endpoint
app.post('/whatsapp-api/webhook/test', async (req, res) => {
  try {
    const { url, secret } = req.body;

    if (!url) {
      return res.status(400).json({
        success: false,
        message: 'Webhook URL is required'
      });
    }

    // Prepare test payload
    const testPayload = {
      event: 'test',
      timestamp: new Date().toISOString(),
      data: {
        message: 'This is a test notification from WhatsApp API'
      }
    };

    // Prepare headers
    const headers = {
      'Content-Type': 'application/json'
    };

    if (secret) {
      headers['X-Webhook-Secret'] = secret;
    }

    // Send test webhook
    const response = await axios.post(url, testPayload, { headers });

    console.log('Webhook test sent successfully:', {
      url,
      statusCode: response.status,
      hasSecret: !!secret
    });

    return res.status(200).json({
      success: true,
      message: 'Webhook test sent successfully',
      response: {
        status: response.status,
        statusText: response.statusText
      }
    });
  } catch (error) {
    console.error('Error testing webhook:', error);

    // Extract response error if available
    let errorMessage = error.message;
    if (error.response) {
      errorMessage = `HTTP ${error.response.status}: ${error.response.statusText}`;
    }

    return res.status(500).json({
      success: false,
      message: 'Error testing webhook: ' + errorMessage
    });
  }
});

/**
 * Send message endpoint
 *
 * Request body parameters:
 * - phone: (Required) Recipient's phone number (with or without country code)
 * - message: Text message to send (Required if no mediaUrl or documentUrl is provided)
 * - mediaUrl: URL to an image to send (Optional)
 * - documentUrl: URL to a document/PDF to send (Optional)
 * - documentName: Name of the document file (Optional, defaults to 'document.pdf')
 * - mimetype: MIME type of the document (Optional, defaults to 'application/pdf')
 *
 * At least one of message, mediaUrl, or documentUrl must be provided.
 */
app.post('/whatsapp-api/send', async (req, res) => {
  try {
    if (!isConnected || !whatsappClient) {
      return res.status(400).json({
        success: false,
        message: 'WhatsApp is not connected. Please scan the QR code first.'
      });
    }

    const { phone, message, mediaUrl, documentUrl, documentName, mimetype } = req.body;

    if (!phone) {
      return res.status(400).json({
        success: false,
        message: 'Phone number is required'
      });
    }

    if (!message && !mediaUrl && !documentUrl) {
      return res.status(400).json({
        success: false,
        message: 'Either message, mediaUrl, or documentUrl is required'
      });
    }

    // Format phone number
    const formattedPhone = formatPhoneNumber(phone);

    let result;

    // Send document if provided
    if (documentUrl) {
      console.log(`Sending document to ${formattedPhone}: ${documentUrl}`);
      result = await whatsappClient.sendMessage(formattedPhone, {
        document: { url: documentUrl },
        fileName: documentName || 'document.pdf',
        mimetype: mimetype || 'application/pdf',
        caption: message || ''
      });
    }
    // Send media if provided
    else if (mediaUrl) {
      console.log(`Sending media to ${formattedPhone}: ${mediaUrl}`);
      result = await whatsappClient.sendMessage(formattedPhone, {
        image: { url: mediaUrl },
        caption: message || ''
      });
    } else {
      // Send text message
      console.log(`Sending text message to ${formattedPhone}`);
      result = await whatsappClient.sendMessage(formattedPhone, { text: message });
    }

    return res.status(200).json({
      success: true,
      message: 'Message sent successfully',
      data: result
    });
  } catch (error) {
    console.error('Error sending message:', error);
    return res.status(500).json({
      success: false,
      message: 'Error sending message: ' + error.message
    });
  }
});

// Helper function to format phone number
function formatPhoneNumber(phone) {
  // Remove any non-digit characters
  const digits = phone.replace(/\D/g, '');

  // Ensure the number has the country code
  if (digits.startsWith('0')) {
    return `${process.env.DEFAULT_COUNTRY_CODE || '60'}${digits.substring(1)}@s.whatsapp.net`;
  } else if (!digits.includes('@')) {
    return `${digits}@s.whatsapp.net`;
  }

  return phone;
}

// Helper function to extract message content
function extractMessageContent(msg) {
  if (!msg.message) {
    return { type: 'unknown', text: '' };
  }

  // Text message
  if (msg.message.conversation) {
    return {
      type: 'text',
      text: msg.message.conversation
    };
  }

  // Extended text message (usually with formatting or replies)
  if (msg.message.extendedTextMessage) {
    return {
      type: 'text',
      text: msg.message.extendedTextMessage.text || '',
      contextInfo: msg.message.extendedTextMessage.contextInfo || null
    };
  }

  // Image message
  if (msg.message.imageMessage) {
    return {
      type: 'image',
      caption: msg.message.imageMessage.caption || '',
      mimetype: msg.message.imageMessage.mimetype || 'image/jpeg'
    };
  }

  // Video message
  if (msg.message.videoMessage) {
    return {
      type: 'video',
      caption: msg.message.videoMessage.caption || '',
      mimetype: msg.message.videoMessage.mimetype || 'video/mp4'
    };
  }

  // Audio message
  if (msg.message.audioMessage) {
    return {
      type: 'audio',
      mimetype: msg.message.audioMessage.mimetype || 'audio/ogg'
    };
  }

  // Document message
  if (msg.message.documentMessage) {
    return {
      type: 'document',
      fileName: msg.message.documentMessage.fileName || '',
      mimetype: msg.message.documentMessage.mimetype || 'application/octet-stream'
    };
  }

  // Sticker message
  if (msg.message.stickerMessage) {
    return {
      type: 'sticker',
      mimetype: msg.message.stickerMessage.mimetype || 'image/webp'
    };
  }

  // Unknown message type
  return {
    type: 'unknown',
    messageTypes: Object.keys(msg.message)
  };
}

// Connect to WhatsApp
async function connectToWhatsApp() {
  // Create sessions directory if it doesn't exist
  const sessionsDir = path.join(__dirname, process.env.SESSIONS_DIR || 'whatsapp-session');
  if (!fs.existsSync(sessionsDir)) {
    fs.mkdirSync(sessionsDir, { recursive: true });
  }

  // Load auth state
  const { state, saveCreds } = await useMultiFileAuthState(sessionsDir);

  // Create WhatsApp socket with settings for @adiwajshing/baileys
  const sock = makeWASocket({
    auth: state,
    printQRInTerminal: true, // Print QR code in terminal
    browser: [
      process.env.BROWSER_NAME || 'WhatsApp Web',
      process.env.BROWSER_VERSION || 'Chrome',
      process.env.BROWSER_VERSION_NUMBER || '103.0.5060.114'
    ]
  });

  // Handle connection updates
  sock.ev.on('connection.update', async (update) => {
    const { connection, lastDisconnect, qr } = update;

    if (qr) {
      console.log('QR code received');

      // Store the QR code
      currentQR = qr;

      // Log the QR code for debugging (truncated to avoid flooding the console)
      console.log('Raw QR code data received (length):', qr.length);

      // First, try to broadcast the raw QR code data
      broadcast({ type: 'qr', qr: qr });
      console.log('Broadcasted raw QR code to clients');

      // Also generate QR code image as data URL and broadcast it as backup
      try {
        // Convert QR code to data URL
        const qrDataURL = await qrcode.toDataURL(qr);

        // Log success
        console.log('Successfully generated QR code data URL');

        // Broadcast the data URL as well (as a backup approach)
        broadcast({ type: 'qr', qr: qrDataURL, isDataUrl: true });
        console.log('Broadcasted QR code data URL to clients');
      } catch (error) {
        console.error('Error generating QR code data URL:', error);
      }
    }

    if (connection === 'close') {
      console.log('Connection closed. Reason:', lastDisconnect?.error?.output?.payload?.message || 'Unknown');

      // Check if this was a logout or device disconnection
      const statusCode = lastDisconnect?.error?.output?.statusCode;
      const isLoggedOut = statusCode === DisconnectReason.loggedOut;
      const isConnectionReplaced = statusCode === DisconnectReason.connectionReplaced;
      const isConnectionLost = statusCode === DisconnectReason.connectionLost;

      // Update connection status
      isConnected = false;

      // Broadcast disconnection to all clients
      broadcast({ type: 'status', connected: false });

      if (isLoggedOut) {
        console.log('User logged out from WhatsApp');
        // Clear session data
        whatsappClient = null;
        currentQR = null;

        // Delete the session folder
        const sessionsDir = path.join(__dirname, 'whatsapp-session');
        if (fs.existsSync(sessionsDir)) {
          try {
            fs.rmSync(sessionsDir, { recursive: true, force: true });
            console.log('Session folder deleted after logout');
          } catch (error) {
            console.error('Error deleting session folder:', error);
          }
        }

        // Reconnect to get a new QR code
        setTimeout(() => {
          connectToWhatsApp().catch(err => console.log('Error reconnecting after logout:', err));
        }, 1000);
      } else if (isConnectionReplaced) {
        console.log('Connection replaced (logged in from another device)');
        // Clear session data
        whatsappClient = null;
        currentQR = null;

        // Delete the session folder
        const sessionsDir = path.join(__dirname, 'whatsapp-session');
        if (fs.existsSync(sessionsDir)) {
          try {
            fs.rmSync(sessionsDir, { recursive: true, force: true });
            console.log('Session folder deleted after connection replaced');
          } catch (error) {
            console.error('Error deleting session folder:', error);
          }
        }

        // Reconnect to get a new QR code
        setTimeout(() => {
          connectToWhatsApp().catch(err => console.log('Error reconnecting after connection replaced:', err));
        }, 1000);
      } else if (isConnectionLost) {
        console.log('Connection lost. Attempting to reconnect...');
        // Try to reconnect
        setTimeout(() => {
          connectToWhatsApp().catch(err => console.log('Error reconnecting after connection lost:', err));
        }, 1000);
      } else {
        // For other disconnection reasons, try to reconnect
        const shouldReconnect = (lastDisconnect?.error instanceof Boom) &&
          statusCode !== DisconnectReason.loggedOut;

        if (shouldReconnect) {
          console.log('Attempting to reconnect...');
          setTimeout(() => {
            connectToWhatsApp().catch(err => console.log('Error reconnecting:', err));
          }, 1000);
        } else {
          console.log('Not reconnecting due to:', statusCode);
          // Clear session data
          whatsappClient = null;
          currentQR = null;

          // Reconnect to get a new QR code
          setTimeout(() => {
            connectToWhatsApp().catch(err => console.log('Error reconnecting after permanent disconnect:', err));
          }, 1000);
        }
      }
    } else if (connection === 'open') {
      console.log('Connected to WhatsApp!');
      isConnected = true;
      whatsappClient = sock; // Store the client for sending messages
      currentQR = null; // Clear QR code
      broadcast({ type: 'status', connected: true });
    }
  });

  // Save credentials whenever they are updated
  sock.ev.on('creds.update', saveCreds);

  // Handle messages
  sock.ev.on('messages.upsert', async (m) => {
    if (m.type === 'notify') {
      for (const msg of m.messages) {
        if (!msg.key.fromMe) {
          console.log('New message received');
          console.log('From:', msg.key.remoteJid);
          console.log('Sender name:', msg.pushName || 'Unknown');

          // Extract message content
          const messageContent = extractMessageContent(msg);
          console.log('Message content type:', messageContent.type);

          // Send webhook notification if enabled
          if (webhookConfig.enabled && webhookConfig.url) {
            try {
              // Prepare webhook payload
              const webhookPayload = {
                event: 'message',
                timestamp: new Date().toISOString(),
                data: {
                  id: msg.key.id,
                  from: msg.key.remoteJid,
                  pushName: msg.pushName || 'Unknown',
                  timestamp: msg.messageTimestamp,
                  message: messageContent
                }
              };

              // Prepare headers
              const headers = {
                'Content-Type': 'application/json'
              };

              if (webhookConfig.secret) {
                headers['X-Webhook-Secret'] = webhookConfig.secret;
              }

              // Send webhook notification
              const response = await axios.post(webhookConfig.url, webhookPayload, { headers });

              console.log('Webhook notification sent successfully:', {
                url: webhookConfig.url,
                statusCode: response.status
              });
            } catch (error) {
              console.error('Error sending webhook notification:', error.message);
            }
          }

          // Broadcast to WebSocket clients
          broadcast({
            type: 'message',
            data: {
              from: msg.key.remoteJid,
              pushName: msg.pushName || 'Unknown',
              message: messageContent
            }
          });
        }
      }
    }
  });
}

// On startup, load the API key (fail fast if not available)
(async () => {
  try {
    await loadAllowedApiKey();
    console.log(`API key ready (source: ${apiKeySource})`);
    // Start the server
    const PORT = process.env.PORT || 3030;
    const HOST = process.env.HOST || '0.0.0.0';
    server.listen(PORT, HOST, () => {
      console.log(`Server running on port ${PORT}`);
      console.log(`Open http://localhost:${PORT} in your browser`);
      console.log('All API endpoints require x-api-key header');
      connectToWhatsApp().catch(err => console.log('Error connecting to WhatsApp:', err));
    });
  } catch (err) {
    console.error('Fatal error: Unable to start server due to missing API key:', err.message);
    process.exit(1);
  }
})();

