# WhatsApp Personal API - Complete Implementation Details

## Architecture Overview

This WhatsApp API is built using <PERSON>'s WhatsApp library to provide a multi-session WhatsApp Web interface with webhook integration. The system allows multiple concurrent WhatsApp sessions with independent authentication and message handling.

### Technology Stack

- **Backend**: Node.js with Express.js and TypeScript
- **WhatsApp Library**: @whiskeysockets/baileys v6.2.1
- **Frontend**: Vanilla JavaScript with Bootstrap 5
- **Real-time Communication**: WebSocket connection
- **Authentication**: Multi-file auth state for session persistence
- **Deployment**: Docker containerization with ECR deployment

## Backend Implementation

### Core Service Architecture

The backend follows a modular architecture with clear separation of concerns:

```
src/
├── config/          # Configuration management
├── controllers/     # Request handlers
├── middleware/      # Authentication middleware
├── routes/          # API route definitions
├── services/        # Business logic (WhatsApp service)
├── types/           # TypeScript type definitions
└── utils/           # Utility functions (webhooks)
```

### 1. Session Management (`src/services/whatsapp.service.ts`)

**Core Features:**
- Multi-session support with unique session IDs
- Persistent session storage using <PERSON>'s multiFileAuthState
- Automatic reconnection handling
- QR code generation and management

**Key Methods:**
- `createSession(sessionId, sessionName)` - Creates new WhatsApp session
- `getSession(sessionId)` - Retrieves existing session
- `getAllSessions()` - Returns all active sessions
- `deleteSession(sessionId)` - Removes session and cleans up

**Session Lifecycle:**
1. Session creation with unique ID
2. Authentication state initialization in `sessions/{sessionId}/` directory
3. Bailey's WASocket creation with browser signature
4. QR code generation for WhatsApp Web linking
5. Connection event handling (open, close, reconnect)
6. Webhook notifications for session events

### 2. API Endpoints

#### Session Management Routes (`/api/sessions`)

**POST /api/sessions** - Create new session
```json
Request: {
  "id": "session_unique_id",
  "name": "Optional session name"
}

Response: {
  "success": true,
  "data": {
    "id": "session_unique_id",
    "name": "session name",
    "ready": false,
    "qr": "2@encrypted_qr_data..."
  }
}
```

**GET /api/sessions** - List all sessions
```json
Response: {
  "success": true,
  "data": [
    {
      "id": "session_id",
      "name": "session name",
      "ready": true,
      "lastSeen": "2024-01-01T12:00:00.000Z"
    }
  ]
}
```

**GET /api/sessions/:id** - Get specific session
**DELETE /api/sessions/:id** - Delete session

#### Message Management Routes (`/api/messages`)

**POST /api/messages/whatsapp-api/:id/send** - Send message
```json
Request: {
  "to": "60123456789",
  "text": "Hello World",
  "media": {
    "url": "https://example.com/image.jpg",
    "caption": "Optional caption",
    "mimetype": "image/jpeg"
  }
}

Response: {
  "success": true,
  "data": {
    // Bailey's message response object
  }
}
```

### 3. Webhook Integration (`src/utils/webhook.ts`)

**Webhook Events:**
- `qr` - QR code generated for session
- `connected` - Session successfully connected
- `disconnected` - Session disconnected
- `message` - New message received

**Webhook Payload Structure:**
```json
{
  "sessionId": "session_id",
  "event": "message",
  "data": {
    // Event-specific data
  }
}
```

### 4. Configuration Management (`src/config/index.ts`)

**Environment Variables:**
- `PORT` - Server port (default: 3000)
- `NODE_ENV` - Environment mode
- `WEBHOOK_URL` - External webhook endpoint
- Sessions stored in `./sessions/` directory

## Frontend Implementation

### 1. Session Management Interface (`src/public/index.html`)

**Features:**
- Session creation form with ID and name inputs
- Real-time session status display
- QR code display for authentication
- Session deletion controls

**UI Components:**
- Bootstrap 5 responsive design
- Session list with status badges (Connected/Pending)
- QR code container with automatic refresh
- Form validation and error handling

### 2. JavaScript Session Handler (`src/public/js/main.js`)

**Core Functions:**
- `createSession()` - Creates new WhatsApp session via API
- `loadSessions()` - Fetches and displays all active sessions
- `showQRCode()` - Renders QR code for scanning
- `pollForQRCode()` - Polls for QR code availability
- `handleSessionAction()` - Manages session deletion

**QR Code Handling:**
1. Detects WhatsApp QR format (starts with "2@")
2. Uses QRCode.js library for local generation
3. Fallback to external QR service (qrserver.com)
4. Automatic session status polling
5. QR code removal when session connects

**Polling Mechanisms:**
- Session list refresh every 5 seconds
- QR code availability check every 2 seconds
- Session connection status check every 3 seconds

### 3. WebSocket Real-time Communication (`public/js/websocket-connection.js`)

**WebSocket Manager Features:**
- Singleton pattern for consistent connection
- Automatic reconnection with exponential backoff
- State persistence across page navigation
- Event listener management

**Connection Lifecycle:**
1. WebSocket connection to `/ws` endpoint
2. State management (connected/connecting/disconnected)
3. Automatic reconnection on connection loss
4. Session storage for state persistence

**Event Handling:**
- `connection` - WebSocket connection status
- `qr` - QR code updates
- `status` - WhatsApp session status
- `message` - Incoming WhatsApp messages

## Message Flow Architecture

### 1. Outgoing Messages

```
Frontend Form → API Controller → WhatsApp Service → Bailey's Client → WhatsApp Servers
```

**Process:**
1. User submits message form
2. API validates session and message data
3. WhatsApp service formats phone number
4. Bailey's client sends to WhatsApp
5. Response returned to frontend

### 2. Incoming Messages

```
WhatsApp Servers → Bailey's Client → Event Handler → Webhook → External System
```

**Process:**
1. Bailey's receives message event
2. Service filters for non-self messages
3. Webhook payload constructed
4. HTTP POST to configured webhook URL
5. External system processes message

## Phone Number Formatting (`src/services/whatsapp.service.ts:251`)

**Formatting Logic:**
- Removes non-digit characters
- Handles Malaysian numbers (0 prefix → 6 prefix)
- Appends `@s.whatsapp.net` suffix
- Returns standardized format for Bailey's

## Session Persistence

**Storage Mechanism:**
- Each session stored in `sessions/{sessionId}/` directory
- Bailey's multiFileAuthState manages:
  - `creds.json` - Authentication credentials
  - `keys/` - Signal protocol keys
  - Session tokens and state

**Reconnection Strategy:**
1. Automatic reconnection on disconnect (except logout)
2. Session directory cleanup on logout
3. Webhook notifications for connection events
4. State restoration from persisted files

## Security Considerations

**Authentication:**
- API key authentication middleware (`src/middleware/apiKeyAuth.js`)
- Session isolation with unique directories
- Webhook secret key support

**Data Protection:**
- No sensitive data logging
- Secure credential storage
- Session cleanup on deletion

## Deployment Configuration

**Docker Setup:**
- Multi-stage build with Node.js
- Production optimizations
- ECR push scripts for AWS deployment

**Environment Setup:**
- Development: `npm run dev` (nodemon)
- Production: `npm start` (node)
- Build: `npm run build` (TypeScript compilation)

## Error Handling

**Backend Error Responses:**
- Consistent JSON error format
- HTTP status code mapping
- Detailed error logging

**Frontend Error Handling:**
- User-friendly error messages
- Graceful degradation
- Connection retry mechanisms

## Monitoring and Webhooks

**Webhook Events Sequence:**
1. Session creation → `qr` event with QR data
2. QR scan completion → `connected` event
3. Message reception → `message` event with full payload
4. Disconnection → `disconnected` event with reason

**Health Monitoring:**
- Session status tracking
- Connection state monitoring
- Automatic cleanup on failures

This implementation provides a robust, scalable WhatsApp API solution with multi-session support, real-time communication, and comprehensive webhook integration for external system integration.