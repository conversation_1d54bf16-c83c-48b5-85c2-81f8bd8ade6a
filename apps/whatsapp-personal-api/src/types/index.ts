import { WASocket } from '@whiskeysockets/baileys';

export interface WhatsAppSession {
  id: string;
  name?: string;
  client: WASocket;
  qr?: string;
  ready: boolean;
  lastSeen?: Date;
  phoneNumber?: string;
  status: 'pending' | 'connected' | 'disconnected' | 'failed';
  connectionAttempts?: number;
  webhookUrl?: string;
  metadata?: Record<string, any>;
}

export interface WebhookPayload {
  sessionId: string;
  event: string;
  data: any;
  timestamp?: string;
}

export interface SendMessageRequest {
  to: string;
  text?: string;
  media?: {
    url: string;
    caption?: string;
    mimetype?: string;
  };
  document?: {
    url: string;
    filename?: string;
    mimetype?: string;
  };
}

export interface SessionInfo {
  id: string;
  name?: string;
  ready: boolean;
  lastSeen?: Date;
  status: 'pending' | 'connected' | 'disconnected' | 'failed';
  phoneNumber?: string;
  webhookUrl?: string;
  createdAt?: number;
  connectionAttempts?: number;
}

export interface SessionCreateRequest {
  id: string;
  name?: string;
  webhookUrl?: string;
}

export interface SessionUpdateRequest {
  name?: string;
  webhookUrl?: string;
}

export interface SessionHealthStatus {
  sessionId: string;
  healthy: boolean;
  lastChecked: number;
  uptime?: number;
  errors?: string[];
}

export interface SessionStats {
  sessionId: string;
  messagesSent: number;
  messagesReceived: number;
  webhooksDelivered: number;
  webhooksFailed: number;
  uptime: number;
  lastActivity: number;
}

export interface BulkMessageRequest {
  recipients: string[];
  message: {
    text?: string;
    media?: {
      url: string;
      caption?: string;
      mimetype?: string;
    };
  };
  delay?: number; // milliseconds between messages
}

export interface BulkMessageResponse {
  success: boolean;
  results: Array<{
    to: string;
    success: boolean;
    messageId?: string;
    error?: string;
  }>;
  summary: {
    total: number;
    sent: number;
    failed: number;
  };
}

export interface ErrorResponse {
  success: false;
  message: string;
  code?: string;
  details?: any;
}

export interface SuccessResponse<T = any> {
  success: true;
  data: T;
  message?: string;
}

export type ApiResponse<T = any> = SuccessResponse<T> | ErrorResponse;
