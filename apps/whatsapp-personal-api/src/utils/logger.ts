import winston from 'winston';
import config from '../config';

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

winston.addColors(colors);

// Define log format
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`,
  ),
);

// Define which transports to use
const transports = [
  new winston.transports.Console(),
  new winston.transports.File({
    filename: 'logs/error.log',
    level: 'error',
  }),
  new winston.transports.File({
    filename: 'logs/combined.log',
  }),
];

// Create the logger instance
const logger = winston.createLogger({
  level: config.environment === 'development' ? 'debug' : 'info',
  levels,
  format,
  transports,
});

// Create session-specific logger
export const createSessionLogger = (sessionId: string) => {
  return {
    info: (message: string, meta?: any) => 
      logger.info(`[Session: ${sessionId}] ${message}`, meta),
    error: (message: string, meta?: any) => 
      logger.error(`[Session: ${sessionId}] ${message}`, meta),
    warn: (message: string, meta?: any) => 
      logger.warn(`[Session: ${sessionId}] ${message}`, meta),
    debug: (message: string, meta?: any) => 
      logger.debug(`[Session: ${sessionId}] ${message}`, meta),
  };
};

export default logger;