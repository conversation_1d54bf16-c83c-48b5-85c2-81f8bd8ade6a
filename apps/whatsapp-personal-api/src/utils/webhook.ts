import axios, { AxiosError } from 'axios';
import crypto from 'crypto';
import config from '../config';
import { WebhookPayload } from '../types';
import dynamoDBService from '../services/dynamodb.service';
import logger, { createSessionLogger } from './logger';

export interface WebhookConfig {
  url: string;
  secret?: string;
  events?: string[];
  retryCount?: number;
  timeout?: number;
}

export interface WebhookDeliveryResult {
  success: boolean;
  statusCode?: number;
  error?: string;
  retryCount: number;
  deliveryTime: number;
}

class WebhookService {
  async sendWebhook(payload: WebhookPayload): Promise<WebhookDeliveryResult> {
    const startTime = Date.now();
    let retryCount = 0;
    
    try {
      // Get session-specific webhook config
      const session = await dynamoDBService.getSession(payload.sessionId);
      const webhookUrl = session?.webhookUrl || config.webhook.url;
      
      if (!webhookUrl || webhookUrl.trim() === '') {
        logger.debug(`No webhook URL configured for session ${payload.sessionId}`);
        return {
          success: false,
          error: 'No webhook URL configured',
          retryCount: 0,
          deliveryTime: Date.now() - startTime,
        };
      }

      const webhookConfig: WebhookConfig = {
        url: webhookUrl,
        secret: config.webhook.secret,
        timeout: config.webhook.timeout,
        retryCount: config.webhook.maxRetries,
      };

      return await this.deliverWebhook(payload, webhookConfig, startTime);
    } catch (error) {
      logger.error('Error in webhook service:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        retryCount,
        deliveryTime: Date.now() - startTime,
      };
    }
  }

  async sendSessionWebhook(
    sessionId: string,
    event: string,
    data: any,
    customConfig?: Partial<WebhookConfig>
  ): Promise<WebhookDeliveryResult> {
    const payload: WebhookPayload = {
      sessionId,
      event,
      data,
      timestamp: new Date().toISOString(),
    };

    if (customConfig) {
      const session = await dynamoDBService.getSession(sessionId);
      if (session) {
        const config: WebhookConfig = {
          url: customConfig.url || session.webhookUrl || '',
          secret: customConfig.secret,
          events: customConfig.events,
          retryCount: customConfig.retryCount,
          timeout: customConfig.timeout,
        };

        // Check if this event should be sent
        if (config.events && !config.events.includes(event)) {
          return {
            success: true,
            retryCount: 0,
            deliveryTime: 0,
          };
        }

        return await this.deliverWebhook(payload, config);
      }
    }

    return await this.sendWebhook(payload);
  }

  private async deliverWebhook(
    payload: WebhookPayload,
    config: WebhookConfig,
    startTime: number = Date.now()
  ): Promise<WebhookDeliveryResult> {
    const sessionLogger = createSessionLogger(payload.sessionId);
    let lastError: string = '';

    for (let attempt = 0; attempt <= (config.retryCount || 0); attempt++) {
      try {
        const headers: Record<string, string> = {
          'Content-Type': 'application/json',
          'User-Agent': 'WhatsApp-API-Webhook/1.0',
          'X-Webhook-Event': payload.event,
          'X-Webhook-Session': payload.sessionId,
        };

        // Add signature if secret is configured
        if (config.secret) {
          const signature = this.generateSignature(JSON.stringify(payload), config.secret);
          headers['X-Webhook-Signature'] = signature;
        }

        const response = await axios.post(config.url, payload, {
          headers,
          timeout: config.timeout || 30000,
          validateStatus: (status) => status < 500, // Don't retry on 4xx errors
        });

        // Success
        if (response.status >= 200 && response.status < 300) {
          sessionLogger.debug(`Webhook delivered successfully: ${payload.event}`);
          return {
            success: true,
            statusCode: response.status,
            retryCount: attempt,
            deliveryTime: Date.now() - startTime,
          };
        }

        // Client error (4xx) - don't retry
        if (response.status >= 400 && response.status < 500) {
          lastError = `HTTP ${response.status}: ${response.statusText}`;
          sessionLogger.warn(`Webhook client error (not retrying): ${lastError}`);
          break;
        }

        // Server error (5xx) - retry
        lastError = `HTTP ${response.status}: ${response.statusText}`;
        sessionLogger.warn(`Webhook server error (attempt ${attempt + 1}): ${lastError}`);

      } catch (error) {
        const axiosError = error as AxiosError;
        lastError = axiosError.message;
        
        if (axiosError.code === 'ECONNABORTED') {
          lastError = 'Webhook timeout';
        } else if (axiosError.code === 'ECONNREFUSED') {
          lastError = 'Connection refused';
        }

        sessionLogger.warn(`Webhook delivery failed (attempt ${attempt + 1}): ${lastError}`);
      }

      // Wait before retry (exponential backoff)
      if (attempt < (config.retryCount || 0)) {
        const delay = Math.min(1000 * Math.pow(2, attempt), 30000);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    return {
      success: false,
      error: lastError,
      retryCount: config.retryCount || 0,
      deliveryTime: Date.now() - startTime,
    };
  }

  async updateSessionWebhook(sessionId: string, webhookUrl: string): Promise<void> {
    try {
      await dynamoDBService.updateSession(sessionId, {
        webhookUrl: webhookUrl.trim() || undefined,
      });
      logger.info(`Updated webhook URL for session ${sessionId}`);
    } catch (error) {
      logger.error(`Failed to update webhook for session ${sessionId}:`, error);
      throw error;
    }
  }

  async testWebhook(sessionId: string, webhookUrl?: string): Promise<WebhookDeliveryResult> {
    const testPayload: WebhookPayload = {
      sessionId,
      event: 'webhook_test',
      data: {
        message: 'This is a test webhook',
        timestamp: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
    };

    if (webhookUrl) {
      const config: WebhookConfig = {
        url: webhookUrl,
        secret: config.webhook.secret,
        timeout: 10000, // Shorter timeout for tests
        retryCount: 0, // No retries for tests
      };
      return await this.deliverWebhook(testPayload, config);
    }

    return await this.sendWebhook(testPayload);
  }

  private generateSignature(payload: string, secret: string): string {
    return `sha256=${crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('hex')}`;
  }

  async logWebhookEvent(
    sessionId: string,
    event: string,
    success: boolean,
    details?: any
  ): Promise<void> {
    const sessionLogger = createSessionLogger(sessionId);
    
    if (success) {
      sessionLogger.info(`Webhook event delivered: ${event}`, details);
    } else {
      sessionLogger.error(`Webhook event failed: ${event}`, details);
    }

    // Update session metadata with webhook stats
    try {
      const session = await dynamoDBService.getSession(sessionId);
      if (session) {
        const metadata = session.metadata || {};
        const webhookStats = metadata.webhookStats || { delivered: 0, failed: 0 };
        
        if (success) {
          webhookStats.delivered++;
        } else {
          webhookStats.failed++;
        }
        
        webhookStats.lastAttempt = Date.now();
        metadata.webhookStats = webhookStats;

        await dynamoDBService.updateSession(sessionId, { metadata });
      }
    } catch (error) {
      logger.error(`Failed to update webhook stats for session ${sessionId}:`, error);
    }
  }
}

// Create singleton instance
const webhookService = new WebhookService();

// Export both the service and legacy function for backward compatibility
export { webhookService };
export const sendWebhook = webhookService.sendWebhook.bind(webhookService);
export default sendWebhook;
