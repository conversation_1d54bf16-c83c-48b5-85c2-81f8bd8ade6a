import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config();

export interface AppConfig {
  port: number;
  environment: 'development' | 'production';
  aws: {
    region: string;
    dynamoTable: string;
    secretsArn?: string;
    credentials?: {
      accessKeyId?: string;
      secretAccessKey?: string;
    };
  };
  webhook: {
    url?: string;
    secret?: string;
    timeout: number;
    maxRetries: number;
  };
  session: {
    maxPerUser: number;
    timeoutHours: number;
    cleanupInterval: number;
  };
  sessionsDir: string;
  logging: {
    level: string;
    directory: string;
  };
}

export const config: AppConfig = {
  port: parseInt(process.env.PORT || '3000', 10),
  environment: (process.env.NODE_ENV as 'development' | 'production') || 'development',
  
  aws: {
    region: process.env.AWS_REGION || 'ap-southeast-1',
    dynamoTable: process.env.DYNAMODB_TABLE_NAME || 'whatsapp-sessions',
    secretsArn: process.env.AWS_SECRETS_ARN,
    credentials: process.env.NODE_ENV === 'development' ? {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    } : undefined,
  },
  
  webhook: {
    url: process.env.WEBHOOK_URL,
    secret: process.env.WEBHOOK_SECRET_KEY,
    timeout: parseInt(process.env.WEBHOOK_TIMEOUT_MS || '30000', 10),
    maxRetries: parseInt(process.env.WEBHOOK_MAX_RETRIES || '3', 10),
  },
  
  session: {
    maxPerUser: parseInt(process.env.MAX_SESSIONS_PER_USER || '5', 10),
    timeoutHours: parseInt(process.env.SESSION_TIMEOUT_HOURS || '24', 10),
    cleanupInterval: parseInt(process.env.SESSION_CLEANUP_INTERVAL || '3600', 10), // seconds
  },
  
  sessionsDir: path.join(process.cwd(), 'sessions'),
  
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    directory: process.env.LOG_DIRECTORY || 'logs',
  },
};

// Validation
const requiredEnvVars = [
  'AWS_REGION',
  'DYNAMODB_TABLE_NAME',
];

if (config.environment === 'production') {
  requiredEnvVars.push('AWS_SECRETS_ARN');
}

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
if (missingVars.length > 0) {
  throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
}

export default config;
