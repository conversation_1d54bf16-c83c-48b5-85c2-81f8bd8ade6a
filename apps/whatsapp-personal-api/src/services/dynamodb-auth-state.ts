import { AuthenticationState, AuthenticationCreds, SignalDataTypeMap } from '@whiskeysockets/baileys';
import { proto } from '@whiskeysockets/baileys';
import dynamoDBService from './dynamodb.service';
import { createSessionLogger } from '../utils/logger';

export interface DynamoDBAuthState extends AuthenticationState {
  saveCreds: () => Promise<void>;
}

/**
 * Custom authentication state provider that stores <PERSON>'s auth data in DynamoDB
 * This replaces the file-based authentication state with persistent cloud storage
 */
export class DynamoDBAuthStateProvider {
  private sessionId: string;
  private logger: any;
  private creds: AuthenticationCreds | null = null;
  private keys: any = {};

  constructor(sessionId: string) {
    this.sessionId = sessionId;
    this.logger = createSessionLogger(sessionId);
  }

  /**
   * Initialize the auth state from DynamoDB
   */
  async initialize(): Promise<DynamoDBAuthState> {
    try {
      // Load existing auth state from DynamoDB
      const session = await dynamoDBService.getSession(this.sessionId);
      
      if (session?.authState) {
        try {
          const authData = JSON.parse(session.authState);
          this.creds = authData.creds || null;
          this.keys = authData.keys || {};
          this.logger.info('Loaded existing auth state from DynamoDB');
        } catch (error) {
          this.logger.error('Failed to parse auth state from DynamoDB:', error);
          // Initialize with empty state if parsing fails
          this.initializeEmptyState();
        }
      } else {
        this.logger.info('No existing auth state found, initializing empty state');
        this.initializeEmptyState();
      }

      return {
        creds: this.creds,
        keys: {
          get: (type: keyof SignalDataTypeMap, ids: string[]): any => {
            const key = `${type}-${ids.join('-')}`;
            return this.keys[key];
          },
          set: (data: any): void => {
            for (const category in data) {
              for (const id in data[category]) {
                const key = `${category}-${id}`;
                this.keys[key] = data[category][id];
              }
            }
          },
        },
        saveCreds: this.saveCreds.bind(this),
      };
    } catch (error) {
      this.logger.error('Failed to initialize auth state:', error);
      throw error;
    }
  }

  /**
   * Initialize empty authentication state
   */
  private initializeEmptyState(): void {
    this.creds = null;
    this.keys = {};
  }

  /**
   * Save credentials to DynamoDB
   */
  private async saveCreds(): Promise<void> {
    try {
      const authData = {
        creds: this.creds,
        keys: this.keys,
        timestamp: Date.now(),
      };

      const authStateString = JSON.stringify(authData);
      
      // Update the session record with the new auth state
      await dynamoDBService.updateSession(this.sessionId, {
        authState: authStateString,
      });

      this.logger.debug('Auth state saved to DynamoDB');
    } catch (error) {
      this.logger.error('Failed to save auth state to DynamoDB:', error);
      throw error;
    }
  }

  /**
   * Update credentials (called by Bailey's when creds change)
   */
  updateCreds(creds: AuthenticationCreds): void {
    this.creds = creds;
  }

  /**
   * Clear all authentication data
   */
  async clearAuthState(): Promise<void> {
    try {
      this.creds = null;
      this.keys = {};
      
      await dynamoDBService.updateSession(this.sessionId, {
        authState: JSON.stringify({
          creds: null,
          keys: {},
          timestamp: Date.now(),
        }),
      });

      this.logger.info('Auth state cleared from DynamoDB');
    } catch (error) {
      this.logger.error('Failed to clear auth state:', error);
      throw error;
    }
  }

  /**
   * Get the current authentication status
   */
  isAuthenticated(): boolean {
    return !!(this.creds && this.creds.noiseKey && this.creds.signedIdentityKey);
  }

  /**
   * Export auth state for backup/migration
   */
  async exportAuthState(): Promise<any> {
    const session = await dynamoDBService.getSession(this.sessionId);
    if (session?.authState) {
      return JSON.parse(session.authState);
    }
    return null;
  }

  /**
   * Import auth state from backup/migration
   */
  async importAuthState(authData: any): Promise<void> {
    try {
      this.creds = authData.creds || null;
      this.keys = authData.keys || {};
      
      await this.saveCreds();
      this.logger.info('Auth state imported successfully');
    } catch (error) {
      this.logger.error('Failed to import auth state:', error);
      throw error;
    }
  }
}

/**
 * Factory function to create DynamoDB auth state (similar to Bailey's useMultiFileAuthState)
 */
export async function useDynamoDBAuthState(sessionId: string): Promise<{
  state: DynamoDBAuthState;
  saveCreds: () => Promise<void>;
}> {
  const provider = new DynamoDBAuthStateProvider(sessionId);
  const state = await provider.initialize();
  
  // Wrapper for credentials update
  const originalSaveCreds = state.saveCreds;
  const saveCreds = async () => {
    // Update the provider's creds before saving
    provider.updateCreds(state.creds);
    await originalSaveCreds();
  };

  return {
    state,
    saveCreds,
  };
}

export default DynamoDBAuthStateProvider;