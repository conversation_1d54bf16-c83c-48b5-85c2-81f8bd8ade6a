import cron from 'node-cron';
import dynamoDBService from './dynamodb.service';
import whatsappService from './whatsapp.service';
import { webhookService } from '../utils/webhook';
import config from '../config';
import logger from '../utils/logger';

export interface SessionHealthReport {
  totalSessions: number;
  healthySessions: number;
  unhealthySessions: number;
  disconnectedSessions: number;
  failedSessions: number;
  sessionsNeedingAttention: string[];
  timestamp: number;
}

export interface CleanupReport {
  expiredSessionsRemoved: number;
  failedSessionsArchived: number;
  orphanedAuthStatesCleared: number;
  timestamp: number;
}

class MonitoringService {
  private cleanupJob: any = null;
  private healthCheckJob: any = null;
  private initialized: boolean = false;

  constructor() {
    this.initializeMonitoring();
  }

  private initializeMonitoring(): void {
    try {
      // Schedule cleanup job every hour
      this.cleanupJob = cron.schedule('0 * * * *', async () => {
        await this.performCleanup();
      }, {
        scheduled: false,
        timezone: 'UTC',
      });

      // Schedule health check every 5 minutes
      this.healthCheckJob = cron.schedule('*/5 * * * *', async () => {
        await this.performHealthCheck();
      }, {
        scheduled: false,
        timezone: 'UTC',
      });

      this.initialized = true;
      logger.info('Monitoring service initialized');
    } catch (error) {
      logger.error('Failed to initialize monitoring service:', error);
    }
  }

  public startMonitoring(): void {
    if (!this.initialized) {
      throw new Error('Monitoring service not initialized');
    }

    this.cleanupJob?.start();
    this.healthCheckJob?.start();
    
    logger.info('Monitoring jobs started');
  }

  public stopMonitoring(): void {
    this.cleanupJob?.stop();
    this.healthCheckJob?.stop();
    
    logger.info('Monitoring jobs stopped');
  }

  public async performCleanup(): Promise<CleanupReport> {
    logger.info('Starting scheduled cleanup');
    
    try {
      // Clean up expired sessions from DynamoDB
      const expiredSessionsRemoved = await dynamoDBService.cleanupExpiredSessions();
      
      // Archive failed sessions (older than 24 hours)
      const failedSessionsArchived = await this.archiveFailedSessions();
      
      // Clear orphaned auth states
      const orphanedAuthStatesCleared = await this.clearOrphanedAuthStates();

      const report: CleanupReport = {
        expiredSessionsRemoved,
        failedSessionsArchived,
        orphanedAuthStatesCleared,
        timestamp: Date.now(),
      };

      logger.info('Cleanup completed', report);
      
      // Send webhook notification for cleanup report
      await this.sendCleanupReport(report);
      
      return report;
    } catch (error) {
      logger.error('Error during cleanup:', error);
      throw error;
    }
  }

  private async archiveFailedSessions(): Promise<number> {
    try {
      const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
      const { sessions } = await dynamoDBService.getSessionsByStatus('failed');
      
      let archivedCount = 0;
      
      for (const session of sessions) {
        if (session.updatedAt < oneDayAgo) {
          // Archive the session (you could move to a different table or S3)
          await dynamoDBService.deleteSession(session.sessionId);
          archivedCount++;
          
          logger.debug(`Archived failed session: ${session.sessionId}`);
        }
      }
      
      return archivedCount;
    } catch (error) {
      logger.error('Error archiving failed sessions:', error);
      return 0;
    }
  }

  private async clearOrphanedAuthStates(): Promise<number> {
    try {
      // Get all sessions from DynamoDB
      const { sessions } = await dynamoDBService.getAllSessions();
      const activeSessions = new Set(sessions.map(s => s.sessionId));
      
      // Note: In a real implementation, you might want to scan for auth states
      // that don't have corresponding session records
      // For now, we'll just return 0 as this is a placeholder
      
      return 0;
    } catch (error) {
      logger.error('Error clearing orphaned auth states:', error);
      return 0;
    }
  }

  public async performHealthCheck(): Promise<SessionHealthReport> {
    try {
      const { sessions } = await dynamoDBService.getAllSessions();
      
      let healthySessions = 0;
      let unhealthySessions = 0;
      let disconnectedSessions = 0;
      let failedSessions = 0;
      const sessionsNeedingAttention: string[] = [];

      for (const session of sessions) {
        const health = await whatsappService.getSessionHealth(session.sessionId);
        
        if (health.healthy) {
          healthySessions++;
        } else {
          unhealthySessions++;
          
          // Check specific issues
          if (session.status === 'disconnected') {
            disconnectedSessions++;
            
            // If disconnected for more than 10 minutes, needs attention
            const tenMinutesAgo = Date.now() - (10 * 60 * 1000);
            if (session.lastSeen && session.lastSeen < tenMinutesAgo) {
              sessionsNeedingAttention.push(session.sessionId);
            }
          } else if (session.status === 'failed') {
            failedSessions++;
            sessionsNeedingAttention.push(session.sessionId);
          }
        }
      }

      const report: SessionHealthReport = {
        totalSessions: sessions.length,
        healthySessions,
        unhealthySessions,
        disconnectedSessions,
        failedSessions,
        sessionsNeedingAttention,
        timestamp: Date.now(),
      };

      // Log health issues
      if (sessionsNeedingAttention.length > 0) {
        logger.warn(`Sessions needing attention: ${sessionsNeedingAttention.join(', ')}`);
      }

      // Send webhook notification for health issues
      if (unhealthySessions > 0) {
        await this.sendHealthAlert(report);
      }

      return report;
    } catch (error) {
      logger.error('Error during health check:', error);
      throw error;
    }
  }

  public async checkSessionHealth(sessionId: string): Promise<boolean> {
    try {
      const health = await whatsappService.getSessionHealth(sessionId);
      return health.healthy;
    } catch (error) {
      logger.error(`Error checking health for session ${sessionId}:`, error);
      return false;
    }
  }

  public async forceSessionReconnection(sessionId: string): Promise<boolean> {
    try {
      logger.info(`Forcing reconnection for session ${sessionId}`);
      
      // Get the session
      const session = whatsappService.getSessionFromMemory(sessionId);
      if (!session) {
        logger.warn(`Session ${sessionId} not found in memory`);
        return false;
      }

      // Close existing connection
      try {
        session.client.end(undefined);
      } catch (error) {
        logger.warn(`Error closing connection for ${sessionId}:`, error);
      }

      // Remove from memory
      await whatsappService.deleteSession(sessionId);

      // Recreate session
      const dbSession = await dynamoDBService.getSession(sessionId);
      if (dbSession) {
        const sessionRequest = {
          id: dbSession.sessionId,
          name: dbSession.sessionName,
          webhookUrl: dbSession.webhookUrl,
        };
        
        await whatsappService.createSession(sessionRequest);
        return true;
      }

      return false;
    } catch (error) {
      logger.error(`Error forcing reconnection for session ${sessionId}:`, error);
      return false;
    }
  }

  private async sendHealthAlert(report: SessionHealthReport): Promise<void> {
    try {
      // Send to a global monitoring webhook if configured
      if (config.webhook.url) {
        await webhookService.sendWebhook({
          sessionId: 'monitoring',
          event: 'health_alert',
          data: report,
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error) {
      logger.error('Error sending health alert:', error);
    }
  }

  private async sendCleanupReport(report: CleanupReport): Promise<void> {
    try {
      // Send to a global monitoring webhook if configured
      if (config.webhook.url) {
        await webhookService.sendWebhook({
          sessionId: 'monitoring',
          event: 'cleanup_report',
          data: report,
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error) {
      logger.error('Error sending cleanup report:', error);
    }
  }

  public async getSystemHealth(): Promise<{
    whatsappService: boolean;
    dynamoDb: boolean;
    memoryUsage: NodeJS.MemoryUsage;
    uptime: number;
  }> {
    try {
      const whatsappServiceHealthy = await whatsappService.healthCheck();
      const dynamoDbHealthy = await dynamoDBService.healthCheck();

      return {
        whatsappService: whatsappServiceHealthy,
        dynamoDb: dynamoDbHealthy,
        memoryUsage: process.memoryUsage(),
        uptime: process.uptime(),
      };
    } catch (error) {
      logger.error('Error getting system health:', error);
      throw error;
    }
  }

  public async generateReport(): Promise<{
    health: SessionHealthReport;
    systemHealth: any;
    serviceStats: any;
  }> {
    try {
      const [health, systemHealth, serviceStats] = await Promise.all([
        this.performHealthCheck(),
        this.getSystemHealth(),
        whatsappService.getServiceStats(),
      ]);

      return {
        health,
        systemHealth,
        serviceStats,
      };
    } catch (error) {
      logger.error('Error generating monitoring report:', error);
      throw error;
    }
  }

  public isInitialized(): boolean {
    return this.initialized;
  }
}

// Create singleton instance
const monitoringService = new MonitoringService();
export default monitoringService;