import {
  DynamoDBClient,
  CreateTableCommand,
  DescribeTableCommand,
  DeleteTableCommand,
} from '@aws-sdk/client-dynamodb';
import {
  DynamoDBDocumentClient,
  PutCommand,
  GetCommand,
  UpdateCommand,
  DeleteCommand,
  <PERSON>an<PERSON>ommand,
  QueryCommand,
} from '@aws-sdk/lib-dynamodb';
import config from '../config';
import logger from '../utils/logger';

export interface SessionRecord {
  sessionId: string;           // Primary Key
  sessionName: string;
  phoneNumber?: string;
  status: 'pending' | 'connected' | 'disconnected' | 'failed';
  authState?: string;          // Encrypted <PERSON>'s auth state
  qrCode?: string;
  createdAt: number;
  updatedAt: number;
  expiresAt: number;           // TTL
  webhookUrl?: string;
  lastSeen?: number;
  connectionAttempts?: number;
  metadata?: Record<string, any>;
}

export interface SessionQueryOptions {
  status?: string;
  limit?: number;
  lastEvaluatedKey?: any;
}

class DynamoDBService {
  private client: DynamoDBClient;
  private docClient: DynamoDBDocumentClient;
  private tableName: string;

  constructor() {
    this.client = new DynamoDBClient({
      region: config.aws.region,
      credentials: config.aws.credentials,
    });
    this.docClient = DynamoDBDocumentClient.from(this.client);
    this.tableName = config.aws.dynamoTable;
  }

  async createTable(): Promise<boolean> {
    try {
      // Check if table already exists
      try {
        await this.client.send(new DescribeTableCommand({
          TableName: this.tableName,
        }));
        logger.info(`DynamoDB table ${this.tableName} already exists`);
        return true;
      } catch (error: any) {
        if (error.name !== 'ResourceNotFoundException') {
          throw error;
        }
      }

      // Create table
      const createTableCommand = new CreateTableCommand({
        TableName: this.tableName,
        KeySchema: [
          {
            AttributeName: 'sessionId',
            KeyType: 'HASH',
          },
        ],
        AttributeDefinitions: [
          {
            AttributeName: 'sessionId',
            AttributeType: 'S',
          },
          {
            AttributeName: 'status',
            AttributeType: 'S',
          },
          {
            AttributeName: 'createdAt',
            AttributeType: 'N',
          },
        ],
        GlobalSecondaryIndexes: [
          {
            IndexName: 'StatusIndex',
            KeySchema: [
              {
                AttributeName: 'status',
                KeyType: 'HASH',
              },
              {
                AttributeName: 'createdAt',
                KeyType: 'RANGE',
              },
            ],
            Projection: {
              ProjectionType: 'ALL',
            },
            ProvisionedThroughput: {
              ReadCapacityUnits: 5,
              WriteCapacityUnits: 5,
            },
          },
        ],
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
        TimeToLiveSpecification: {
          AttributeName: 'expiresAt',
          Enabled: true,
        },
      });

      await this.client.send(createTableCommand);
      logger.info(`DynamoDB table ${this.tableName} created successfully`);
      return true;
    } catch (error) {
      logger.error('Error creating DynamoDB table:', error);
      throw error;
    }
  }

  async createSession(sessionData: SessionRecord): Promise<void> {
    try {
      const now = Date.now();
      const sessionRecord: SessionRecord = {
        ...sessionData,
        createdAt: now,
        updatedAt: now,
        expiresAt: now + (config.session.timeoutHours * 60 * 60 * 1000), // TTL
      };

      const command = new PutCommand({
        TableName: this.tableName,
        Item: sessionRecord,
        ConditionExpression: 'attribute_not_exists(sessionId)',
      });

      await this.docClient.send(command);
      logger.info(`Session created in DynamoDB: ${sessionData.sessionId}`);
    } catch (error) {
      logger.error(`Error creating session ${sessionData.sessionId}:`, error);
      throw error;
    }
  }

  async getSession(sessionId: string): Promise<SessionRecord | null> {
    try {
      const command = new GetCommand({
        TableName: this.tableName,
        Key: { sessionId },
      });

      const result = await this.docClient.send(command);
      return result.Item as SessionRecord || null;
    } catch (error) {
      logger.error(`Error getting session ${sessionId}:`, error);
      throw error;
    }
  }

  async updateSession(sessionId: string, updates: Partial<SessionRecord>): Promise<void> {
    try {
      // Build update expression dynamically
      const updateExpressions: string[] = [];
      const expressionAttributeNames: Record<string, string> = {};
      const expressionAttributeValues: Record<string, any> = {};

      Object.entries(updates).forEach(([key, value]) => {
        if (key !== 'sessionId' && value !== undefined) {
          updateExpressions.push(`#${key} = :${key}`);
          expressionAttributeNames[`#${key}`] = key;
          expressionAttributeValues[`:${key}`] = value;
        }
      });

      // Always update the updatedAt timestamp
      updateExpressions.push('#updatedAt = :updatedAt');
      expressionAttributeNames['#updatedAt'] = 'updatedAt';
      expressionAttributeValues[':updatedAt'] = Date.now();

      const command = new UpdateCommand({
        TableName: this.tableName,
        Key: { sessionId },
        UpdateExpression: `SET ${updateExpressions.join(', ')}`,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: expressionAttributeValues,
        ConditionExpression: 'attribute_exists(sessionId)',
      });

      await this.docClient.send(command);
      logger.info(`Session updated in DynamoDB: ${sessionId}`);
    } catch (error) {
      logger.error(`Error updating session ${sessionId}:`, error);
      throw error;
    }
  }

  async deleteSession(sessionId: string): Promise<void> {
    try {
      const command = new DeleteCommand({
        TableName: this.tableName,
        Key: { sessionId },
        ConditionExpression: 'attribute_exists(sessionId)',
      });

      await this.docClient.send(command);
      logger.info(`Session deleted from DynamoDB: ${sessionId}`);
    } catch (error) {
      logger.error(`Error deleting session ${sessionId}:`, error);
      throw error;
    }
  }

  async getAllSessions(options: SessionQueryOptions = {}): Promise<{
    sessions: SessionRecord[];
    lastEvaluatedKey?: any;
  }> {
    try {
      const command = new ScanCommand({
        TableName: this.tableName,
        Limit: options.limit || 50,
        ExclusiveStartKey: options.lastEvaluatedKey,
      });

      const result = await this.docClient.send(command);
      return {
        sessions: (result.Items as SessionRecord[]) || [],
        lastEvaluatedKey: result.LastEvaluatedKey,
      };
    } catch (error) {
      logger.error('Error getting all sessions:', error);
      throw error;
    }
  }

  async getSessionsByStatus(status: string, options: SessionQueryOptions = {}): Promise<{
    sessions: SessionRecord[];
    lastEvaluatedKey?: any;
  }> {
    try {
      const command = new QueryCommand({
        TableName: this.tableName,
        IndexName: 'StatusIndex',
        KeyConditionExpression: '#status = :status',
        ExpressionAttributeNames: {
          '#status': 'status',
        },
        ExpressionAttributeValues: {
          ':status': status,
        },
        Limit: options.limit || 50,
        ExclusiveStartKey: options.lastEvaluatedKey,
        ScanIndexForward: false, // Most recent first
      });

      const result = await this.docClient.send(command);
      return {
        sessions: (result.Items as SessionRecord[]) || [],
        lastEvaluatedKey: result.LastEvaluatedKey,
      };
    } catch (error) {
      logger.error(`Error getting sessions by status ${status}:`, error);
      throw error;
    }
  }

  async cleanupExpiredSessions(): Promise<number> {
    try {
      const now = Date.now();
      let deletedCount = 0;
      let lastEvaluatedKey: any = undefined;

      do {
        const scanResult = await this.docClient.send(new ScanCommand({
          TableName: this.tableName,
          FilterExpression: 'expiresAt < :now',
          ExpressionAttributeValues: {
            ':now': now,
          },
          ExclusiveStartKey: lastEvaluatedKey,
          Limit: 25,
        }));

        if (scanResult.Items && scanResult.Items.length > 0) {
          for (const item of scanResult.Items) {
            await this.deleteSession(item.sessionId);
            deletedCount++;
          }
        }

        lastEvaluatedKey = scanResult.LastEvaluatedKey;
      } while (lastEvaluatedKey);

      logger.info(`Cleaned up ${deletedCount} expired sessions`);
      return deletedCount;
    } catch (error) {
      logger.error('Error cleaning up expired sessions:', error);
      throw error;
    }
  }

  async getSessionStats(): Promise<{
    total: number;
    byStatus: Record<string, number>;
  }> {
    try {
      const allSessions = await this.getAllSessions({ limit: 1000 });
      const stats = {
        total: allSessions.sessions.length,
        byStatus: {} as Record<string, number>,
      };

      allSessions.sessions.forEach((session) => {
        stats.byStatus[session.status] = (stats.byStatus[session.status] || 0) + 1;
      });

      return stats;
    } catch (error) {
      logger.error('Error getting session stats:', error);
      throw error;
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      await this.client.send(new DescribeTableCommand({
        TableName: this.tableName,
      }));
      return true;
    } catch (error) {
      logger.error('DynamoDB health check failed:', error);
      return false;
    }
  }
}

// Create singleton instance
const dynamoDBService = new DynamoDBService();
export default dynamoDBService;