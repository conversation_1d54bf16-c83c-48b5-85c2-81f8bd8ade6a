import makeWASocket, {
  DisconnectReason,
  useMultiFileAuthState,
  WASocket,
  fetchLatestBaileysVersion,
  makeCacheableSignalKeyStore,
  isJidUser,
} from '@whiskeysockets/baileys';
import { Boom } from '@hapi/boom';
import qrcode from 'qrcode-terminal';
import fs from 'fs';
import path from 'path';
import { WhatsAppSession as BaileysSession, WebhookPayload } from '../types';
import config from '../config';
import { sendWebhook } from '../utils/webhook';
import { db, WhatsAppSession as DbSession } from '../database/dynamodb';

// Enhanced session interface that combines in-memory and database session
interface EnhancedSession extends BaileysSession {
  userId: string;
  dbSession: DbSession;
}

class EnhancedWhatsAppService {
  private sessions: Map<string, EnhancedSession> = new Map();

  constructor() {
    // Create sessions directory if it doesn't exist
    if (!fs.existsSync(config.sessionsDir)) {
      fs.mkdirSync(config.sessionsDir, { recursive: true });
    }

    // Initialize existing sessions on startup
    this.initializeExistingSessions();
  }

  /**
   * Initialize existing sessions from database on startup
   */
  private async initializeExistingSessions() {
    try {
      const activeSessions = await db.getAllActiveSessions();
      
      for (const dbSession of activeSessions) {
        // Only initialize sessions that have valid auth state
        const sessionDir = path.join(config.sessionsDir, dbSession.id);
        if (fs.existsSync(sessionDir)) {
          await this.createSession(dbSession.id, dbSession.userId, dbSession.name);
        } else {
          // Mark session as disconnected if auth files don't exist
          await db.updateSession(dbSession.id, { 
            status: 'disconnected',
            isActive: false 
          });
        }
      }
    } catch (error) {
      console.error('Error initializing existing sessions:', error);
    }
  }

  /**
   * Create a new WhatsApp session for a user
   */
  public async createSession(sessionId: string, userId: string, sessionName?: string): Promise<EnhancedSession | null> {
    try {
      // Check if user can create more sessions
      const canCreate = await db.canUserCreateSession(userId);
      if (!canCreate) {
        throw new Error('User has reached maximum session limit');
      }

      // Check if session already exists in memory
      if (this.sessions.has(sessionId)) {
        return this.sessions.get(sessionId) || null;
      }

      // Create or update session in database
      let dbSession = await db.getSessionById(sessionId);
      if (!dbSession) {
        const sessionDir = path.join(config.sessionsDir, sessionId);
        dbSession = await db.createSession({
          userId,
          name: sessionName,
          status: 'pending',
          isActive: true,
          sessionDirectory: sessionDir,
        });
      }

      // Create session directory
      const sessionDir = path.join(config.sessionsDir, sessionId);
      if (!fs.existsSync(sessionDir)) {
        fs.mkdirSync(sessionDir, { recursive: true });
      }

      // Initialize auth state
      const { state, saveCreds } = await useMultiFileAuthState(sessionDir);

      // Fetch latest version
      const { version } = await fetchLatestBaileysVersion();

      // Create WhatsApp client
      const sock = makeWASocket({
        auth: {
          creds: state.creds,
          // @ts-ignore
          keys: makeCacheableSignalKeyStore(state.keys, console.log),
        },
        printQRInTerminal: true,
        browser: ['WhatsApp API', 'Chrome', '103.0.5060.114'],
      });

      // Create enhanced session object
      const session: EnhancedSession = {
        id: sessionId,
        name: sessionName,
        client: sock,
        ready: false,
        userId,
        dbSession,
      };

      // Store session immediately
      this.sessions.set(sessionId, session);

      // Update database session status
      await db.updateSession(sessionId, { status: 'connecting' });

      // Set up connection events
      sock.ev.on('connection.update', async (update) => {
        const { connection, lastDisconnect, qr } = update;

        if (qr) {
          console.log(`QR code received for session ${sessionId}`);

          // Store the QR code in the session
          session.qr = qr;

          // Update database with QR code
          await db.updateSession(sessionId, { 
            qrCode: qr,
            status: 'pending' 
          });

          // Display QR code in terminal for debugging
          qrcode.generate(qr, { small: true });

          // Send webhook notification
          await sendWebhook({
            sessionId,
            event: 'qr',
            data: { qr, userId },
          });

          // Update the session in the map
          this.sessions.set(sessionId, session);
        }

        if (connection === 'close') {
          const shouldReconnect = (lastDisconnect?.error as Boom)?.output?.statusCode !== DisconnectReason.loggedOut;

          if (shouldReconnect) {
            // Update database status
            await db.updateSession(sessionId, { status: 'disconnected' });
            
            // Reconnect if not logged out
            setTimeout(() => {
              this.createSession(sessionId, userId, sessionName);
            }, 5000);
          } else {
            // Remove session if logged out
            this.sessions.delete(sessionId);
            
            // Update database status
            await db.updateSession(sessionId, { 
              status: 'expired',
              isActive: false 
            });

            // Send webhook notification
            await sendWebhook({
              sessionId,
              event: 'disconnected',
              data: { reason: 'logged_out', userId },
            });
          }
        } else if (connection === 'open') {
          // Session is ready
          session.ready = true;
          session.lastSeen = new Date();

          // Extract phone number from user info
          const phoneNumber = sock.user?.id?.split(':')[0];

          // Update database with connection info
          await db.updateSession(sessionId, {
            status: 'connected',
            phoneNumber,
            lastSeen: new Date().toISOString(),
          });

          // Send webhook notification
          await sendWebhook({
            sessionId,
            event: 'connected',
            data: {
              user: sock.user,
              time: new Date().toISOString(),
              userId,
            },
          });
        }
      });

      // Save credentials on update
      sock.ev.on('creds.update', saveCreds);

      // Handle messages
      sock.ev.on('messages.upsert', async (m) => {
        if (m.type === 'notify') {
          for (const msg of m.messages) {
            if (!msg.key.fromMe && isJidUser(msg.key.remoteJid || '')) {
              // Send webhook notification for new message
              await sendWebhook({
                sessionId,
                event: 'message',
                data: { ...msg, userId },
              });
            }
          }
        }
      });

      return session;
    } catch (error) {
      console.error(`Error creating session ${sessionId}:`, error);
      
      // Update database with error status
      try {
        await db.updateSession(sessionId, { 
          status: 'disconnected',
          isActive: false 
        });
      } catch (dbError) {
        console.error('Error updating session status in database:', dbError);
      }
      
      return null;
    }
  }

  /**
   * Get session if user owns it
   */
  public async getSession(sessionId: string, userId: string): Promise<EnhancedSession | null> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return null;
    }

    // Check ownership
    if (session.userId !== userId) {
      return null;
    }

    return session;
  }

  /**
   * Get all sessions for a user
   */
  public async getUserSessions(userId: string): Promise<EnhancedSession[]> {
    const allSessions = Array.from(this.sessions.values());
    return allSessions.filter(session => session.userId === userId);
  }

  /**
   * Get all sessions (admin only)
   */
  public getAllSessions(): EnhancedSession[] {
    return Array.from(this.sessions.values());
  }

  /**
   * Delete session if user owns it
   */
  public async deleteSession(sessionId: string, userId: string): Promise<boolean> {
    try {
      const session = this.sessions.get(sessionId);
      if (!session) {
        return false;
      }

      // Check ownership
      if (session.userId !== userId) {
        return false;
      }

      // Close connection
      session.client.end(undefined);

      // Remove from sessions map
      this.sessions.delete(sessionId);

      // Delete session directory
      const sessionDir = path.join(config.sessionsDir, sessionId);
      if (fs.existsSync(sessionDir)) {
        fs.rmSync(sessionDir, { recursive: true, force: true });
      }

      // Delete from database
      await db.deleteSession(sessionId);

      return true;
    } catch (error) {
      console.error(`Error deleting session ${sessionId}:`, error);
      return false;
    }
  }

  /**
   * Send text message if user owns the session
   */
  public async sendTextMessage(sessionId: string, userId: string, to: string, text: string): Promise<any> {
    try {
      const session = await this.getSession(sessionId, userId);
      if (!session || !session.ready) {
        throw new Error('Session not found, not owned by user, or not ready');
      }

      // Format phone number
      const formattedNumber = this.formatPhoneNumber(to);

      // Send message
      const result = await session.client.sendMessage(formattedNumber, { text });
      return result;
    } catch (error) {
      console.error(`Error sending message in session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Send media message if user owns the session
   */
  public async sendMediaMessage(
    sessionId: string,
    userId: string,
    to: string,
    url: string,
    caption?: string,
    mimetype?: string
  ): Promise<any> {
    try {
      const session = await this.getSession(sessionId, userId);
      if (!session || !session.ready) {
        throw new Error('Session not found, not owned by user, or not ready');
      }

      // Format phone number
      const formattedNumber = this.formatPhoneNumber(to);

      // Send media message
      const result = await session.client.sendMessage(formattedNumber, {
        image: { url },
        caption: caption || '',
        mimetype: mimetype || 'image/jpeg',
      });

      return result;
    } catch (error) {
      console.error(`Error sending media message in session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Get session status with database sync
   */
  public async getSessionStatus(sessionId: string, userId: string): Promise<DbSession | null> {
    const session = this.sessions.get(sessionId);
    if (!session || session.userId !== userId) {
      return null;
    }

    // Get latest status from database
    const dbSession = await db.getSessionById(sessionId);
    if (!dbSession) {
      return null;
    }

    // Update database with current connection status
    if (session.ready && dbSession.status !== 'connected') {
      await db.updateSession(sessionId, { 
        status: 'connected',
        lastSeen: new Date().toISOString(),
      });
    }

    return db.getSessionById(sessionId);
  }

  /**
   * Cleanup disconnected sessions
   */
  public async cleanupDisconnectedSessions(): Promise<void> {
    const allSessions = Array.from(this.sessions.values());
    
    for (const session of allSessions) {
      if (!session.ready) {
        const timeSinceLastSeen = session.lastSeen ? 
          Date.now() - session.lastSeen.getTime() : 
          Infinity;

        // Clean up sessions that have been disconnected for more than 1 hour
        if (timeSinceLastSeen > 60 * 60 * 1000) {
          console.log(`Cleaning up disconnected session: ${session.id}`);
          await this.deleteSession(session.id, session.userId);
        }
      }
    }
  }

  private formatPhoneNumber(phoneNumber: string): string {
    // Remove any non-digit characters
    const digits = phoneNumber.replace(/\D/g, '');

    // Ensure the number has the country code
    if (digits.startsWith('0')) {
      return `6${digits.substring(1)}@s.whatsapp.net`;
    } else if (!digits.includes('@')) {
      return `${digits}@s.whatsapp.net`;
    }

    return phoneNumber;
  }
}

// Create singleton instance
const enhancedWhatsAppService = new EnhancedWhatsAppService();
export default enhancedWhatsAppService;