import makeWASocket, {
  Disconnect<PERSON><PERSON>on,
  WASocket,
  fetchLatestBaileysVersion,
  makeCacheableSignalKeyStore,
  isJidUser,
} from '@whiskeysockets/baileys';
import { Boom } from '@hapi/boom';
// @ts-ignore
import qrcode from 'qrcode-terminal';
import { WhatsAppSession, WebhookPayload, SessionCreateRequest, SessionInfo, BulkMessageRequest, BulkMessageResponse } from '../types';
import config from '../config';
import { webhookService } from '../utils/webhook';
import dynamoDBService, { SessionRecord } from './dynamodb.service';
import { useDynamoDBAuthState } from './dynamodb-auth-state';
import logger, { createSessionLogger } from '../utils/logger';

class WhatsAppService {
  private sessions: Map<string, WhatsAppSession> = new Map();
  private initialized: boolean = false;

  constructor() {
    this.initializeService();
  }

  private async initializeService(): Promise<void> {
    try {
      // Initialize DynamoDB table
      await dynamoDBService.createTable();
      
      // Load existing sessions from DynamoDB
      await this.loadExistingSessions();
      
      this.initialized = true;
      logger.info('WhatsApp service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize WhatsApp service:', error);
      throw error;
    }
  }

  private async loadExistingSessions(): Promise<void> {
    try {
      const { sessions } = await dynamoDBService.getAllSessions();
      
      for (const sessionRecord of sessions) {
        if (sessionRecord.status === 'connected') {
          // Attempt to restore connected sessions
          await this.restoreSession(sessionRecord);
        }
      }
      
      logger.info(`Loaded ${sessions.length} existing sessions from DynamoDB`);
    } catch (error) {
      logger.error('Failed to load existing sessions:', error);
    }
  }

  private async restoreSession(sessionRecord: SessionRecord): Promise<void> {
    try {
      const sessionLogger = createSessionLogger(sessionRecord.sessionId);
      sessionLogger.info('Attempting to restore session');

      // Create WhatsApp client with DynamoDB auth state
      const { state, saveCreds } = await useDynamoDBAuthState(sessionRecord.sessionId);
      const { version } = await fetchLatestBaileysVersion();

      const sock = makeWASocket({
        auth: {
          creds: state.creds,
          keys: makeCacheableSignalKeyStore(state.keys, sessionLogger.debug),
        },
        printQRInTerminal: false, // Don't print QR for restored sessions
        browser: ['WhatsApp API', 'Chrome', '103.0.5060.114'],
        version,
      });

      const session: WhatsAppSession = {
        id: sessionRecord.sessionId,
        name: sessionRecord.sessionName,
        client: sock,
        ready: false,
        status: 'pending',
        phoneNumber: sessionRecord.phoneNumber,
        webhookUrl: sessionRecord.webhookUrl,
        connectionAttempts: sessionRecord.connectionAttempts || 0,
      };

      await this.setupSessionEventHandlers(session, saveCreds);
      this.sessions.set(sessionRecord.sessionId, session);
      
      sessionLogger.info('Session restoration initiated');
    } catch (error) {
      logger.error(`Failed to restore session ${sessionRecord.sessionId}:`, error);
    }
  }

  public async createSession(sessionRequest: SessionCreateRequest): Promise<WhatsAppSession | null> {
    try {
      const { id: sessionId, name: sessionName, webhookUrl } = sessionRequest;
      const sessionLogger = createSessionLogger(sessionId);
      
      // Check if session already exists in memory
      if (this.sessions.has(sessionId)) {
        sessionLogger.info('Session already exists in memory');
        return this.sessions.get(sessionId) || null;
      }

      // Check if session exists in DynamoDB
      const existingSession = await dynamoDBService.getSession(sessionId);
      if (existingSession) {
        sessionLogger.info('Session already exists in DynamoDB, restoring');
        await this.restoreSession(existingSession);
        return this.sessions.get(sessionId) || null;
      }

      sessionLogger.info('Creating new session');

      // Create session record in DynamoDB first
      const sessionRecord: SessionRecord = {
        sessionId,
        sessionName: sessionName || sessionId,
        status: 'pending',
        createdAt: Date.now(),
        updatedAt: Date.now(),
        expiresAt: Date.now() + (config.session.timeoutHours * 60 * 60 * 1000),
        webhookUrl,
        connectionAttempts: 0,
      };

      await dynamoDBService.createSession(sessionRecord);

      // Create WhatsApp client with DynamoDB auth state
      const { state, saveCreds } = await useDynamoDBAuthState(sessionId);
      const { version } = await fetchLatestBaileysVersion();

      const sock = makeWASocket({
        auth: {
          creds: state.creds,
          keys: makeCacheableSignalKeyStore(state.keys, sessionLogger.debug),
        },
        printQRInTerminal: true, // Enable QR code in terminal for debugging
        browser: ['WhatsApp API', 'Chrome', '103.0.5060.114'],
        version,
      });

      // Create session object
      const session: WhatsAppSession = {
        id: sessionId,
        name: sessionName,
        client: sock,
        ready: false,
        status: 'pending',
        webhookUrl,
        connectionAttempts: 0,
      };

      // Set up event handlers
      await this.setupSessionEventHandlers(session, saveCreds);

      sessionLogger.info('Session created successfully');
      return session;
    } catch (error) {
      logger.error(`Error creating session ${sessionRequest.id}:`, error);
      return null;
    }
  }

  private async setupSessionEventHandlers(session: WhatsAppSession, saveCreds: () => Promise<void>): Promise<void> {
    const sessionLogger = createSessionLogger(session.id);
    const sock = session.client;

    // Connection events
    sock.ev.on('connection.update', async (update) => {
      const { connection, lastDisconnect, qr } = update;

      if (qr) {
        sessionLogger.info('QR code received');
        
        // Store the QR code in the session
        session.qr = qr;
        
        // Update DynamoDB
        await dynamoDBService.updateSession(session.id, {
          qrCode: qr,
          status: 'pending',
        });

        // Display QR code in terminal for debugging
        qrcode.generate(qr, { small: true });

        // Send webhook notification
        await webhookService.sendSessionWebhook(session.id, 'qr', { qr });

        // Update the session in memory
        this.sessions.set(session.id, session);

        sessionLogger.info('QR code generated and stored');
      }

      if (connection === 'close') {
        const shouldReconnect = (lastDisconnect?.error as Boom)?.output?.statusCode !== DisconnectReason.loggedOut;
        
        sessionLogger.warn('Connection closed', { 
          shouldReconnect, 
          reason: lastDisconnect?.error?.message 
        });

        if (shouldReconnect) {
          // Update status and attempt reconnection
          session.status = 'disconnected';
          session.connectionAttempts = (session.connectionAttempts || 0) + 1;
          
          await dynamoDBService.updateSession(session.id, {
            status: 'disconnected',
            connectionAttempts: session.connectionAttempts,
          });

          // Send webhook notification
          await webhookService.sendSessionWebhook(session.id, 'disconnected', { 
            reason: 'connection_lost',
            attempts: session.connectionAttempts,
          });

          // Attempt reconnection if under limit
          if (session.connectionAttempts < 5) {
            sessionLogger.info(`Attempting reconnection (${session.connectionAttempts}/5)`);
            setTimeout(() => this.reconnectSession(session.id), 5000);
          } else {
            sessionLogger.error('Max reconnection attempts reached');
            await this.handleSessionFailure(session.id, 'Max reconnection attempts exceeded');
          }
        } else {
          // Logged out - clean up session
          sessionLogger.info('Session logged out, cleaning up');
          await this.cleanupSession(session.id, 'logged_out');
        }
      } else if (connection === 'open') {
        // Session is ready
        session.ready = true;
        session.status = 'connected';
        session.lastSeen = new Date();
        session.connectionAttempts = 0;
        
        // Extract phone number if available
        if (sock.user?.id) {
          session.phoneNumber = sock.user.id.split('@')[0];
        }

        // Update DynamoDB
        await dynamoDBService.updateSession(session.id, {
          status: 'connected',
          phoneNumber: session.phoneNumber,
          lastSeen: Date.now(),
          connectionAttempts: 0,
        });

        // Send webhook notification
        await webhookService.sendSessionWebhook(session.id, 'connected', {
          user: sock.user,
          phoneNumber: session.phoneNumber,
          timestamp: new Date().toISOString(),
        });

        sessionLogger.info('Session connected successfully', {
          phoneNumber: session.phoneNumber,
        });
      }
    });

    // Save credentials on update
    sock.ev.on('creds.update', saveCreds);

    // Handle incoming messages
    sock.ev.on('messages.upsert', async (m) => {
      if (m.type === 'notify') {
        for (const msg of m.messages) {
          if (!msg.key.fromMe && isJidUser(msg.key.remoteJid || '')) {
            // Update last activity
            session.lastSeen = new Date();
            await dynamoDBService.updateSession(session.id, {
              lastSeen: Date.now(),
            });

            // Send webhook notification for new message
            await webhookService.sendSessionWebhook(session.id, 'message', msg);
            
            sessionLogger.debug('Message received and webhook sent');
          }
        }
      }
    });
  }

  private async reconnectSession(sessionId: string): Promise<void> {
    try {
      const sessionLogger = createSessionLogger(sessionId);
      sessionLogger.info('Attempting session reconnection');

      const session = this.sessions.get(sessionId);
      if (!session) {
        sessionLogger.error('Session not found in memory for reconnection');
        return;
      }

      // Create new WhatsApp client
      const { state, saveCreds } = await useDynamoDBAuthState(sessionId);
      const { version } = await fetchLatestBaileysVersion();

      const sock = makeWASocket({
        auth: {
          creds: state.creds,
          keys: makeCacheableSignalKeyStore(state.keys, sessionLogger.debug),
        },
        printQRInTerminal: false,
        browser: ['WhatsApp API', 'Chrome', '103.0.5060.114'],
        version,
      });

      // Update session with new client
      session.client = sock;
      session.ready = false;
      session.status = 'pending';

      // Set up event handlers
      await this.setupSessionEventHandlers(session, saveCreds);

      sessionLogger.info('Session reconnection initiated');
    } catch (error) {
      logger.error(`Failed to reconnect session ${sessionId}:`, error);
      await this.handleSessionFailure(sessionId, 'Reconnection failed');
    }
  }

  private async handleSessionFailure(sessionId: string, reason: string): Promise<void> {
    try {
      const sessionLogger = createSessionLogger(sessionId);
      sessionLogger.error('Session failed:', reason);

      // Update status in DynamoDB
      await dynamoDBService.updateSession(sessionId, {
        status: 'failed',
        metadata: {
          failureReason: reason,
          failedAt: Date.now(),
        },
      });

      // Send webhook notification
      await webhookService.sendSessionWebhook(sessionId, 'failed', {
        reason,
        timestamp: new Date().toISOString(),
      });

      // Remove from memory but keep in DynamoDB for analysis
      this.sessions.delete(sessionId);
    } catch (error) {
      logger.error(`Error handling session failure for ${sessionId}:`, error);
    }
  }

  private async cleanupSession(sessionId: string, reason: string): Promise<void> {
    try {
      const sessionLogger = createSessionLogger(sessionId);
      sessionLogger.info('Cleaning up session:', reason);

      // Update status in DynamoDB
      await dynamoDBService.updateSession(sessionId, {
        status: 'disconnected',
        metadata: {
          disconnectReason: reason,
          disconnectedAt: Date.now(),
        },
      });

      // Send webhook notification
      await webhookService.sendSessionWebhook(sessionId, 'disconnected', {
        reason,
        timestamp: new Date().toISOString(),
      });

      // Remove from memory
      this.sessions.delete(sessionId);
    } catch (error) {
      logger.error(`Error cleaning up session ${sessionId}:`, error);
    }
  }

  public async getSession(sessionId: string): Promise<WhatsAppSession | null> {
    // First check memory
    const memorySession = this.sessions.get(sessionId);
    if (memorySession) {
      return memorySession;
    }

    // If not in memory, check DynamoDB
    try {
      const dbSession = await dynamoDBService.getSession(sessionId);
      if (dbSession && dbSession.status === 'connected') {
        // Try to restore the session
        await this.restoreSession(dbSession);
        return this.sessions.get(sessionId) || null;
      }
    } catch (error) {
      logger.error(`Error retrieving session ${sessionId}:`, error);
    }

    return null;
  }

  public getSessionFromMemory(sessionId: string): WhatsAppSession | null {
    return this.sessions.get(sessionId) || null;
  }

  public async getAllSessions(): Promise<SessionInfo[]> {
    try {
      const { sessions } = await dynamoDBService.getAllSessions();
      
      return sessions.map(dbSession => {
        const memorySession = this.sessions.get(dbSession.sessionId);
        
        return {
          id: dbSession.sessionId,
          name: dbSession.sessionName,
          ready: memorySession?.ready || false,
          lastSeen: dbSession.lastSeen ? new Date(dbSession.lastSeen) : undefined,
          status: dbSession.status,
          phoneNumber: dbSession.phoneNumber,
          webhookUrl: dbSession.webhookUrl,
          createdAt: dbSession.createdAt,
          connectionAttempts: dbSession.connectionAttempts,
        };
      });
    } catch (error) {
      logger.error('Error getting all sessions:', error);
      return [];
    }
  }

  public getMemorySessions(): WhatsAppSession[] {
    return Array.from(this.sessions.values());
  }

  public async deleteSession(sessionId: string): Promise<boolean> {
    try {
      const sessionLogger = createSessionLogger(sessionId);
      sessionLogger.info('Deleting session');

      // Get session from memory
      const session = this.sessions.get(sessionId);
      
      // Close connection if exists
      if (session) {
        try {
          session.client.end(undefined);
        } catch (error) {
          sessionLogger.warn('Error closing WhatsApp connection:', error);
        }
        
        // Remove from memory
        this.sessions.delete(sessionId);
      }

      // Delete from DynamoDB
      await dynamoDBService.deleteSession(sessionId);

      // Send webhook notification
      await webhookService.sendSessionWebhook(sessionId, 'deleted', {
        timestamp: new Date().toISOString(),
      });

      sessionLogger.info('Session deleted successfully');
      return true;
    } catch (error) {
      logger.error(`Error deleting session ${sessionId}:`, error);
      return false;
    }
  }

  public async updateSession(sessionId: string, updates: { name?: string; webhookUrl?: string }): Promise<boolean> {
    try {
      const sessionLogger = createSessionLogger(sessionId);
      
      // Update DynamoDB
      const dbUpdates: Partial<SessionRecord> = {};
      if (updates.name !== undefined) {
        dbUpdates.sessionName = updates.name;
      }
      if (updates.webhookUrl !== undefined) {
        dbUpdates.webhookUrl = updates.webhookUrl;
      }

      await dynamoDBService.updateSession(sessionId, dbUpdates);

      // Update memory session if exists
      const session = this.sessions.get(sessionId);
      if (session) {
        if (updates.name !== undefined) {
          session.name = updates.name;
        }
        if (updates.webhookUrl !== undefined) {
          session.webhookUrl = updates.webhookUrl;
        }
      }

      sessionLogger.info('Session updated successfully');
      return true;
    } catch (error) {
      logger.error(`Error updating session ${sessionId}:`, error);
      return false;
    }
  }

  public async sendTextMessage(sessionId: string, to: string, text: string): Promise<any> {
    try {
      const sessionLogger = createSessionLogger(sessionId);
      const session = this.sessions.get(sessionId);
      
      if (!session || !session.ready) {
        throw new Error('Session not found or not ready');
      }

      // Format phone number
      const formattedNumber = this.formatPhoneNumber(to);

      // Send message
      const result = await session.client.sendMessage(formattedNumber, { text });
      
      sessionLogger.info(`Text message sent to ${to}`);
      
      // Update session stats in DynamoDB
      await this.updateMessageStats(sessionId, 'sent');

      return result;
    } catch (error) {
      logger.error(`Error sending message in session ${sessionId}:`, error);
      throw error;
    }
  }

  public async sendMediaMessage(
    sessionId: string,
    to: string,
    url: string,
    caption?: string,
    mimetype?: string
  ): Promise<any> {
    try {
      const sessionLogger = createSessionLogger(sessionId);
      const session = this.sessions.get(sessionId);
      
      if (!session || !session.ready) {
        throw new Error('Session not found or not ready');
      }

      // Format phone number
      const formattedNumber = this.formatPhoneNumber(to);

      // Determine media type based on mimetype
      let mediaMessage: any;
      if (mimetype?.startsWith('image/')) {
        mediaMessage = {
          image: { url },
          caption: caption || '',
          mimetype: mimetype,
        };
      } else if (mimetype?.startsWith('video/')) {
        mediaMessage = {
          video: { url },
          caption: caption || '',
          mimetype: mimetype,
        };
      } else if (mimetype?.startsWith('audio/')) {
        mediaMessage = {
          audio: { url },
          mimetype: mimetype,
        };
      } else {
        // Default to image
        mediaMessage = {
          image: { url },
          caption: caption || '',
          mimetype: mimetype || 'image/jpeg',
        };
      }

      // Send media message
      const result = await session.client.sendMessage(formattedNumber, mediaMessage);
      
      sessionLogger.info(`Media message sent to ${to}`);
      
      // Update session stats
      await this.updateMessageStats(sessionId, 'sent');

      return result;
    } catch (error) {
      logger.error(`Error sending media message in session ${sessionId}:`, error);
      throw error;
    }
  }

  public async sendDocumentMessage(
    sessionId: string,
    to: string,
    url: string,
    filename?: string,
    mimetype?: string
  ): Promise<any> {
    try {
      const sessionLogger = createSessionLogger(sessionId);
      const session = this.sessions.get(sessionId);
      
      if (!session || !session.ready) {
        throw new Error('Session not found or not ready');
      }

      // Format phone number
      const formattedNumber = this.formatPhoneNumber(to);

      // Send document message
      const result = await session.client.sendMessage(formattedNumber, {
        document: { url },
        fileName: filename || 'document.pdf',
        mimetype: mimetype || 'application/pdf',
      });
      
      sessionLogger.info(`Document sent to ${to}`);
      
      // Update session stats
      await this.updateMessageStats(sessionId, 'sent');

      return result;
    } catch (error) {
      logger.error(`Error sending document in session ${sessionId}:`, error);
      throw error;
    }
  }

  public async sendBulkMessages(
    sessionId: string,
    request: BulkMessageRequest
  ): Promise<BulkMessageResponse> {
    const sessionLogger = createSessionLogger(sessionId);
    const session = this.sessions.get(sessionId);
    
    if (!session || !session.ready) {
      throw new Error('Session not found or not ready');
    }

    const results: BulkMessageResponse['results'] = [];
    const delay = request.delay || 1000; // Default 1 second delay

    sessionLogger.info(`Starting bulk message to ${request.recipients.length} recipients`);

    for (const recipient of request.recipients) {
      try {
        let result: any;

        if (request.message.text) {
          result = await this.sendTextMessage(sessionId, recipient, request.message.text);
        } else if (request.message.media) {
          result = await this.sendMediaMessage(
            sessionId,
            recipient,
            request.message.media.url,
            request.message.media.caption,
            request.message.media.mimetype
          );
        }

        results.push({
          to: recipient,
          success: true,
          messageId: result?.key?.id,
        });

        // Wait before sending next message
        if (delay > 0) {
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      } catch (error) {
        results.push({
          to: recipient,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    const summary = {
      total: request.recipients.length,
      sent: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
    };

    sessionLogger.info(`Bulk message completed: ${summary.sent}/${summary.total} sent`);

    return {
      success: true,
      results,
      summary,
    };
  }

  private async updateMessageStats(sessionId: string, type: 'sent' | 'received'): Promise<void> {
    try {
      const session = await dynamoDBService.getSession(sessionId);
      if (session) {
        const metadata = session.metadata || {};
        const stats = metadata.messageStats || { sent: 0, received: 0 };
        
        stats[type]++;
        stats.lastActivity = Date.now();
        metadata.messageStats = stats;

        await dynamoDBService.updateSession(sessionId, { metadata });
      }
    } catch (error) {
      logger.error(`Error updating message stats for session ${sessionId}:`, error);
    }
  }

  public async getSessionHealth(sessionId: string): Promise<{
    healthy: boolean;
    status: string;
    lastSeen?: number;
    uptime?: number;
    errors?: string[];
  }> {
    try {
      const session = this.sessions.get(sessionId);
      const dbSession = await dynamoDBService.getSession(sessionId);
      
      if (!dbSession) {
        return {
          healthy: false,
          status: 'not_found',
          errors: ['Session not found in database'],
        };
      }

      const healthy = session?.ready && dbSession.status === 'connected';
      const uptime = dbSession.lastSeen ? Date.now() - dbSession.lastSeen : undefined;
      
      return {
        healthy,
        status: dbSession.status,
        lastSeen: dbSession.lastSeen,
        uptime,
        errors: healthy ? [] : ['Session not connected or not ready'],
      };
    } catch (error) {
      return {
        healthy: false,
        status: 'error',
        errors: [error instanceof Error ? error.message : 'Unknown error'],
      };
    }
  }

  public async getSessionStats(sessionId: string): Promise<any> {
    try {
      const session = await dynamoDBService.getSession(sessionId);
      if (!session) {
        throw new Error('Session not found');
      }

      const memorySession = this.sessions.get(sessionId);
      const metadata = session.metadata || {};
      const messageStats = metadata.messageStats || { sent: 0, received: 0 };
      const webhookStats = metadata.webhookStats || { delivered: 0, failed: 0 };

      return {
        sessionId,
        status: session.status,
        ready: memorySession?.ready || false,
        createdAt: session.createdAt,
        lastSeen: session.lastSeen,
        connectionAttempts: session.connectionAttempts || 0,
        messageStats,
        webhookStats,
        uptime: session.lastSeen ? Date.now() - session.createdAt : 0,
      };
    } catch (error) {
      logger.error(`Error getting session stats for ${sessionId}:`, error);
      throw error;
    }
  }

  private formatPhoneNumber(phoneNumber: string): string {
    // Remove any non-digit characters
    const digits = phoneNumber.replace(/\D/g, '');

    // Ensure the number has the country code
    if (digits.startsWith('0')) {
      return `6${digits.substring(1)}@s.whatsapp.net`;
    } else if (!digits.includes('@')) {
      return `${digits}@s.whatsapp.net`;
    }

    return phoneNumber;
  }

  public async healthCheck(): Promise<boolean> {
    try {
      // Check DynamoDB connectivity
      const dbHealthy = await dynamoDBService.healthCheck();
      if (!dbHealthy) {
        return false;
      }

      // Check if service is initialized
      if (!this.initialized) {
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Health check failed:', error);
      return false;
    }
  }

  public async getServiceStats(): Promise<{
    totalSessions: number;
    activeSessions: number;
    connectedSessions: number;
    pendingSessions: number;
    failedSessions: number;
    memoryUsage: number;
  }> {
    try {
      const dbStats = await dynamoDBService.getSessionStats();
      const memorySessions = this.sessions.size;
      const connectedSessions = Array.from(this.sessions.values()).filter(s => s.ready).length;

      return {
        totalSessions: dbStats.total,
        activeSessions: memorySessions,
        connectedSessions,
        pendingSessions: dbStats.byStatus.pending || 0,
        failedSessions: dbStats.byStatus.failed || 0,
        memoryUsage: process.memoryUsage().heapUsed,
      };
    } catch (error) {
      logger.error('Error getting service stats:', error);
      throw error;
    }
  }

  public isInitialized(): boolean {
    return this.initialized;
  }
}

// Create singleton instance
const whatsappService = new WhatsAppService();
export default whatsappService;
