import { Request, Response } from 'express';
import whatsappService from '../services/whatsapp.service';
import { SendMessageRequest } from '../types';

export const sendMessage = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { to, text, media }: SendMessageRequest = req.body;

    if (!to) {
      return res.status(400).json({ success: false, message: 'Recipient number is required' });
    }

    if (!text && !media) {
      return res.status(400).json({ success: false, message: 'Either text or media is required' });
    }

    const session = whatsappService.getSession(id);
    
    if (!session) {
      return res.status(404).json({ success: false, message: 'Session not found' });
    }

    if (!session.ready) {
      return res.status(400).json({ success: false, message: 'Session is not ready' });
    }

    let result;

    // Send text message
    if (text) {
      result = await whatsappService.sendTextMessage(id, to, text);
    }

    // Send media message
    if (media && media.url) {
      result = await whatsappService.sendMediaMessage(
        id, 
        to, 
        media.url, 
        media.caption, 
        media.mimetype
      );
    }

    return res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error sending message:', error);
    return res.status(500).json({ 
      success: false, 
      message: error instanceof Error ? error.message : 'Internal server error' 
    });
  }
};
