import { Request, Response } from 'express';
import whatsappService from '../services/whatsapp.service';
import { SendMessageRequest, BulkMessageRequest, ApiResponse } from '../types';
import { webhookService } from '../utils/webhook';

export const sendMessage = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { to, text, media, document }: SendMessageRequest = req.body;

    if (!to) {
      return res.status(400).json({ success: false, message: 'Recipient number is required' });
    }

    if (!text && !media && !document) {
      return res.status(400).json({ success: false, message: 'Either text, media, or document is required' });
    }

    const session = await whatsappService.getSession(id);
    
    if (!session) {
      return res.status(404).json({ success: false, message: 'Session not found' });
    }

    if (!session.ready) {
      return res.status(400).json({ success: false, message: 'Session is not ready' });
    }

    let result;

    // Send text message
    if (text) {
      result = await whatsappService.sendTextMessage(id, to, text);
    }
    // Send media message
    else if (media && media.url) {
      result = await whatsappService.sendMediaMessage(
        id, 
        to, 
        media.url, 
        media.caption, 
        media.mimetype
      );
    }
    // Send document message
    else if (document && document.url) {
      result = await whatsappService.sendDocumentMessage(
        id,
        to,
        document.url,
        document.filename,
        document.mimetype
      );
    }

    return res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error sending message:', error);
    return res.status(500).json({ 
      success: false, 
      message: error instanceof Error ? error.message : 'Internal server error' 
    });
  }
};

export const sendBulkMessages = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const bulkRequest: BulkMessageRequest = req.body;

    if (!bulkRequest.recipients || bulkRequest.recipients.length === 0) {
      return res.status(400).json({ success: false, message: 'Recipients list is required' });
    }

    if (!bulkRequest.message?.text && !bulkRequest.message?.media) {
      return res.status(400).json({ success: false, message: 'Message content is required' });
    }

    const session = await whatsappService.getSession(id);
    
    if (!session) {
      return res.status(404).json({ success: false, message: 'Session not found' });
    }

    if (!session.ready) {
      return res.status(400).json({ success: false, message: 'Session is not ready' });
    }

    const result = await whatsappService.sendBulkMessages(id, bulkRequest);

    return res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error sending bulk messages:', error);
    return res.status(500).json({ 
      success: false, 
      message: error instanceof Error ? error.message : 'Internal server error' 
    });
  }
};
