import { Request, Response } from 'express';
import whatsappService from '../services/whatsapp.service';
import { SessionInfo, SessionCreateRequest, SessionUpdateRequest } from '../types';
import { webhookService } from '../utils/webhook';
import monitoringService from '../services/monitoring.service';

export const createSession = async (req: Request, res: Response) => {
  try {
    const sessionRequest: SessionCreateRequest = req.body;

    if (!sessionRequest.id) {
      return res.status(400).json({ success: false, message: 'Session ID is required' });
    }

    const session = await whatsappService.createSession(sessionRequest);

    if (!session) {
      return res.status(500).json({ success: false, message: 'Failed to create session' });
    }

    console.log('Session created:', {
      id: session.id,
      name: session.name,
      ready: session.ready,
      hasQR: !!session.qr,
    });

    return res.status(201).json({
      success: true,
      data: {
        id: session.id,
        name: session.name,
        ready: session.ready,
        status: session.status,
        qr: session.qr,
        webhookUrl: session.webhookUrl,
      },
    });
  } catch (error) {
    console.error('Error creating session:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

export const getSession = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const session = await whatsappService.getSession(id);

    if (!session) {
      return res.status(404).json({ success: false, message: 'Session not found' });
    }

    return res.status(200).json({
      success: true,
      data: {
        id: session.id,
        name: session.name,
        ready: session.ready,
        status: session.status,
        qr: session.qr,
        lastSeen: session.lastSeen,
        phoneNumber: session.phoneNumber,
        webhookUrl: session.webhookUrl,
        connectionAttempts: session.connectionAttempts,
      },
    });
  } catch (error) {
    console.error('Error getting session:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

export const getAllSessions = async (req: Request, res: Response) => {
  try {
    const sessions = await whatsappService.getAllSessions();

    return res.status(200).json({
      success: true,
      data: sessions,
    });
  } catch (error) {
    console.error('Error getting all sessions:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

export const deleteSession = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const success = await whatsappService.deleteSession(id);

    if (!success) {
      return res.status(404).json({ success: false, message: 'Session not found or could not be deleted' });
    }

    return res.status(200).json({
      success: true,
      message: 'Session deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting session:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

export const updateSession = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const updates: SessionUpdateRequest = req.body;

    const success = await whatsappService.updateSession(id, updates);

    if (!success) {
      return res.status(404).json({ success: false, message: 'Session not found or could not be updated' });
    }

    return res.status(200).json({
      success: true,
      message: 'Session updated successfully',
    });
  } catch (error) {
    console.error('Error updating session:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

export const getSessionHealth = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const health = await whatsappService.getSessionHealth(id);

    return res.status(200).json({
      success: true,
      data: health,
    });
  } catch (error) {
    console.error('Error getting session health:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

export const getSessionStats = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const stats = await whatsappService.getSessionStats(id);

    return res.status(200).json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error('Error getting session stats:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

export const reconnectSession = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const success = await monitoringService.forceSessionReconnection(id);

    if (!success) {
      return res.status(404).json({ success: false, message: 'Session not found or could not be reconnected' });
    }

    return res.status(200).json({
      success: true,
      message: 'Session reconnection initiated',
    });
  } catch (error) {
    console.error('Error reconnecting session:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

export const updateSessionWebhook = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { webhookUrl } = req.body;

    if (typeof webhookUrl !== 'string') {
      return res.status(400).json({ success: false, message: 'Webhook URL must be a string' });
    }

    await webhookService.updateSessionWebhook(id, webhookUrl);

    return res.status(200).json({
      success: true,
      message: 'Session webhook updated successfully',
    });
  } catch (error) {
    console.error('Error updating session webhook:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

export const testSessionWebhook = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { webhookUrl } = req.body;

    const result = await webhookService.testWebhook(id, webhookUrl);

    return res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error testing session webhook:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};
