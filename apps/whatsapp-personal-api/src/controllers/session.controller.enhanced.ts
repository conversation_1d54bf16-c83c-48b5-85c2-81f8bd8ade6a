import { Request, Response } from 'express';
import enhancedWhatsAppService from '../services/whatsapp.service.enhanced';
import { db } from '../database/dynamodb';

export class EnhancedSessionController {
  /**
   * Create a new WhatsApp session for the authenticated user
   */
  async createSession(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          success: false, 
          message: 'Authentication required' 
        });
      }

      const { name } = req.body;
      const userId = req.user.userId;

      // Check if user can create more sessions
      const canCreate = await db.canUserCreateSession(userId);
      if (!canCreate) {
        return res.status(403).json({
          success: false,
          message: 'Maximum session limit reached'
        });
      }

      // Generate unique session ID
      const sessionId = `session_${userId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      const session = await enhancedWhatsAppService.createSession(sessionId, userId, name);

      if (!session) {
        return res.status(500).json({ 
          success: false, 
          message: 'Failed to create session' 
        });
      }

      return res.status(201).json({
        success: true,
        data: {
          id: session.id,
          name: session.name,
          ready: session.ready,
          qr: session.qr,
          status: session.dbSession.status,
          userId: session.userId,
          createdAt: session.dbSession.createdAt,
        },
      });
    } catch (error) {
      console.error('Error creating session:', error);
      return res.status(500).json({ 
        success: false, 
        message: error instanceof Error ? error.message : 'Internal server error' 
      });
    }
  }

  /**
   * Get a specific session (user can only access their own sessions)
   */
  async getSession(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          success: false, 
          message: 'Authentication required' 
        });
      }

      const { id } = req.params;
      const userId = req.user.userId;

      const session = await enhancedWhatsAppService.getSession(id, userId);

      if (!session) {
        return res.status(404).json({ 
          success: false, 
          message: 'Session not found or access denied' 
        });
      }

      // Get latest status from database
      const sessionStatus = await enhancedWhatsAppService.getSessionStatus(id, userId);

      return res.status(200).json({
        success: true,
        data: {
          id: session.id,
          name: session.name,
          ready: session.ready,
          qr: session.qr,
          lastSeen: session.lastSeen,
          status: sessionStatus?.status,
          phoneNumber: sessionStatus?.phoneNumber,
          createdAt: sessionStatus?.createdAt,
          updatedAt: sessionStatus?.updatedAt,
        },
      });
    } catch (error) {
      console.error('Error getting session:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Internal server error' 
      });
    }
  }

  /**
   * Get all sessions for the authenticated user
   */
  async getUserSessions(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          success: false, 
          message: 'Authentication required' 
        });
      }

      const userId = req.user.userId;

      // Get sessions from both memory and database
      const memorySessions = await enhancedWhatsAppService.getUserSessions(userId);
      const dbSessions = await db.getSessionsByUserId(userId);

      // Combine and format session data
      const combinedSessions = dbSessions.map(dbSession => {
        const memorySession = memorySessions.find(ms => ms.id === dbSession.id);
        return {
          id: dbSession.id,
          name: dbSession.name,
          status: dbSession.status,
          phoneNumber: dbSession.phoneNumber,
          createdAt: dbSession.createdAt,
          updatedAt: dbSession.updatedAt,
          lastSeen: dbSession.lastSeen,
          ready: memorySession?.ready || false,
          qr: memorySession?.qr,
          isActive: dbSession.isActive,
        };
      });

      return res.status(200).json({
        success: true,
        data: combinedSessions,
      });
    } catch (error) {
      console.error('Error getting user sessions:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Internal server error' 
      });
    }
  }

  /**
   * Get all sessions (admin only)
   */
  async getAllSessions(req: Request, res: Response) {
    try {
      // This would need admin role check in a real implementation
      const sessions = enhancedWhatsAppService.getAllSessions();

      const sessionInfos = sessions.map(session => ({
        id: session.id,
        name: session.name,
        ready: session.ready,
        lastSeen: session.lastSeen,
        userId: session.userId,
        status: session.dbSession.status,
        phoneNumber: session.dbSession.phoneNumber,
        createdAt: session.dbSession.createdAt,
      }));

      return res.status(200).json({
        success: true,
        data: sessionInfos,
      });
    } catch (error) {
      console.error('Error getting all sessions:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Internal server error' 
      });
    }
  }

  /**
   * Delete a session (user can only delete their own sessions)
   */
  async deleteSession(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          success: false, 
          message: 'Authentication required' 
        });
      }

      const { id } = req.params;
      const userId = req.user.userId;

      const success = await enhancedWhatsAppService.deleteSession(id, userId);

      if (!success) {
        return res.status(404).json({ 
          success: false, 
          message: 'Session not found or access denied' 
        });
      }

      return res.status(200).json({
        success: true,
        message: 'Session deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting session:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Internal server error' 
      });
    }
  }

  /**
   * Get QR code for a session
   */
  async getQRCode(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          success: false, 
          message: 'Authentication required' 
        });
      }

      const { id } = req.params;
      const userId = req.user.userId;

      const session = await enhancedWhatsAppService.getSession(id, userId);

      if (!session) {
        return res.status(404).json({ 
          success: false, 
          message: 'Session not found or access denied' 
        });
      }

      if (!session.qr) {
        return res.status(404).json({ 
          success: false, 
          message: 'QR code not available. Session may already be connected.' 
        });
      }

      return res.status(200).json({
        success: true,
        data: {
          sessionId: session.id,
          qr: session.qr,
          ready: session.ready,
        },
      });
    } catch (error) {
      console.error('Error getting QR code:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Internal server error' 
      });
    }
  }

  /**
   * Get session statistics for a user
   */
  async getSessionStats(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          success: false, 
          message: 'Authentication required' 
        });
      }

      const userId = req.user.userId;

      // Get user info and sessions
      const user = await db.getUserById(userId);
      const sessions = await db.getSessionsByUserId(userId);

      if (!user) {
        return res.status(404).json({ 
          success: false, 
          message: 'User not found' 
        });
      }

      const stats = {
        totalSessions: sessions.length,
        activeSessions: sessions.filter(s => s.isActive).length,
        connectedSessions: sessions.filter(s => s.status === 'connected').length,
        maxSessions: user.maxSessions,
        canCreateMore: sessions.length < user.maxSessions,
        sessionsByStatus: {
          pending: sessions.filter(s => s.status === 'pending').length,
          connecting: sessions.filter(s => s.status === 'connecting').length,
          connected: sessions.filter(s => s.status === 'connected').length,
          disconnected: sessions.filter(s => s.status === 'disconnected').length,
          expired: sessions.filter(s => s.status === 'expired').length,
        },
      };

      return res.status(200).json({
        success: true,
        data: stats,
      });
    } catch (error) {
      console.error('Error getting session stats:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Internal server error' 
      });
    }
  }
}

export const enhancedSessionController = new EnhancedSessionController();