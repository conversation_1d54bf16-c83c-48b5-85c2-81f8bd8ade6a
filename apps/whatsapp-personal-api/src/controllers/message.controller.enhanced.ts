import { Request, Response } from 'express';
import enhancedWhatsAppService from '../services/whatsapp.service.enhanced';
import { SendMessageRequest } from '../types';

export class EnhancedMessageController {
  /**
   * Send message through user's WhatsApp session
   */
  async sendMessage(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          success: false, 
          message: 'Authentication required' 
        });
      }

      const { id } = req.params;
      const { to, text, media }: SendMessageRequest = req.body;
      const userId = req.user.userId;

      // Validate input
      if (!to) {
        return res.status(400).json({ 
          success: false, 
          message: 'Recipient number is required' 
        });
      }

      if (!text && !media) {
        return res.status(400).json({ 
          success: false, 
          message: 'Either text or media is required' 
        });
      }

      // Validate phone number format
      if (!this.isValidPhoneNumber(to)) {
        return res.status(400).json({ 
          success: false, 
          message: 'Invalid phone number format' 
        });
      }

      // Check if user owns the session
      const session = await enhancedWhatsAppService.getSession(id, userId);
      
      if (!session) {
        return res.status(404).json({ 
          success: false, 
          message: 'Session not found or access denied' 
        });
      }

      if (!session.ready) {
        return res.status(400).json({ 
          success: false, 
          message: 'Session is not ready. Please wait for connection or scan QR code.' 
        });
      }

      let result;

      // Send text message
      if (text) {
        result = await enhancedWhatsAppService.sendTextMessage(id, userId, to, text);
      }

      // Send media message
      if (media && media.url) {
        // Validate media URL
        if (!this.isValidUrl(media.url)) {
          return res.status(400).json({ 
            success: false, 
            message: 'Invalid media URL' 
          });
        }

        result = await enhancedWhatsAppService.sendMediaMessage(
          id, 
          userId,
          to, 
          media.url, 
          media.caption, 
          media.mimetype
        );
      }

      return res.status(200).json({
        success: true,
        data: {
          messageId: result?.key?.id,
          remoteJid: result?.key?.remoteJid,
          timestamp: new Date().toISOString(),
          sessionId: id,
          to,
          text,
          media,
        },
      });
    } catch (error) {
      console.error('Error sending message:', error);
      return res.status(500).json({ 
        success: false, 
        message: error instanceof Error ? error.message : 'Internal server error' 
      });
    }
  }

  /**
   * Send bulk messages to multiple recipients
   */
  async sendBulkMessage(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          success: false, 
          message: 'Authentication required' 
        });
      }

      const { id } = req.params;
      const { recipients, text, media, delay = 1000 } = req.body;
      const userId = req.user.userId;

      // Validate input
      if (!recipients || !Array.isArray(recipients) || recipients.length === 0) {
        return res.status(400).json({ 
          success: false, 
          message: 'Recipients array is required' 
        });
      }

      if (!text && !media) {
        return res.status(400).json({ 
          success: false, 
          message: 'Either text or media is required' 
        });
      }

      // Limit bulk message size
      if (recipients.length > 50) {
        return res.status(400).json({ 
          success: false, 
          message: 'Maximum 50 recipients allowed per bulk message' 
        });
      }

      // Check if user owns the session
      const session = await enhancedWhatsAppService.getSession(id, userId);
      
      if (!session) {
        return res.status(404).json({ 
          success: false, 
          message: 'Session not found or access denied' 
        });
      }

      if (!session.ready) {
        return res.status(400).json({ 
          success: false, 
          message: 'Session is not ready' 
        });
      }

      const results = [];
      const errors = [];

      for (let i = 0; i < recipients.length; i++) {
        const recipient = recipients[i];

        if (!this.isValidPhoneNumber(recipient)) {
          errors.push({
            recipient,
            error: 'Invalid phone number format'
          });
          continue;
        }

        try {
          let result;

          // Send text message
          if (text) {
            result = await enhancedWhatsAppService.sendTextMessage(id, userId, recipient, text);
          }

          // Send media message
          if (media && media.url) {
            result = await enhancedWhatsAppService.sendMediaMessage(
              id, 
              userId,
              recipient, 
              media.url, 
              media.caption, 
              media.mimetype
            );
          }

          results.push({
            recipient,
            messageId: result?.key?.id,
            success: true,
          });

          // Add delay between messages to avoid rate limiting
          if (i < recipients.length - 1) {
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        } catch (error) {
          errors.push({
            recipient,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      return res.status(200).json({
        success: true,
        data: {
          totalRecipients: recipients.length,
          successful: results.length,
          failed: errors.length,
          results,
          errors,
        },
      });
    } catch (error) {
      console.error('Error sending bulk message:', error);
      return res.status(500).json({ 
        success: false, 
        message: error instanceof Error ? error.message : 'Internal server error' 
      });
    }
  }

  /**
   * Get message sending statistics for a session
   */
  async getMessageStats(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          success: false, 
          message: 'Authentication required' 
        });
      }

      const { id } = req.params;
      const userId = req.user.userId;

      // Check if user owns the session
      const session = await enhancedWhatsAppService.getSession(id, userId);
      
      if (!session) {
        return res.status(404).json({ 
          success: false, 
          message: 'Session not found or access denied' 
        });
      }

      // In a real implementation, you'd track message statistics in the database
      // For now, return basic session info
      const stats = {
        sessionId: id,
        sessionName: session.name,
        ready: session.ready,
        lastSeen: session.lastSeen,
        // These would be retrieved from database in a real implementation
        totalMessagesSent: 0,
        messagesLastHour: 0,
        messagesLastDay: 0,
      };

      return res.status(200).json({
        success: true,
        data: stats,
      });
    } catch (error) {
      console.error('Error getting message stats:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Internal server error' 
      });
    }
  }

  /**
   * Validate phone number format
   */
  private isValidPhoneNumber(phoneNumber: string): boolean {
    // Basic phone number validation
    const phoneRegex = /^[\d\s\-\+\(\)]{8,20}$/;
    return phoneRegex.test(phoneNumber);
  }

  /**
   * Validate URL format
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
}

export const enhancedMessageController = new EnhancedMessageController();