import { Request, Response } from 'express';
import monitoringService from '../services/monitoring.service';
import whatsappService from '../services/whatsapp.service';
import dynamoDBService from '../services/dynamodb.service';

export const getSystemHealth = async (req: Request, res: Response) => {
  try {
    const health = await monitoringService.getSystemHealth();

    return res.status(200).json({
      success: true,
      data: health,
    });
  } catch (error) {
    console.error('Error getting system health:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

export const getServiceStats = async (req: Request, res: Response) => {
  try {
    const stats = await whatsappService.getServiceStats();

    return res.status(200).json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error('Error getting service stats:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

export const performCleanup = async (req: Request, res: Response) => {
  try {
    const report = await monitoringService.performCleanup();

    return res.status(200).json({
      success: true,
      data: report,
    });
  } catch (error) {
    console.error('Error performing cleanup:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

export const performHealthCheck = async (req: Request, res: Response) => {
  try {
    const report = await monitoringService.performHealthCheck();

    return res.status(200).json({
      success: true,
      data: report,
    });
  } catch (error) {
    console.error('Error performing health check:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

export const generateReport = async (req: Request, res: Response) => {
  try {
    const report = await monitoringService.generateReport();

    return res.status(200).json({
      success: true,
      data: report,
    });
  } catch (error) {
    console.error('Error generating report:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

export const startMonitoring = async (req: Request, res: Response) => {
  try {
    monitoringService.startMonitoring();

    return res.status(200).json({
      success: true,
      message: 'Monitoring started successfully',
    });
  } catch (error) {
    console.error('Error starting monitoring:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

export const stopMonitoring = async (req: Request, res: Response) => {
  try {
    monitoringService.stopMonitoring();

    return res.status(200).json({
      success: true,
      message: 'Monitoring stopped successfully',
    });
  } catch (error) {
    console.error('Error stopping monitoring:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};