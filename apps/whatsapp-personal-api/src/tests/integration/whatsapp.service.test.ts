import { jest } from '@jest/globals';
import whatsappService from '../../services/whatsapp.service';
import dynamoDBService from '../../services/dynamodb.service';
import { createMockSession, createMockWASocket } from '../setup';

// <PERSON><PERSON>'s WhatsApp library
const mockMakeWASocket = jest.fn();
const mockFetchLatestBaileysVersion = jest.fn();
const mockUseDynamoDBAuthState = jest.fn();

jest.mock('@whiskeysockets/baileys', () => ({
  default: mockMakeWASocket,
  makeWASocket: mockMakeWASocket,
  fetchLatestBaileysVersion: mockFetchLatestBaileysVersion,
  makeCacheableSignalKeyStore: jest.fn(() => ({})),
  isJidUser: jest.fn(() => true),
  DisconnectReason: {
    loggedOut: 'logged_out',
  },
}));

jest.mock('../../services/dynamodb-auth-state', () => ({
  useDynamoDBAuthState: mockUseDynamoDBAuthState,
}));

describe('WhatsApp Service Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mocks
    mockFetchLatestBaileysVersion.mockResolvedValue({
      version: [2, 2323, 4],
    });
    
    mockUseDynamoDBAuthState.mockResolvedValue({
      state: {
        creds: null,
        keys: {},
      },
      saveCreds: jest.fn(),
    });
    
    mockMakeWASocket.mockReturnValue(createMockWASocket());
  });

  describe('Session Lifecycle', () => {
    it('should create a new session successfully', async () => {
      const sessionRequest = {
        id: 'test-session-integration',
        name: 'Test Session',
        webhookUrl: 'https://example.com/webhook',
      };

      // Mock DynamoDB operations
      jest.spyOn(dynamoDBService, 'getSession').mockResolvedValueOnce(null);
      jest.spyOn(dynamoDBService, 'createSession').mockResolvedValueOnce(undefined);

      const session = await whatsappService.createSession(sessionRequest);

      expect(session).toBeTruthy();
      expect(session?.id).toBe(sessionRequest.id);
      expect(session?.name).toBe(sessionRequest.name);
      expect(session?.webhookUrl).toBe(sessionRequest.webhookUrl);
      expect(session?.status).toBe('pending');
      expect(session?.ready).toBe(false);

      // Verify DynamoDB was called
      expect(dynamoDBService.createSession).toHaveBeenCalledWith(
        expect.objectContaining({
          sessionId: sessionRequest.id,
          sessionName: sessionRequest.name,
          status: 'pending',
          webhookUrl: sessionRequest.webhookUrl,
        })
      );

      // Verify WhatsApp client was created
      expect(mockMakeWASocket).toHaveBeenCalledWith(
        expect.objectContaining({
          auth: expect.any(Object),
          printQRInTerminal: true,
          browser: expect.any(Array),
        })
      );
    });

    it('should restore existing session from DynamoDB', async () => {
      const sessionRequest = {
        id: 'existing-session',
        name: 'Existing Session',
      };

      const existingSessionData = createMockSession({
        sessionId: sessionRequest.id,
        sessionName: sessionRequest.name,
        status: 'connected',
      });

      // Mock that session exists in DynamoDB
      jest.spyOn(dynamoDBService, 'getSession').mockResolvedValueOnce(existingSessionData);

      const session = await whatsappService.createSession(sessionRequest);

      expect(session).toBeTruthy();
      expect(session?.id).toBe(sessionRequest.id);

      // Should not create new session if exists
      expect(dynamoDBService.createSession).not.toHaveBeenCalled();
    });

    it('should get session from memory first, then DynamoDB', async () => {
      const sessionId = 'memory-test-session';
      
      // Create a session first
      const sessionRequest = {
        id: sessionId,
        name: 'Memory Test',
      };

      jest.spyOn(dynamoDBService, 'getSession').mockResolvedValue(null);
      jest.spyOn(dynamoDBService, 'createSession').mockResolvedValue(undefined);

      const createdSession = await whatsappService.createSession(sessionRequest);
      expect(createdSession).toBeTruthy();

      // Now get the session - should return from memory
      const retrievedSession = await whatsappService.getSession(sessionId);
      
      expect(retrievedSession).toBeTruthy();
      expect(retrievedSession?.id).toBe(sessionId);
      
      // DynamoDB getSession should not be called since it's in memory
      expect(dynamoDBService.getSession).toHaveBeenCalledTimes(1); // Only during creation
    });

    it('should delete session and cleanup resources', async () => {
      const sessionId = 'delete-test-session';
      
      // Create session first
      const sessionRequest = { id: sessionId, name: 'Delete Test' };
      jest.spyOn(dynamoDBService, 'getSession').mockResolvedValue(null);
      jest.spyOn(dynamoDBService, 'createSession').mockResolvedValue(undefined);
      jest.spyOn(dynamoDBService, 'deleteSession').mockResolvedValue(undefined);

      await whatsappService.createSession(sessionRequest);

      // Delete the session
      const deleted = await whatsappService.deleteSession(sessionId);

      expect(deleted).toBe(true);
      expect(dynamoDBService.deleteSession).toHaveBeenCalledWith(sessionId);
      
      // Session should no longer be in memory
      const session = whatsappService.getSessionFromMemory(sessionId);
      expect(session).toBeNull();
    });

    it('should update session name and webhook URL', async () => {
      const sessionId = 'update-test-session';
      
      // Create session first
      const sessionRequest = { id: sessionId, name: 'Original Name' };
      jest.spyOn(dynamoDBService, 'getSession').mockResolvedValue(null);
      jest.spyOn(dynamoDBService, 'createSession').mockResolvedValue(undefined);
      jest.spyOn(dynamoDBService, 'updateSession').mockResolvedValue(undefined);

      await whatsappService.createSession(sessionRequest);

      // Update the session
      const updates = {
        name: 'Updated Name',
        webhookUrl: 'https://new.webhook.com',
      };

      const updated = await whatsappService.updateSession(sessionId, updates);

      expect(updated).toBe(true);
      expect(dynamoDBService.updateSession).toHaveBeenCalledWith(sessionId, {
        sessionName: updates.name,
        webhookUrl: updates.webhookUrl,
      });

      // Memory session should be updated
      const session = whatsappService.getSessionFromMemory(sessionId);
      expect(session?.name).toBe(updates.name);
      expect(session?.webhookUrl).toBe(updates.webhookUrl);
    });
  });

  describe('Message Sending', () => {
    let sessionId: string;

    beforeEach(async () => {
      sessionId = 'message-test-session';
      const sessionRequest = { id: sessionId, name: 'Message Test' };
      
      jest.spyOn(dynamoDBService, 'getSession').mockResolvedValue(null);
      jest.spyOn(dynamoDBService, 'createSession').mockResolvedValue(undefined);
      jest.spyOn(dynamoDBService, 'updateSession').mockResolvedValue(undefined);

      const session = await whatsappService.createSession(sessionRequest);
      
      // Mark session as ready for message sending
      if (session) {
        session.ready = true;
        session.status = 'connected';
      }
    });

    it('should send text message successfully', async () => {
      const mockSendMessage = jest.fn().mockResolvedValue({
        key: { id: 'message-123' },
      });

      const session = whatsappService.getSessionFromMemory(sessionId);
      if (session) {
        session.client.sendMessage = mockSendMessage;
      }

      const result = await whatsappService.sendTextMessage(
        sessionId,
        '60123456789',
        'Hello World!'
      );

      expect(result).toBeTruthy();
      expect(mockSendMessage).toHaveBeenCalledWith(
        '<EMAIL>',
        { text: 'Hello World!' }
      );
      
      // Should update message stats
      expect(dynamoDBService.updateSession).toHaveBeenCalledWith(
        sessionId,
        expect.objectContaining({
          metadata: expect.objectContaining({
            messageStats: expect.objectContaining({
              sent: 1,
            }),
          }),
        })
      );
    });

    it('should send media message successfully', async () => {
      const mockSendMessage = jest.fn().mockResolvedValue({
        key: { id: 'message-456' },
      });

      const session = whatsappService.getSessionFromMemory(sessionId);
      if (session) {
        session.client.sendMessage = mockSendMessage;
      }

      const result = await whatsappService.sendMediaMessage(
        sessionId,
        '60123456789',
        'https://example.com/image.jpg',
        'Test Caption',
        'image/jpeg'
      );

      expect(result).toBeTruthy();
      expect(mockSendMessage).toHaveBeenCalledWith(
        '<EMAIL>',
        {
          image: { url: 'https://example.com/image.jpg' },
          caption: 'Test Caption',
          mimetype: 'image/jpeg',
        }
      );
    });

    it('should send document message successfully', async () => {
      const mockSendMessage = jest.fn().mockResolvedValue({
        key: { id: 'message-789' },
      });

      const session = whatsappService.getSessionFromMemory(sessionId);
      if (session) {
        session.client.sendMessage = mockSendMessage;
      }

      const result = await whatsappService.sendDocumentMessage(
        sessionId,
        '60123456789',
        'https://example.com/document.pdf',
        'test.pdf',
        'application/pdf'
      );

      expect(result).toBeTruthy();
      expect(mockSendMessage).toHaveBeenCalledWith(
        '<EMAIL>',
        {
          document: { url: 'https://example.com/document.pdf' },
          fileName: 'test.pdf',
          mimetype: 'application/pdf',
        }
      );
    });

    it('should handle bulk message sending', async () => {
      const mockSendMessage = jest.fn()
        .mockResolvedValueOnce({ key: { id: 'msg-1' } })
        .mockResolvedValueOnce({ key: { id: 'msg-2' } })
        .mockRejectedValueOnce(new Error('Send failed'));

      const session = whatsappService.getSessionFromMemory(sessionId);
      if (session) {
        session.client.sendMessage = mockSendMessage;
      }

      const bulkRequest = {
        recipients: ['60111111111', '60222222222', '60333333333'],
        message: {
          text: 'Bulk message test',
        },
        delay: 100, // Short delay for testing
      };

      const result = await whatsappService.sendBulkMessages(sessionId, bulkRequest);

      expect(result.success).toBe(true);
      expect(result.summary.total).toBe(3);
      expect(result.summary.sent).toBe(2);
      expect(result.summary.failed).toBe(1);
      
      expect(result.results).toHaveLength(3);
      expect(result.results[0].success).toBe(true);
      expect(result.results[1].success).toBe(true);
      expect(result.results[2].success).toBe(false);
    });

    it('should throw error when session is not ready', async () => {
      const notReadySessionId = 'not-ready-session';
      const sessionRequest = { id: notReadySessionId, name: 'Not Ready' };
      
      jest.spyOn(dynamoDBService, 'createSession').mockResolvedValue(undefined);
      await whatsappService.createSession(sessionRequest);

      // Session is not marked as ready
      await expect(
        whatsappService.sendTextMessage(notReadySessionId, '60123456789', 'Test')
      ).rejects.toThrow('Session not found or not ready');
    });
  });

  describe('Health and Statistics', () => {
    it('should check session health', async () => {
      const sessionId = 'health-test-session';
      const sessionData = createMockSession({
        sessionId,
        status: 'connected',
        lastSeen: Date.now(),
      });

      jest.spyOn(dynamoDBService, 'getSession').mockResolvedValue(sessionData);

      // Create session in memory and mark as ready
      const sessionRequest = { id: sessionId, name: 'Health Test' };
      jest.spyOn(dynamoDBService, 'createSession').mockResolvedValue(undefined);
      const session = await whatsappService.createSession(sessionRequest);
      if (session) {
        session.ready = true;
        session.status = 'connected';
      }

      const health = await whatsappService.getSessionHealth(sessionId);

      expect(health.healthy).toBe(true);
      expect(health.status).toBe('connected');
      expect(health.lastSeen).toBeDefined();
      expect(health.errors).toHaveLength(0);
    });

    it('should get session statistics', async () => {
      const sessionId = 'stats-test-session';
      const sessionData = createMockSession({
        sessionId,
        status: 'connected',
        createdAt: Date.now() - 3600000, // 1 hour ago
        lastSeen: Date.now(),
        metadata: {
          messageStats: { sent: 10, received: 5 },
          webhookStats: { delivered: 8, failed: 2 },
        },
      });

      jest.spyOn(dynamoDBService, 'getSession').mockResolvedValue(sessionData);

      const stats = await whatsappService.getSessionStats(sessionId);

      expect(stats.sessionId).toBe(sessionId);
      expect(stats.status).toBe('connected');
      expect(stats.messageStats.sent).toBe(10);
      expect(stats.messageStats.received).toBe(5);
      expect(stats.webhookStats.delivered).toBe(8);
      expect(stats.webhookStats.failed).toBe(2);
      expect(stats.uptime).toBeGreaterThan(0);
    });

    it('should get service statistics', async () => {
      jest.spyOn(dynamoDBService, 'getSessionStats').mockResolvedValue({
        total: 5,
        byStatus: {
          connected: 3,
          pending: 1,
          failed: 1,
        },
      });

      const serviceStats = await whatsappService.getServiceStats();

      expect(serviceStats.totalSessions).toBe(5);
      expect(serviceStats.pendingSessions).toBe(1);
      expect(serviceStats.failedSessions).toBe(1);
      expect(serviceStats.memoryUsage).toBeGreaterThan(0);
    });
  });
});