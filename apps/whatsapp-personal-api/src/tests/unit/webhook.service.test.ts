import { jest } from '@jest/globals';
import axios from 'axios';
import { webhookService } from '../../utils/webhook';
import { createMockWebhookPayload } from '../setup';

// Mock axios
const mockAxiosPost = jest.mocked(axios.post);

jest.mock('axios', () => ({
  default: {
    post: jest.fn(),
  },
  post: jest.fn(),
}));

// Mock DynamoDB service
const mockGetSession = jest.fn();
const mockUpdateSession = jest.fn();

jest.mock('../../services/dynamodb.service', () => ({
  default: {
    getSession: mockGetSession,
    updateSession: mockUpdateSession,
  },
}));

describe('Webhook Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset environment variables
    process.env.WEBHOOK_URL = 'https://example.com/webhook';
    process.env.WEBHOOK_SECRET_KEY = 'test-secret';
  });

  describe('sendWebhook', () => {
    it('should send webhook successfully', async () => {
      const payload = createMockWebhookPayload();
      
      mockGetSession.mockResolvedValueOnce({
        sessionId: payload.sessionId,
        webhookUrl: 'https://custom.webhook.com',
      });
      
      mockAxiosPost.mockResolvedValueOnce({
        status: 200,
        statusText: 'OK',
      });

      const result = await webhookService.sendWebhook(payload);

      expect(result.success).toBe(true);
      expect(result.statusCode).toBe(200);
      expect(mockAxiosPost).toHaveBeenCalledWith(
        'https://custom.webhook.com',
        payload,
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'User-Agent': 'WhatsApp-API-Webhook/1.0',
            'X-Webhook-Event': payload.event,
            'X-Webhook-Session': payload.sessionId,
            'X-Webhook-Signature': expect.any(String),
          }),
          timeout: expect.any(Number),
        })
      );
    });

    it('should use global webhook URL if session has no custom URL', async () => {
      const payload = createMockWebhookPayload();
      
      mockGetSession.mockResolvedValueOnce({
        sessionId: payload.sessionId,
        webhookUrl: null,
      });
      
      mockAxiosPost.mockResolvedValueOnce({
        status: 200,
        statusText: 'OK',
      });

      await webhookService.sendWebhook(payload);

      expect(mockAxiosPost).toHaveBeenCalledWith(
        'https://example.com/webhook',
        payload,
        expect.any(Object)
      );
    });

    it('should return failure if no webhook URL is configured', async () => {
      const payload = createMockWebhookPayload();
      
      mockGetSession.mockResolvedValueOnce({
        sessionId: payload.sessionId,
        webhookUrl: null,
      });
      
      // Clear global webhook URL
      delete process.env.WEBHOOK_URL;

      const result = await webhookService.sendWebhook(payload);

      expect(result.success).toBe(false);
      expect(result.error).toBe('No webhook URL configured');
      expect(mockAxiosPost).not.toHaveBeenCalled();
    });

    it('should retry on server errors (5xx)', async () => {
      const payload = createMockWebhookPayload();
      
      mockGetSession.mockResolvedValueOnce({
        sessionId: payload.sessionId,
        webhookUrl: 'https://example.com/webhook',
      });
      
      // First call returns 500, second call succeeds
      mockAxiosPost
        .mockResolvedValueOnce({
          status: 500,
          statusText: 'Internal Server Error',
        })
        .mockResolvedValueOnce({
          status: 200,
          statusText: 'OK',
        });

      const result = await webhookService.sendWebhook(payload);

      expect(result.success).toBe(true);
      expect(result.retryCount).toBe(1);
      expect(mockAxiosPost).toHaveBeenCalledTimes(2);
    });

    it('should not retry on client errors (4xx)', async () => {
      const payload = createMockWebhookPayload();
      
      mockGetSession.mockResolvedValueOnce({
        sessionId: payload.sessionId,
        webhookUrl: 'https://example.com/webhook',
      });
      
      mockAxiosPost.mockResolvedValueOnce({
        status: 400,
        statusText: 'Bad Request',
      });

      const result = await webhookService.sendWebhook(payload);

      expect(result.success).toBe(false);
      expect(result.error).toBe('HTTP 400: Bad Request');
      expect(mockAxiosPost).toHaveBeenCalledTimes(1);
    });

    it('should handle network timeouts', async () => {
      const payload = createMockWebhookPayload();
      
      mockGetSession.mockResolvedValueOnce({
        sessionId: payload.sessionId,
        webhookUrl: 'https://example.com/webhook',
      });
      
      const timeoutError = new Error('timeout of 30000ms exceeded');
      timeoutError.name = 'AxiosError';
      (timeoutError as any).code = 'ECONNABORTED';
      
      mockAxiosPost.mockRejectedValueOnce(timeoutError);

      const result = await webhookService.sendWebhook(payload);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Webhook timeout');
    });

    it('should handle connection refused errors', async () => {
      const payload = createMockWebhookPayload();
      
      mockGetSession.mockResolvedValueOnce({
        sessionId: payload.sessionId,
        webhookUrl: 'https://example.com/webhook',
      });
      
      const connRefusedError = new Error('connect ECONNREFUSED');
      (connRefusedError as any).code = 'ECONNREFUSED';
      
      mockAxiosPost.mockRejectedValueOnce(connRefusedError);

      const result = await webhookService.sendWebhook(payload);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Connection refused');
    });
  });

  describe('sendSessionWebhook', () => {
    it('should send session-specific webhook', async () => {
      const sessionId = 'test-session';
      const event = 'connected';
      const data = { user: { id: '<EMAIL>' } };
      
      mockGetSession.mockResolvedValueOnce({
        sessionId,
        webhookUrl: 'https://custom.webhook.com',
      });
      
      mockAxiosPost.mockResolvedValueOnce({
        status: 200,
        statusText: 'OK',
      });

      const result = await webhookService.sendSessionWebhook(sessionId, event, data);

      expect(result.success).toBe(true);
      expect(mockAxiosPost).toHaveBeenCalledWith(
        'https://custom.webhook.com',
        expect.objectContaining({
          sessionId,
          event,
          data,
          timestamp: expect.any(String),
        }),
        expect.any(Object)
      );
    });

    it('should filter events when event filter is configured', async () => {
      const sessionId = 'test-session';
      const event = 'message';
      const data = { text: 'Hello' };
      
      mockGetSession.mockResolvedValueOnce({
        sessionId,
        webhookUrl: 'https://custom.webhook.com',
      });

      const customConfig = {
        events: ['qr', 'connected'], // message is not in the list
      };

      const result = await webhookService.sendSessionWebhook(
        sessionId,
        event,
        data,
        customConfig
      );

      expect(result.success).toBe(true);
      expect(result.deliveryTime).toBe(0);
      expect(mockAxiosPost).not.toHaveBeenCalled();
    });
  });

  describe('updateSessionWebhook', () => {
    it('should update session webhook URL', async () => {
      const sessionId = 'test-session';
      const webhookUrl = 'https://new.webhook.com';
      
      mockUpdateSession.mockResolvedValueOnce(undefined);

      await webhookService.updateSessionWebhook(sessionId, webhookUrl);

      expect(mockUpdateSession).toHaveBeenCalledWith(sessionId, {
        webhookUrl,
      });
    });

    it('should clear webhook URL when empty string is provided', async () => {
      const sessionId = 'test-session';
      
      mockUpdateSession.mockResolvedValueOnce(undefined);

      await webhookService.updateSessionWebhook(sessionId, '');

      expect(mockUpdateSession).toHaveBeenCalledWith(sessionId, {
        webhookUrl: undefined,
      });
    });
  });

  describe('testWebhook', () => {
    it('should send test webhook successfully', async () => {
      const sessionId = 'test-session';
      const webhookUrl = 'https://test.webhook.com';
      
      mockAxiosPost.mockResolvedValueOnce({
        status: 200,
        statusText: 'OK',
      });

      const result = await webhookService.testWebhook(sessionId, webhookUrl);

      expect(result.success).toBe(true);
      expect(mockAxiosPost).toHaveBeenCalledWith(
        webhookUrl,
        expect.objectContaining({
          sessionId,
          event: 'webhook_test',
          data: expect.objectContaining({
            message: 'This is a test webhook',
          }),
        }),
        expect.objectContaining({
          timeout: 10000, // Shorter timeout for tests
        })
      );
    });

    it('should fail test webhook on invalid URL', async () => {
      const sessionId = 'test-session';
      const webhookUrl = 'https://invalid.webhook.com';
      
      mockAxiosPost.mockRejectedValueOnce(new Error('Network error'));

      const result = await webhookService.testWebhook(sessionId, webhookUrl);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Network error');
    });
  });

  describe('logWebhookEvent', () => {
    it('should log successful webhook event', async () => {
      const sessionId = 'test-session';
      const event = 'message';
      
      mockGetSession.mockResolvedValueOnce({
        sessionId,
        metadata: {
          webhookStats: { delivered: 5, failed: 2 },
        },
      });
      
      mockUpdateSession.mockResolvedValueOnce(undefined);

      await webhookService.logWebhookEvent(sessionId, event, true);

      expect(mockUpdateSession).toHaveBeenCalledWith(sessionId, {
        metadata: expect.objectContaining({
          webhookStats: expect.objectContaining({
            delivered: 6,
            failed: 2,
            lastAttempt: expect.any(Number),
          }),
        }),
      });
    });

    it('should log failed webhook event', async () => {
      const sessionId = 'test-session';
      const event = 'message';
      
      mockGetSession.mockResolvedValueOnce({
        sessionId,
        metadata: {
          webhookStats: { delivered: 5, failed: 2 },
        },
      });
      
      mockUpdateSession.mockResolvedValueOnce(undefined);

      await webhookService.logWebhookEvent(sessionId, event, false);

      expect(mockUpdateSession).toHaveBeenCalledWith(sessionId, {
        metadata: expect.objectContaining({
          webhookStats: expect.objectContaining({
            delivered: 5,
            failed: 3,
            lastAttempt: expect.any(Number),
          }),
        }),
      });
    });

    it('should initialize webhook stats if not present', async () => {
      const sessionId = 'test-session';
      const event = 'message';
      
      mockGetSession.mockResolvedValueOnce({
        sessionId,
        metadata: {},
      });
      
      mockUpdateSession.mockResolvedValueOnce(undefined);

      await webhookService.logWebhookEvent(sessionId, event, true);

      expect(mockUpdateSession).toHaveBeenCalledWith(sessionId, {
        metadata: expect.objectContaining({
          webhookStats: expect.objectContaining({
            delivered: 1,
            failed: 0,
            lastAttempt: expect.any(Number),
          }),
        }),
      });
    });
  });
});