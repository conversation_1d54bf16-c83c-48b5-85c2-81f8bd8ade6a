import { jest } from '@jest/globals';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';
import dynamoDBService, { SessionRecord } from '../../services/dynamodb.service';
import { createMockSession } from '../setup';

// Mock the AWS SDK
const mockSend = jest.fn();
const mockDocClientSend = jest.fn();

jest.mock('@aws-sdk/client-dynamodb', () => ({
  DynamoDBClient: jest.fn().mockImplementation(() => ({
    send: mockSend,
  })),
  CreateTableCommand: jest.fn(),
  DescribeTableCommand: jest.fn(),
  DeleteTableCommand: jest.fn(),
}));

jest.mock('@aws-sdk/lib-dynamodb', () => ({
  DynamoDBDocumentClient: {
    from: jest.fn().mockReturnValue({
      send: mockDocClientSend,
    }),
  },
  PutCommand: jest.fn(),
  GetCommand: jest.fn(),
  UpdateCommand: jest.fn(),
  DeleteCommand: jest.fn(),
  ScanCommand: jest.fn(),
  QueryCommand: jest.fn(),
}));

describe('DynamoDB Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createSession', () => {
    it('should create a new session successfully', async () => {
      const sessionData = createMockSession();
      mockDocClientSend.mockResolvedValueOnce({});

      await dynamoDBService.createSession(sessionData);

      expect(mockDocClientSend).toHaveBeenCalledTimes(1);
      expect(mockDocClientSend).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            TableName: expect.any(String),
            Item: expect.objectContaining({
              sessionId: sessionData.sessionId,
              sessionName: sessionData.sessionName,
              status: sessionData.status,
            }),
            ConditionExpression: 'attribute_not_exists(sessionId)',
          }),
        })
      );
    });

    it('should throw error if session already exists', async () => {
      const sessionData = createMockSession();
      mockDocClientSend.mockRejectedValueOnce(
        new Error('ConditionalCheckFailedException')
      );

      await expect(dynamoDBService.createSession(sessionData)).rejects.toThrow();
    });
  });

  describe('getSession', () => {
    it('should retrieve an existing session', async () => {
      const sessionData = createMockSession();
      mockDocClientSend.mockResolvedValueOnce({
        Item: sessionData,
      });

      const result = await dynamoDBService.getSession(sessionData.sessionId);

      expect(result).toEqual(sessionData);
      expect(mockDocClientSend).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            TableName: expect.any(String),
            Key: { sessionId: sessionData.sessionId },
          }),
        })
      );
    });

    it('should return null if session does not exist', async () => {
      mockDocClientSend.mockResolvedValueOnce({});

      const result = await dynamoDBService.getSession('non-existent-session');

      expect(result).toBeNull();
    });
  });

  describe('updateSession', () => {
    it('should update session successfully', async () => {
      const sessionId = 'test-session';
      const updates = { status: 'connected' as const, phoneNumber: '1234567890' };
      
      mockDocClientSend.mockResolvedValueOnce({});

      await dynamoDBService.updateSession(sessionId, updates);

      expect(mockDocClientSend).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            TableName: expect.any(String),
            Key: { sessionId },
            UpdateExpression: expect.stringContaining('SET'),
            ExpressionAttributeNames: expect.objectContaining({
              '#status': 'status',
              '#phoneNumber': 'phoneNumber',
              '#updatedAt': 'updatedAt',
            }),
            ExpressionAttributeValues: expect.objectContaining({
              ':status': 'connected',
              ':phoneNumber': '1234567890',
              ':updatedAt': expect.any(Number),
            }),
            ConditionExpression: 'attribute_exists(sessionId)',
          }),
        })
      );
    });

    it('should throw error if session does not exist', async () => {
      mockDocClientSend.mockRejectedValueOnce(
        new Error('ConditionalCheckFailedException')
      );

      await expect(
        dynamoDBService.updateSession('non-existent', { status: 'connected' })
      ).rejects.toThrow();
    });
  });

  describe('deleteSession', () => {
    it('should delete session successfully', async () => {
      const sessionId = 'test-session';
      mockDocClientSend.mockResolvedValueOnce({});

      await dynamoDBService.deleteSession(sessionId);

      expect(mockDocClientSend).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            TableName: expect.any(String),
            Key: { sessionId },
            ConditionExpression: 'attribute_exists(sessionId)',
          }),
        })
      );
    });
  });

  describe('getAllSessions', () => {
    it('should return all sessions', async () => {
      const sessions = [
        createMockSession({ sessionId: 'session-1' }),
        createMockSession({ sessionId: 'session-2' }),
      ];

      mockDocClientSend.mockResolvedValueOnce({
        Items: sessions,
        LastEvaluatedKey: undefined,
      });

      const result = await dynamoDBService.getAllSessions();

      expect(result.sessions).toEqual(sessions);
      expect(result.lastEvaluatedKey).toBeUndefined();
    });

    it('should handle pagination', async () => {
      const sessions = [createMockSession()];
      const lastKey = { sessionId: 'last-session' };

      mockDocClientSend.mockResolvedValueOnce({
        Items: sessions,
        LastEvaluatedKey: lastKey,
      });

      const result = await dynamoDBService.getAllSessions({
        limit: 10,
        lastEvaluatedKey: { sessionId: 'start-session' },
      });

      expect(result.sessions).toEqual(sessions);
      expect(result.lastEvaluatedKey).toEqual(lastKey);
      expect(mockDocClientSend).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            Limit: 10,
            ExclusiveStartKey: { sessionId: 'start-session' },
          }),
        })
      );
    });
  });

  describe('getSessionsByStatus', () => {
    it('should return sessions filtered by status', async () => {
      const sessions = [
        createMockSession({ status: 'connected' }),
        createMockSession({ status: 'connected' }),
      ];

      mockDocClientSend.mockResolvedValueOnce({
        Items: sessions,
      });

      const result = await dynamoDBService.getSessionsByStatus('connected');

      expect(result.sessions).toEqual(sessions);
      expect(mockDocClientSend).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            IndexName: 'StatusIndex',
            KeyConditionExpression: '#status = :status',
            ExpressionAttributeNames: {
              '#status': 'status',
            },
            ExpressionAttributeValues: {
              ':status': 'connected',
            },
          }),
        })
      );
    });
  });

  describe('cleanupExpiredSessions', () => {
    it('should cleanup expired sessions', async () => {
      const expiredSessions = [
        createMockSession({ sessionId: 'expired-1' }),
        createMockSession({ sessionId: 'expired-2' }),
      ];

      // Mock scan to return expired sessions
      mockDocClientSend
        .mockResolvedValueOnce({
          Items: expiredSessions,
          LastEvaluatedKey: undefined,
        })
        // Mock delete operations
        .mockResolvedValue({});

      const deletedCount = await dynamoDBService.cleanupExpiredSessions();

      expect(deletedCount).toBe(2);
      expect(mockDocClientSend).toHaveBeenCalledTimes(3); // 1 scan + 2 deletes
    });
  });

  describe('getSessionStats', () => {
    it('should return session statistics', async () => {
      const sessions = [
        createMockSession({ status: 'connected' }),
        createMockSession({ status: 'connected' }),
        createMockSession({ status: 'pending' }),
        createMockSession({ status: 'failed' }),
      ];

      mockDocClientSend.mockResolvedValueOnce({
        Items: sessions,
      });

      const stats = await dynamoDBService.getSessionStats();

      expect(stats).toEqual({
        total: 4,
        byStatus: {
          connected: 2,
          pending: 1,
          failed: 1,
        },
      });
    });
  });

  describe('healthCheck', () => {
    it('should return true when DynamoDB is accessible', async () => {
      mockSend.mockResolvedValueOnce({
        Table: {
          TableName: 'test-table',
          TableStatus: 'ACTIVE',
        },
      });

      const result = await dynamoDBService.healthCheck();

      expect(result).toBe(true);
    });

    it('should return false when DynamoDB is not accessible', async () => {
      mockSend.mockRejectedValueOnce(new Error('DynamoDB error'));

      const result = await dynamoDBService.healthCheck();

      expect(result).toBe(false);
    });
  });
});