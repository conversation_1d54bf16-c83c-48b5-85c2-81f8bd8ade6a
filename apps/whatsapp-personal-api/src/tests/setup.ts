import { jest } from '@jest/globals';

// Mock AWS SDK clients
jest.mock('@aws-sdk/client-dynamodb', () => ({
  DynamoDBClient: jest.fn().mockImplementation(() => ({
    send: jest.fn().mockResolvedValue({}),
  })),
  CreateTableCommand: jest.fn(),
  DescribeTableCommand: jest.fn(),
  DeleteTableCommand: jest.fn(),
}));

jest.mock('@aws-sdk/lib-dynamodb', () => ({
  DynamoDBDocumentClient: {
    from: jest.fn().mockReturnValue({
      send: jest.fn().mockResolvedValue({}),
    }),
  },
  PutCommand: jest.fn(),
  GetCommand: jest.fn(),
  UpdateCommand: jest.fn(),
  DeleteCommand: jest.fn(),
  ScanCommand: jest.fn(),
  QueryCommand: jest.fn(),
}));

// <PERSON><PERSON>'s WhatsApp library
jest.mock('@whiskeysockets/baileys', () => ({
  default: jest.fn(),
  makeWASocket: jest.fn(),
  useMultiFileAuthState: jest.fn(),
  fetchLatestBaileysVersion: jest.fn(),
  makeCacheableSignalKeyStore: jest.fn(),
  isJidUser: jest.fn(),
  DisconnectReason: {
    loggedOut: 'logged_out',
  },
}));

// Mock file system operations
jest.mock('fs', () => ({
  existsSync: jest.fn(),
  mkdirSync: jest.fn(),
  rmSync: jest.fn(),
  readFileSync: jest.fn(),
  writeFileSync: jest.fn(),
}));

// Mock axios for webhook testing
jest.mock('axios', () => ({
  default: {
    post: jest.fn(),
    get: jest.fn(),
  },
  post: jest.fn(),
  get: jest.fn(),
}));

// Mock QR code generation
jest.mock('qrcode-terminal', () => ({
  generate: jest.fn(),
}));

// Mock crypto for webhook signatures
jest.mock('crypto', () => ({
  createHmac: jest.fn().mockReturnValue({
    update: jest.fn().mockReturnValue({
      digest: jest.fn().mockReturnValue('mocked-signature'),
    }),
  }),
}));

// Set up test environment variables
process.env.NODE_ENV = 'test';
process.env.AWS_REGION = 'us-east-1';
process.env.DYNAMODB_TABLE_NAME = 'test-whatsapp-sessions';
process.env.LOG_LEVEL = 'error'; // Reduce log noise in tests

// Global test timeout
jest.setTimeout(30000);

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
});

// Helper function to create mock session data
export const createMockSession = (overrides = {}) => ({
  sessionId: 'test-session-1',
  sessionName: 'Test Session',
  status: 'pending' as const,
  createdAt: Date.now(),
  updatedAt: Date.now(),
  expiresAt: Date.now() + 24 * 60 * 60 * 1000,
  ...overrides,
});

// Helper function to create mock WhatsApp client
export const createMockWASocket = () => ({
  ev: {
    on: jest.fn(),
  },
  sendMessage: jest.fn(),
  end: jest.fn(),
  user: {
    id: '<EMAIL>',
    name: 'Test User',
  },
});

// Helper function to create mock webhook payload
export const createMockWebhookPayload = (overrides = {}) => ({
  sessionId: 'test-session-1',
  event: 'test-event',
  data: { message: 'test' },
  timestamp: new Date().toISOString(),
  ...overrides,
});

export default {};