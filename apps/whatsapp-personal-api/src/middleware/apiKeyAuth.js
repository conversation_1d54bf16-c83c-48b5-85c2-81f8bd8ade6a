function createApiKeyAuth(getAllowedApiKey) {
  return async function apiKeyAuth(req, res, next) {
    try {
      const apiKey = req.header('x-api-key');
      if (!apiKey) {
        return res.status(401).json({ success: false, message: 'Unauthorized: API key required' });
      }
      const allowedApiKey = await getAllowedApiKey();
      if (!allowedApiKey || apiKey.trim() !== allowedApiKey.trim()) {
        return res.status(401).json({ success: false, message: 'Unauthorized: Invalid API key' });
      }
      next();
    } catch (err) {
      // Log error but do not leak sensitive data
      console.error('API key validation error:', err.message);
      return res.status(500).json({ success: false, message: 'Internal server error' });
    }
  };
}

module.exports = createApiKeyAuth; 