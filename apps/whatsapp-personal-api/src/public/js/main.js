document.addEventListener('DOMContentLoaded', () => {
  console.log("DOM loaded");

  // DOM elements
  const createSessionForm = document.getElementById('createSessionForm');
  const sessionIdInput = document.getElementById('sessionId');
  const sessionNameInput = document.getElementById('sessionName');
  const qrContainer = document.getElementById('qrContainer');
  const qrCodeElement = document.getElementById('qrCode');
  const sessionList = document.getElementById('sessionList');
  const sessionTemplate = document.getElementById('sessionTemplate');

  console.log("DOM elements:", {
    createSessionForm: !!createSessionForm,
    sessionIdInput: !!sessionIdInput,
    sessionNameInput: !!sessionNameInput,
    qrContainer: !!qrContainer,
    qrCodeElement: !!qrCodeElement,
    sessionList: !!sessionList,
    sessionTemplate: !!sessionTemplate
  });

  // API base URL
  const API_BASE_URL = '/api';

  // Load all sessions on page load
  loadSessions();

  // Set up event listeners
  createSessionForm.addEventListener('submit', createSession);
  sessionList.addEventListener('click', handleSessionAction);

  // Poll for session updates every 5 seconds
  setInterval(loadSessions, 5000);

  // Function to poll for QR code
  function pollForQRCode(sessionId) {
    console.log(`Polling for QR code for session ${sessionId}`);

    // Show the QR container
    qrContainer.classList.remove('d-none');

    // Set up polling to check for QR code
    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`);
        const data = await response.json();

        if (response.ok && data.data.qr) {
          // QR code is available
          console.log(`QR code received for session ${sessionId}`);
          clearInterval(pollInterval);

          // Clear any existing QR code
          qrCodeElement.innerHTML = '';

          // Show the QR code
          showQRCode(data.data.qr, sessionId);
        }
      } catch (error) {
        console.error('Error polling for QR code:', error);
      }
    }, 2000); // Poll every 2 seconds

    // Stop polling after 1 minute
    setTimeout(() => {
      clearInterval(pollInterval);
    }, 60000);
  }

  // Create a new WhatsApp session
  async function createSession(event) {
    event.preventDefault();

    const sessionId = sessionIdInput.value.trim();
    const sessionName = sessionNameInput.value.trim();

    if (!sessionId) {
      alert('Please enter a session ID');
      return;
    }

    try {
      const response = await fetch(`${API_BASE_URL}/sessions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: sessionId,
          name: sessionName || sessionId,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to create session');
      }

      // Clear form
      sessionIdInput.value = '';
      sessionNameInput.value = '';

      // Show QR code if available
      console.log("Session created response:", data);
      if (data.data.qr) {
        console.log("QR code available, showing QR code");
        // Clear any existing QR code first
        qrCodeElement.innerHTML = '';
        showQRCode(data.data.qr, data.data.id);
      } else {
        console.log("No QR code in response");
        // Try to get the QR code by polling the session
        pollForQRCode(data.data.id);
      }

      // Reload sessions
      loadSessions();

    } catch (error) {
      alert(`Error: ${error.message}`);
    }
  }

  // Load all active sessions
  async function loadSessions() {
    try {
      const response = await fetch(`${API_BASE_URL}/sessions`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to load sessions');
      }

      renderSessions(data.data);

    } catch (error) {
      console.error('Error loading sessions:', error);
    }
  }

  // Render the list of sessions
  function renderSessions(sessions) {
    // Clear current list
    sessionList.innerHTML = '';

    if (!sessions || sessions.length === 0) {
      sessionList.innerHTML = `
        <div class="text-center py-4 text-muted">
          <p>No active sessions</p>
        </div>
      `;
      return;
    }

    // Add each session to the list
    sessions.forEach(session => {
      const sessionElement = sessionTemplate.content.cloneNode(true);

      // Set session details
      sessionElement.querySelector('.session-name').textContent = session.name || session.id;
      sessionElement.querySelector('.session-id').textContent = `ID: ${session.id}`;

      // Set status badge
      const statusBadge = sessionElement.querySelector('.status-badge');
      if (session.ready) {
        statusBadge.textContent = 'Connected';
        statusBadge.classList.add('bg-success');
      } else {
        statusBadge.textContent = 'Pending';
        statusBadge.classList.add('bg-warning', 'text-dark');
      }

      // Set data attributes for actions
      const sessionItem = sessionElement.querySelector('.session-item');
      sessionItem.dataset.sessionId = session.id;

      sessionList.appendChild(sessionElement);
    });
  }

  // Handle session actions (delete)
  async function handleSessionAction(event) {
    const deleteBtn = event.target.closest('.delete-btn');

    if (deleteBtn) {
      const sessionItem = deleteBtn.closest('.session-item');
      const sessionId = sessionItem.dataset.sessionId;

      if (confirm(`Are you sure you want to delete session ${sessionId}?`)) {
        try {
          const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`, {
            method: 'DELETE',
          });

          if (!response.ok) {
            const data = await response.json();
            throw new Error(data.message || 'Failed to delete session');
          }

          // Remove from UI
          sessionItem.remove();

          // Reload sessions
          loadSessions();

        } catch (error) {
          alert(`Error: ${error.message}`);
        }
      }
    }
  }

  // Show QR code for scanning
  function showQRCode(qrData, sessionId) {
    console.log("Showing QR code for session:", sessionId);
    console.log("QR data:", qrData);

    // Show QR container
    qrContainer.classList.remove('d-none');

    // Clear previous QR code
    qrCodeElement.innerHTML = '';

    // For WhatsApp QR codes, we need to handle them specially
    if (qrData.startsWith('2@')) {
      console.log("Detected WhatsApp QR code format");

      // For WhatsApp QR codes, we need to use a special format
      // The QR code data from Baileys starts with "2@" and contains the connection data
      try {
        // Create a new QRCode instance with the WhatsApp data
        new QRCode(qrCodeElement, {
          text: qrData,
          width: 200,
          height: 200,
          colorDark: '#122e31',
          colorLight: '#ffffff',
          correctLevel: QRCode.CorrectLevel.H
        });
        console.log("WhatsApp QR code generated successfully");
      } catch (error) {
        console.error('Error generating WhatsApp QR code:', error);

        // Fallback to external service
        const img = document.createElement('img');
        img.src = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' + encodeURIComponent(qrData);
        img.alt = 'WhatsApp QR Code';
        img.width = 200;
        img.height = 200;
        qrCodeElement.appendChild(img);
        console.log("Using external QR code service as fallback");
      }
    } else {
      // For regular QR codes, use the external service
      const img = document.createElement('img');
      img.src = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' + encodeURIComponent(qrData);
      img.alt = 'WhatsApp QR Code';
      img.width = 200;
      img.height = 200;
      qrCodeElement.appendChild(img);
      console.log("Using external QR code service for non-WhatsApp QR code");
    }

    // No backup QR code needed

    // Set up polling to check session status
    const checkInterval = setInterval(async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`);
        const data = await response.json();

        if (response.ok && data.data.ready) {
          // Session is ready, hide QR code
          qrContainer.classList.add('d-none');
          clearInterval(checkInterval);

          // Reload sessions
          loadSessions();
        }
      } catch (error) {
        console.error('Error checking session status:', error);
      }
    }, 3000);

    // Stop checking after 2 minutes
    setTimeout(() => {
      clearInterval(checkInterval);
    }, 120000);
  }
});
