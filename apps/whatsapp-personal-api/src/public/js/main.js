document.addEventListener('DOMContentLoaded', () => {
  console.log("DOM loaded");

  // API base URL
  const API_BASE_URL = '/api';

  // Global state
  let currentSessions = [];
  let qrCheckInterval = null;
  let sessionCheckInterval = null;

  // Initialize the application
  initializeApp();

  function initializeApp() {
    // Set up tab navigation
    setupTabNavigation();
    
    // Set up form event listeners
    setupFormEventListeners();
    
    // Load initial data
    loadSessions();
    loadDashboardStats();
    
    // Set up periodic updates
    setInterval(loadSessions, 10000); // Every 10 seconds
    setInterval(loadDashboardStats, 30000); // Every 30 seconds
  }

  // Tab Navigation
  function setupTabNavigation() {
    const tabLinks = document.querySelectorAll('[data-tab]');
    const tabContents = document.querySelectorAll('.tab-content');

    tabLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        
        // Remove active class from all links
        tabLinks.forEach(l => l.classList.remove('active'));
        
        // Hide all tab contents
        tabContents.forEach(content => content.style.display = 'none');
        
        // Add active class to clicked link
        link.classList.add('active');
        
        // Show corresponding tab content
        const tabName = link.getAttribute('data-tab');
        const tabContent = document.getElementById(`${tabName}-content`);
        if (tabContent) {
          tabContent.style.display = 'block';
          
          // Load tab-specific data
          if (tabName === 'dashboard') {
            loadDashboardStats();
          } else if (tabName === 'messaging') {
            updateSessionDropdowns();
          } else if (tabName === 'monitoring') {
            loadSystemHealth();
          }
        }
      });
    });
  }

  // Form Event Listeners
  function setupFormEventListeners() {
    // Create session form
    const createSessionForm = document.getElementById('createSessionForm');
    if (createSessionForm) {
      createSessionForm.addEventListener('submit', createSession);
    }

    // Send message form
    const sendMessageForm = document.getElementById('sendMessageForm');
    if (sendMessageForm) {
      sendMessageForm.addEventListener('submit', sendMessage);
    }

    // Bulk message form
    const bulkMessageForm = document.getElementById('bulkMessageForm');
    if (bulkMessageForm) {
      bulkMessageForm.addEventListener('submit', sendBulkMessages);
    }

    // Webhook form
    const webhookForm = document.getElementById('webhookForm');
    if (webhookForm) {
      webhookForm.addEventListener('submit', updateWebhook);
    }

    // Session list click handler
    const sessionList = document.getElementById('sessionList');
    if (sessionList) {
      sessionList.addEventListener('click', handleSessionAction);
    }
  }

  // Dashboard Functions
  async function loadDashboardStats() {
    try {
      const response = await fetch(`${API_BASE_URL}/monitoring/stats`);
      if (!response.ok) throw new Error('Failed to load stats');
      
      const data = await response.json();
      updateDashboardDisplay(data.data);
    } catch (error) {
      console.error('Error loading dashboard stats:', error);
    }
  }

  function updateDashboardDisplay(stats) {
    document.getElementById('totalSessions').textContent = stats.totalSessions || 0;
    document.getElementById('connectedSessions').textContent = stats.connectedSessions || 0;
    document.getElementById('pendingSessions').textContent = stats.pendingSessions || 0;
    document.getElementById('failedSessions').textContent = stats.failedSessions || 0;
  }

  // Session Management Functions
  async function createSession(event) {
    event.preventDefault();

    const sessionId = document.getElementById('sessionId').value.trim();
    const sessionName = document.getElementById('sessionName').value.trim();
    const webhookUrl = document.getElementById('webhookUrl').value.trim();

    if (!sessionId) {
      alert('Please enter a session ID');
      return;
    }

    try {
      const response = await fetch(`${API_BASE_URL}/sessions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: sessionId,
          name: sessionName || sessionId,
          webhookUrl: webhookUrl || undefined,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to create session');
      }

      // Clear form
      document.getElementById('sessionId').value = '';
      document.getElementById('sessionName').value = '';
      document.getElementById('webhookUrl').value = '';

      // Handle QR code
      if (data.data.qr) {
        showQRCode(data.data.qr, data.data.id);
      } else {
        pollForQRCode(data.data.id);
      }

      // Reload data
      loadSessions();
      loadDashboardStats();

    } catch (error) {
      alert(`Error: ${error.message}`);
    }
  }

  async function loadSessions() {
    try {
      const response = await fetch(`${API_BASE_URL}/sessions`);
      if (!response.ok) throw new Error('Failed to load sessions');
      
      const data = await response.json();
      currentSessions = data.data || [];
      renderSessions(currentSessions);
      updateSessionDropdowns();
    } catch (error) {
      console.error('Error loading sessions:', error);
    }
  }

  function renderSessions(sessions) {
    const sessionList = document.getElementById('sessionList');
    if (!sessionList) return;

    sessionList.innerHTML = '';

    if (!sessions || sessions.length === 0) {
      sessionList.innerHTML = `
        <div class="text-center py-4 text-muted">
          <p>No active sessions</p>
        </div>
      `;
      return;
    }

    sessions.forEach(session => {
      const sessionElement = createSessionElement(session);
      sessionList.appendChild(sessionElement);
    });
  }

  function createSessionElement(session) {
    const template = document.getElementById('sessionTemplate');
    const sessionElement = template.content.cloneNode(true);

    // Basic info
    sessionElement.querySelector('.session-name').textContent = session.name || session.id;
    sessionElement.querySelector('.session-id').textContent = `ID: ${session.id}`;

    // Status and health indicator
    const statusBadge = sessionElement.querySelector('.status-badge');
    const healthIndicator = sessionElement.querySelector('.health-indicator');
    const sessionCard = sessionElement.querySelector('.session-card');

    let status = 'disconnected';
    let statusText = 'Disconnected';
    let statusClass = 'bg-secondary';
    let healthClass = 'health-unknown';

    if (session.ready) {
      status = 'connected';
      statusText = 'Connected';
      statusClass = 'bg-success';
      healthClass = 'health-healthy';
    } else if (session.qr) {
      status = 'pending';
      statusText = 'Pending';
      statusClass = 'bg-warning text-dark';
      healthClass = 'health-unknown';
    } else if (session.error) {
      status = 'failed';
      statusText = 'Failed';
      statusClass = 'bg-danger';
      healthClass = 'health-unhealthy';
    }

    statusBadge.textContent = statusText;
    statusBadge.className = `badge rounded-pill status-badge ${statusClass}`;
    healthIndicator.className = `health-indicator ${healthClass}`;
    sessionCard.classList.add(status);

    // Session stats
    const phoneNumber = sessionElement.querySelector('.phone-number');
    const lastSeen = sessionElement.querySelector('.last-seen');
    
    if (session.phoneNumber) {
      phoneNumber.textContent = `Phone: ${session.phoneNumber}`;
    }
    if (session.lastSeen) {
      lastSeen.textContent = `Last seen: ${new Date(session.lastSeen).toLocaleString()}`;
    }

    // Set data attributes
    const sessionItem = sessionElement.querySelector('.session-item');
    sessionItem.dataset.sessionId = session.id;

    return sessionElement;
  }

  async function handleSessionAction(event) {
    const sessionItem = event.target.closest('.session-item');
    if (!sessionItem) return;

    const sessionId = sessionItem.dataset.sessionId;

    if (event.target.closest('.delete-btn')) {
      await deleteSession(sessionId);
    } else if (event.target.closest('.health-btn')) {
      await checkSessionHealth(sessionId);
    } else if (event.target.closest('.stats-btn')) {
      await showSessionStats(sessionId);
    } else if (event.target.closest('.reconnect-btn')) {
      await reconnectSession(sessionId);
    } else if (event.target.closest('.webhook-btn')) {
      await showWebhookModal(sessionId);
    }
  }

  async function deleteSession(sessionId) {
    if (!confirm(`Are you sure you want to delete session ${sessionId}?`)) return;

    try {
      const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.message || 'Failed to delete session');
      }

      loadSessions();
      loadDashboardStats();
    } catch (error) {
      alert(`Error: ${error.message}`);
    }
  }

  async function checkSessionHealth(sessionId) {
    try {
      const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}/health`);
      const data = await response.json();
      
      if (response.ok) {
        alert(`Session Health: ${data.data.status}\nUptime: ${data.data.uptime || 'Unknown'}`);
      } else {
        throw new Error(data.message || 'Health check failed');
      }
    } catch (error) {
      alert(`Error: ${error.message}`);
    }
  }

  async function showSessionStats(sessionId) {
    try {
      const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}/stats`);
      const data = await response.json();
      
      if (response.ok) {
        const stats = data.data;
        const modal = new bootstrap.Modal(document.getElementById('sessionStatsModal'));
        const content = document.getElementById('sessionStatsContent');
        
        content.innerHTML = `
          <div class="row">
            <div class="col-md-6">
              <h6>Session Info</h6>
              <p><strong>ID:</strong> ${stats.sessionId}</p>
              <p><strong>Status:</strong> ${stats.status}</p>
              <p><strong>Created:</strong> ${new Date(stats.createdAt).toLocaleString()}</p>
              <p><strong>Updated:</strong> ${new Date(stats.updatedAt).toLocaleString()}</p>
            </div>
            <div class="col-md-6">
              <h6>Message Statistics</h6>
              <p><strong>Messages Sent:</strong> ${stats.messagesSent || 0}</p>
              <p><strong>Messages Received:</strong> ${stats.messagesReceived || 0}</p>
              <p><strong>Last Activity:</strong> ${stats.lastActivity ? new Date(stats.lastActivity).toLocaleString() : 'None'}</p>
            </div>
          </div>
        `;
        
        modal.show();
      } else {
        throw new Error(data.message || 'Failed to load stats');
      }
    } catch (error) {
      alert(`Error: ${error.message}`);
    }
  }

  async function reconnectSession(sessionId) {
    try {
      const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}/reconnect`, {
        method: 'POST',
      });

      const data = await response.json();
      
      if (response.ok) {
        alert('Reconnection initiated');
        if (data.data.qr) {
          showQRCode(data.data.qr, sessionId);
        }
        loadSessions();
      } else {
        throw new Error(data.message || 'Reconnection failed');
      }
    } catch (error) {
      alert(`Error: ${error.message}`);
    }
  }

  async function showWebhookModal(sessionId) {
    const session = currentSessions.find(s => s.id === sessionId);
    const modal = new bootstrap.Modal(document.getElementById('webhookModal'));
    
    document.getElementById('webhookSessionId').value = sessionId;
    document.getElementById('modalWebhookUrl').value = session?.webhookUrl || '';
    
    modal.show();
  }

  async function updateWebhook(event) {
    event.preventDefault();
    
    const sessionId = document.getElementById('webhookSessionId').value;
    const webhookUrl = document.getElementById('modalWebhookUrl').value.trim();

    try {
      const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}/webhook`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ webhookUrl: webhookUrl || null }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.message || 'Failed to update webhook');
      }

      bootstrap.Modal.getInstance(document.getElementById('webhookModal')).hide();
      alert('Webhook updated successfully');
      loadSessions();
    } catch (error) {
      alert(`Error: ${error.message}`);
    }
  }

  // Messaging Functions
  function updateSessionDropdowns() {
    const dropdowns = ['messageSessionId', 'bulkSessionId'];
    
    dropdowns.forEach(dropdownId => {
      const dropdown = document.getElementById(dropdownId);
      if (!dropdown) return;

      dropdown.innerHTML = '<option value="">Select a session</option>';
      
      currentSessions.forEach(session => {
        if (session.ready) {
          const option = document.createElement('option');
          option.value = session.id;
          option.textContent = `${session.name || session.id} (${session.phoneNumber || 'Unknown'})`;
          dropdown.appendChild(option);
        }
      });
    });
  }

  async function sendMessage(event) {
    event.preventDefault();

    const sessionId = document.getElementById('messageSessionId').value;
    const recipientNumber = document.getElementById('recipientNumber').value.trim();
    const messageText = document.getElementById('messageText').value.trim();

    if (!sessionId || !recipientNumber || !messageText) {
      alert('Please fill in all fields');
      return;
    }

    try {
      const response = await fetch(`${API_BASE_URL}/messages/whatsapp-api/${sessionId}/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          number: recipientNumber,
          message: messageText,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to send message');
      }

      alert('Message sent successfully');
      
      // Clear form
      document.getElementById('recipientNumber').value = '';
      document.getElementById('messageText').value = '';
    } catch (error) {
      alert(`Error: ${error.message}`);
    }
  }

  async function sendBulkMessages(event) {
    event.preventDefault();

    const sessionId = document.getElementById('bulkSessionId').value;
    const recipients = document.getElementById('bulkRecipients').value.trim();
    const message = document.getElementById('bulkMessage').value.trim();
    const delay = parseInt(document.getElementById('bulkDelay').value) || 1000;

    if (!sessionId || !recipients || !message) {
      alert('Please fill in all fields');
      return;
    }

    const recipientList = recipients.split('\n').filter(line => line.trim());
    
    if (recipientList.length === 0) {
      alert('Please enter at least one recipient');
      return;
    }

    if (!confirm(`Send message to ${recipientList.length} recipients?`)) return;

    try {
      const response = await fetch(`${API_BASE_URL}/messages/whatsapp-api/${sessionId}/send-bulk`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          recipients: recipientList,
          message: message,
          delay: delay,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to send bulk messages');
      }

      alert(`Bulk messages initiated for ${recipientList.length} recipients`);
      
      // Clear form
      document.getElementById('bulkRecipients').value = '';
      document.getElementById('bulkMessage').value = '';
    } catch (error) {
      alert(`Error: ${error.message}`);
    }
  }

  // Monitoring Functions
  async function loadSystemHealth() {
    try {
      const response = await fetch(`${API_BASE_URL}/monitoring/health`);
      const data = await response.json();
      
      const healthDiv = document.getElementById('systemHealth');
      if (healthDiv && response.ok) {
        const health = data.data;
        healthDiv.innerHTML = `
          <p><strong>Status:</strong> <span class="badge ${health.status === 'healthy' ? 'bg-success' : 'bg-danger'}">${health.status}</span></p>
          <p><strong>Database:</strong> <span class="badge ${health.database ? 'bg-success' : 'bg-danger'}">${health.database ? 'Connected' : 'Disconnected'}</span></p>
          <p><strong>Active Sessions:</strong> ${health.activeSessions}</p>
          <p><strong>Uptime:</strong> ${health.uptime}</p>
        `;
      }
    } catch (error) {
      console.error('Error loading system health:', error);
    }
  }

  // QR Code Functions
  function pollForQRCode(sessionId) {
    console.log(`Polling for QR code for session ${sessionId}`);

    const qrContainer = document.getElementById('qrContainer');
    qrContainer.style.display = 'block';

    qrCheckInterval = setInterval(async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`);
        const data = await response.json();

        if (response.ok && data.data.qr) {
          clearInterval(qrCheckInterval);
          showQRCode(data.data.qr, sessionId);
        }
      } catch (error) {
        console.error('Error polling for QR code:', error);
      }
    }, 2000);

    setTimeout(() => {
      if (qrCheckInterval) {
        clearInterval(qrCheckInterval);
      }
    }, 60000);
  }

  function showQRCode(qrData, sessionId) {
    console.log("Showing QR code for session:", sessionId);

    const qrContainer = document.getElementById('qrContainer');
    const qrCodeElement = document.getElementById('qrCode');
    
    qrContainer.style.display = 'block';
    qrCodeElement.innerHTML = '';

    try {
      // Try to use the QRCode library if available
      if (typeof QRCode !== 'undefined') {
        new QRCode(qrCodeElement, {
          text: qrData,
          width: 200,
          height: 200,
          colorDark: '#122e31',
          colorLight: '#ffffff',
          correctLevel: QRCode.CorrectLevel.H
        });
      } else {
        // Fallback to external service
        const img = document.createElement('img');
        img.src = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' + encodeURIComponent(qrData);
        img.alt = 'WhatsApp QR Code';
        img.width = 200;
        img.height = 200;
        qrCodeElement.appendChild(img);
      }
    } catch (error) {
      console.error('Error generating QR code:', error);
      
      // Fallback to external service
      const img = document.createElement('img');
      img.src = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' + encodeURIComponent(qrData);
      img.alt = 'WhatsApp QR Code';
      img.width = 200;
      img.height = 200;
      qrCodeElement.appendChild(img);
    }

    // Check session status periodically
    sessionCheckInterval = setInterval(async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`);
        const data = await response.json();

        if (response.ok && data.data.ready) {
          qrContainer.style.display = 'none';
          clearInterval(sessionCheckInterval);
          loadSessions();
        }
      } catch (error) {
        console.error('Error checking session status:', error);
      }
    }, 3000);

    setTimeout(() => {
      if (sessionCheckInterval) {
        clearInterval(sessionCheckInterval);
      }
    }, 120000);
  }

  // Global functions for button handlers
  window.refreshSessions = loadSessions;
  
  window.performHealthCheck = async function() {
    try {
      const response = await fetch(`${API_BASE_URL}/monitoring/health-check`, {
        method: 'POST',
      });
      
      if (response.ok) {
        alert('Health check completed');
        loadSystemHealth();
      } else {
        throw new Error('Health check failed');
      }
    } catch (error) {
      alert(`Error: ${error.message}`);
    }
  };

  window.performCleanup = async function() {
    if (!confirm('Perform system cleanup? This will remove inactive sessions.')) return;
    
    try {
      const response = await fetch(`${API_BASE_URL}/monitoring/cleanup`, {
        method: 'POST',
      });
      
      const data = await response.json();
      
      if (response.ok) {
        alert(`Cleanup completed. Removed ${data.data.cleaned} items.`);
        loadSessions();
        loadDashboardStats();
      } else {
        throw new Error(data.message || 'Cleanup failed');
      }
    } catch (error) {
      alert(`Error: ${error.message}`);
    }
  };

  window.generateReport = async function() {
    try {
      const response = await fetch(`${API_BASE_URL}/monitoring/report`);
      const data = await response.json();
      
      if (response.ok) {
        const report = data.data;
        const reportWindow = window.open('', '_blank');
        reportWindow.document.write(`
          <html>
            <head><title>System Report</title></head>
            <body>
              <h1>WhatsApp API System Report</h1>
              <h2>Generated: ${new Date().toLocaleString()}</h2>
              <pre>${JSON.stringify(report, null, 2)}</pre>
            </body>
          </html>
        `);
      } else {
        throw new Error(data.message || 'Failed to generate report');
      }
    } catch (error) {
      alert(`Error: ${error.message}`);
    }
  };

  window.testWebhook = async function() {
    const sessionId = document.getElementById('webhookSessionId').value;
    
    try {
      const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}/webhook/test`, {
        method: 'POST',
      });
      
      const data = await response.json();
      
      if (response.ok) {
        alert('Webhook test sent successfully');
      } else {
        throw new Error(data.message || 'Webhook test failed');
      }
    } catch (error) {
      alert(`Error: ${error.message}`);
    }
  };
});