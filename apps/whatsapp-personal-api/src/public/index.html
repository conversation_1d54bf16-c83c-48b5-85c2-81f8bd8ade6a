<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WhatsApp API Portal</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      padding-top: 2rem;
      background-color: #f8f9fa;
    }
    .card {
      margin-bottom: 1.5rem;
      box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    .qr-container {
      text-align: center;
      padding: 1rem;
      background-color: white;
      border-radius: 0.25rem;
      margin-top: 1rem;
    }
    .session-list {
      max-height: 500px;
      overflow-y: auto;
    }
    .status-badge {
      font-size: 0.8rem;
    }
    .session-actions {
      display: flex;
      gap: 0.5rem;
      flex-wrap: wrap;
    }
    .session-card {
      border-left: 4px solid #6c757d;
      transition: all 0.3s ease;
    }
    .session-card.connected {
      border-left-color: #28a745;
    }
    .session-card.pending {
      border-left-color: #ffc107;
    }
    .session-card.failed {
      border-left-color: #dc3545;
    }
    .session-card.disconnected {
      border-left-color: #6c757d;
    }
    .session-stats {
      font-size: 0.8rem;
      color: #6c757d;
    }
    .monitoring-panel {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    .stat-card {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 0.5rem;
      padding: 1rem;
      text-align: center;
    }
    .health-indicator {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      display: inline-block;
      margin-right: 0.5rem;
    }
    .health-healthy { background-color: #28a745; }
    .health-unhealthy { background-color: #dc3545; }
    .health-unknown { background-color: #6c757d; }
    .bulk-message-form {
      background-color: #f8f9fa;
      border-radius: 0.5rem;
      padding: 1rem;
      margin-top: 1rem;
    }
  </style>
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
    <div class="container">
      <a class="navbar-brand" href="#">WhatsApp API Portal</a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav ms-auto">
          <li class="nav-item">
            <a class="nav-link" href="#dashboard" data-tab="dashboard">Dashboard</a>
          </li>
          <li class="nav-item">
            <a class="nav-link active" href="#sessions" data-tab="sessions">Sessions</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#messaging" data-tab="messaging">Messaging</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#monitoring" data-tab="monitoring">Monitoring</a>
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <div class="container" style="margin-top: 80px;">
    <!-- Dashboard Tab -->
    <div id="dashboard-content" class="tab-content" style="display: none;">
      <div class="card monitoring-panel">
        <div class="card-header">
          <h4 class="mb-0">System Dashboard</h4>
        </div>
        <div class="card-body">
          <div class="row" id="systemStats">
            <div class="col-md-3">
              <div class="stat-card">
                <h3 id="totalSessions">-</h3>
                <small>Total Sessions</small>
              </div>
            </div>
            <div class="col-md-3">
              <div class="stat-card">
                <h3 id="connectedSessions">-</h3>
                <small>Connected</small>
              </div>
            </div>
            <div class="col-md-3">
              <div class="stat-card">
                <h3 id="pendingSessions">-</h3>
                <small>Pending</small>
              </div>
            </div>
            <div class="col-md-3">
              <div class="stat-card">
                <h3 id="failedSessions">-</h3>
                <small>Failed</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Sessions Tab -->
    <div id="sessions-content" class="tab-content">
      <div class="row">
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">Create New Session</h5>
            </div>
            <div class="card-body">
              <form id="createSessionForm">
                <div class="mb-3">
                  <label for="sessionId" class="form-label">Session ID</label>
                  <input type="text" class="form-control" id="sessionId" required>
                  <div class="form-text">A unique identifier for this WhatsApp session</div>
                </div>
                <div class="mb-3">
                  <label for="sessionName" class="form-label">Session Name (Optional)</label>
                  <input type="text" class="form-control" id="sessionName">
                </div>
                <div class="mb-3">
                  <label for="webhookUrl" class="form-label">Webhook URL (Optional)</label>
                  <input type="url" class="form-control" id="webhookUrl" placeholder="https://your-webhook.com/endpoint">
                  <div class="form-text">URL to receive WhatsApp events and messages</div>
                </div>
                <button type="submit" class="btn btn-primary">Create Session</button>
              </form>

              <div id="qrContainer" class="qr-container" style="display: none;">
                <h5>Scan this QR code with WhatsApp</h5>
                <div id="qrCode"></div>
                <div class="mt-2">
                  <small class="text-muted">Open WhatsApp on your phone, tap Menu or Settings and select WhatsApp Web</small>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h5 class="mb-0">Active Sessions</h5>
              <button class="btn btn-sm btn-outline-primary" onclick="refreshSessions()">
                <i class="fas fa-sync-alt"></i> Refresh
              </button>
            </div>
            <div class="card-body">
              <div class="session-list" id="sessionList">
                <div class="text-center py-4 text-muted">
                  <p>No active sessions</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Messaging Tab -->
    <div id="messaging-content" class="tab-content" style="display: none;">
      <div class="row">
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">Send Message</h5>
            </div>
            <div class="card-body">
              <form id="sendMessageForm">
                <div class="mb-3">
                  <label for="messageSessionId" class="form-label">Session</label>
                  <select class="form-control" id="messageSessionId" required>
                    <option value="">Select a session</option>
                  </select>
                </div>
                <div class="mb-3">
                  <label for="recipientNumber" class="form-label">Recipient Number</label>
                  <input type="text" class="form-control" id="recipientNumber" placeholder="60123456789" required>
                </div>
                <div class="mb-3">
                  <label for="messageText" class="form-label">Message</label>
                  <textarea class="form-control" id="messageText" rows="3" required></textarea>
                </div>
                <button type="submit" class="btn btn-success">Send Message</button>
              </form>
            </div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">Bulk Messaging</h5>
            </div>
            <div class="card-body">
              <form id="bulkMessageForm">
                <div class="mb-3">
                  <label for="bulkSessionId" class="form-label">Session</label>
                  <select class="form-control" id="bulkSessionId" required>
                    <option value="">Select a session</option>
                  </select>
                </div>
                <div class="mb-3">
                  <label for="bulkRecipients" class="form-label">Recipients</label>
                  <textarea class="form-control" id="bulkRecipients" rows="3" placeholder="60123456789&#10;60987654321&#10;..." required></textarea>
                  <div class="form-text">One phone number per line</div>
                </div>
                <div class="mb-3">
                  <label for="bulkMessage" class="form-label">Message</label>
                  <textarea class="form-control" id="bulkMessage" rows="3" required></textarea>
                </div>
                <div class="mb-3">
                  <label for="bulkDelay" class="form-label">Delay between messages (ms)</label>
                  <input type="number" class="form-control" id="bulkDelay" value="1000" min="100">
                </div>
                <button type="submit" class="btn btn-warning">Send Bulk Messages</button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Monitoring Tab -->
    <div id="monitoring-content" class="tab-content" style="display: none;">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">System Monitoring</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6>System Health</h6>
              <div id="systemHealth">
                <p class="text-muted">Loading...</p>
              </div>
            </div>
            <div class="col-md-6">
              <h6>Actions</h6>
              <div class="d-grid gap-2">
                <button class="btn btn-info" onclick="performHealthCheck()">Health Check</button>
                <button class="btn btn-warning" onclick="performCleanup()">Cleanup</button>
                <button class="btn btn-secondary" onclick="generateReport()">Generate Report</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Session Template -->
  <template id="sessionTemplate">
    <div class="card mb-2 session-item session-card">
      <div class="card-body py-2">
        <div class="d-flex justify-content-between align-items-start">
          <div class="flex-grow-1">
            <div class="d-flex align-items-center mb-1">
              <span class="health-indicator health-unknown"></span>
              <h6 class="mb-0 session-name">Session Name</h6>
            </div>
            <small class="text-muted session-id">ID: session123</small>
            <div class="session-stats mt-1">
              <span class="phone-number text-muted"></span>
              <span class="last-seen text-muted"></span>
            </div>
          </div>
          <div class="text-end">
            <div class="mb-2">
              <span class="badge rounded-pill status-badge">Status</span>
            </div>
            <div class="session-actions">
              <button class="btn btn-sm btn-outline-info health-btn" title="Health Check">
                <i class="fas fa-heartbeat"></i>
              </button>
              <button class="btn btn-sm btn-outline-secondary stats-btn" title="Statistics">
                <i class="fas fa-chart-bar"></i>
              </button>
              <button class="btn btn-sm btn-outline-warning reconnect-btn" title="Reconnect">
                <i class="fas fa-sync-alt"></i>
              </button>
              <button class="btn btn-sm btn-outline-primary webhook-btn" title="Webhook">
                <i class="fas fa-link"></i>
              </button>
              <button class="btn btn-sm btn-danger delete-btn" title="Delete">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>

  <!-- Modals -->
  <!-- Session Stats Modal -->
  <div class="modal fade" id="sessionStatsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Session Statistics</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body" id="sessionStatsContent">
          <!-- Content loaded dynamically -->
        </div>
      </div>
    </div>
  </div>

  <!-- Webhook Modal -->
  <div class="modal fade" id="webhookModal" tabindex="-1">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Manage Webhook</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <form id="webhookForm">
            <input type="hidden" id="webhookSessionId">
            <div class="mb-3">
              <label for="modalWebhookUrl" class="form-label">Webhook URL</label>
              <input type="url" class="form-control" id="modalWebhookUrl" placeholder="https://your-webhook.com/endpoint">
            </div>
            <div class="d-flex gap-2">
              <button type="submit" class="btn btn-primary">Update</button>
              <button type="button" class="btn btn-secondary" onclick="testWebhook()">Test</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <!-- QR Code libraries - include both for compatibility -->
  <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.0/build/qrcode.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/qrcodejs@1.0.0/qrcode.min.js"></script>
  <script src="js/main.js"></script>
</body>
</html>
