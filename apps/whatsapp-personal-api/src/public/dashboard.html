<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Sessions Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .header h1 {
            color: #25D366;
            margin-bottom: 10px;
        }

        .user-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .auth-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .auth-tabs {
            display: flex;
            margin-bottom: 20px;
        }

        .auth-tab {
            padding: 10px 20px;
            background: #f0f0f0;
            border: none;
            cursor: pointer;
            margin-right: 10px;
            border-radius: 4px;
        }

        .auth-tab.active {
            background: #25D366;
            color: white;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .btn {
            padding: 10px 20px;
            background: #25D366;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn:hover {
            background: #1da851;
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .dashboard-content {
            display: none;
        }

        .dashboard-content.active {
            display: block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #25D366;
        }

        .stat-label {
            color: #666;
            margin-top: 5px;
        }

        .sessions-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .session-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .session-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .session-name {
            font-weight: bold;
            font-size: 1.1em;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .status-connected {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-disconnected {
            background: #f8d7da;
            color: #721c24;
        }

        .session-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 10px;
        }

        .session-actions {
            display: flex;
            gap: 10px;
        }

        .qr-code {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-top: 10px;
        }

        .qr-code img {
            max-width: 256px;
            height: auto;
        }

        .hidden {
            display: none;
        }

        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }

        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }

        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>WhatsApp Sessions Dashboard</h1>
            <div class="user-info">
                <span id="user-name">Please log in to continue</span>
                <button id="logout-btn" class="logout-btn hidden">Logout</button>
            </div>
        </div>

        <!-- Authentication Section -->
        <div id="auth-section" class="auth-section">
            <div class="auth-tabs">
                <button class="auth-tab active" onclick="showAuthTab('login')">Login</button>
                <button class="auth-tab" onclick="showAuthTab('register')">Register</button>
            </div>

            <div id="login-form" class="auth-form">
                <h3>Login to Your Account</h3>
                <div class="form-group">
                    <label for="login-email">Email:</label>
                    <input type="email" id="login-email" required>
                </div>
                <div class="form-group">
                    <label for="login-password">Password:</label>
                    <input type="password" id="login-password" required>
                </div>
                <button class="btn" onclick="login()">Login</button>
            </div>

            <div id="register-form" class="auth-form hidden">
                <h3>Create New Account</h3>
                <div class="form-group">
                    <label for="register-name">Full Name:</label>
                    <input type="text" id="register-name" required>
                </div>
                <div class="form-group">
                    <label for="register-email">Email:</label>
                    <input type="email" id="register-email" required>
                </div>
                <div class="form-group">
                    <label for="register-password">Password:</label>
                    <input type="password" id="register-password" required>
                </div>
                <button class="btn" onclick="register()">Register</button>
            </div>
        </div>

        <!-- Dashboard Content -->
        <div id="dashboard-content" class="dashboard-content">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="total-sessions">0</div>
                    <div class="stat-label">Total Sessions</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="active-sessions">0</div>
                    <div class="stat-label">Active Sessions</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="connected-sessions">0</div>
                    <div class="stat-label">Connected Sessions</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="max-sessions">0</div>
                    <div class="stat-label">Max Sessions</div>
                </div>
            </div>

            <div class="sessions-section">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h2>Your WhatsApp Sessions</h2>
                    <button class="btn" onclick="createNewSession()">Create New Session</button>
                </div>

                <div id="sessions-container">
                    <!-- Sessions will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        let authToken = localStorage.getItem('authToken');
        let currentUser = null;

        // Initialize the dashboard
        document.addEventListener('DOMContentLoaded', function() {
            if (authToken) {
                showDashboard();
            } else {
                showAuthSection();
            }
        });

        function showAuthTab(tab) {
            document.querySelectorAll('.auth-tab').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.auth-form').forEach(f => f.classList.add('hidden'));
            
            document.querySelector(`[onclick="showAuthTab('${tab}')"]`).classList.add('active');
            document.getElementById(`${tab}-form`).classList.remove('hidden');
        }

        function showAuthSection() {
            document.getElementById('auth-section').style.display = 'block';
            document.getElementById('dashboard-content').classList.remove('active');
            document.getElementById('logout-btn').classList.add('hidden');
        }

        function showDashboard() {
            document.getElementById('auth-section').style.display = 'none';
            document.getElementById('dashboard-content').classList.add('active');
            document.getElementById('logout-btn').classList.remove('hidden');
            
            loadUserProfile();
            loadSessions();
            loadSessionStats();
        }

        async function login() {
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password }),
                });

                const data = await response.json();

                if (response.ok) {
                    authToken = data.tokens.accessToken;
                    localStorage.setItem('authToken', authToken);
                    localStorage.setItem('refreshToken', data.tokens.refreshToken);
                    currentUser = data.user;
                    showDashboard();
                } else {
                    showError(data.error || 'Login failed');
                }
            } catch (error) {
                showError('Network error. Please try again.');
            }
        }

        async function register() {
            const name = document.getElementById('register-name').value;
            const email = document.getElementById('register-email').value;
            const password = document.getElementById('register-password').value;

            try {
                const response = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ name, email, password }),
                });

                const data = await response.json();

                if (response.ok) {
                    authToken = data.tokens.accessToken;
                    localStorage.setItem('authToken', authToken);
                    localStorage.setItem('refreshToken', data.tokens.refreshToken);
                    currentUser = data.user;
                    showDashboard();
                } else {
                    showError(data.error || 'Registration failed');
                }
            } catch (error) {
                showError('Network error. Please try again.');
            }
        }

        async function loadUserProfile() {
            try {
                const response = await fetch('/api/auth/profile', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                    },
                });

                const data = await response.json();

                if (response.ok) {
                    currentUser = data.user;
                    document.getElementById('user-name').textContent = `Welcome, ${currentUser.name}`;
                }
            } catch (error) {
                console.error('Error loading user profile:', error);
            }
        }

        async function loadSessions() {
            try {
                const response = await fetch('/api/sessions', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                    },
                });

                const data = await response.json();

                if (response.ok) {
                    renderSessions(data.data);
                }
            } catch (error) {
                console.error('Error loading sessions:', error);
            }
        }

        async function loadSessionStats() {
            try {
                const response = await fetch('/api/sessions/stats', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                    },
                });

                const data = await response.json();

                if (response.ok) {
                    const stats = data.data;
                    document.getElementById('total-sessions').textContent = stats.totalSessions;
                    document.getElementById('active-sessions').textContent = stats.activeSessions;
                    document.getElementById('connected-sessions').textContent = stats.connectedSessions;
                    document.getElementById('max-sessions').textContent = stats.maxSessions;
                }
            } catch (error) {
                console.error('Error loading session stats:', error);
            }
        }

        function renderSessions(sessions) {
            const container = document.getElementById('sessions-container');
            
            if (sessions.length === 0) {
                container.innerHTML = '<p>No sessions found. Create your first session to get started!</p>';
                return;
            }

            container.innerHTML = sessions.map(session => `
                <div class="session-card">
                    <div class="session-header">
                        <div class="session-name">${session.name || session.id}</div>
                        <div class="status-badge status-${session.status}">
                            ${session.status.toUpperCase()}
                        </div>
                    </div>
                    <div class="session-info">
                        <div><strong>ID:</strong> ${session.id}</div>
                        <div><strong>Phone:</strong> ${session.phoneNumber || 'Not connected'}</div>
                        <div><strong>Created:</strong> ${new Date(session.createdAt).toLocaleString()}</div>
                        <div><strong>Last Seen:</strong> ${session.lastSeen ? new Date(session.lastSeen).toLocaleString() : 'Never'}</div>
                    </div>
                    <div class="session-actions">
                        ${session.status === 'pending' ? `<button class="btn btn-secondary" onclick="showQRCode('${session.id}')">Show QR Code</button>` : ''}
                        ${session.status === 'connected' ? `<button class="btn btn-secondary" onclick="showSendMessage('${session.id}')">Send Message</button>` : ''}
                        <button class="btn btn-danger" onclick="deleteSession('${session.id}')">Delete</button>
                    </div>
                    <div id="qr-${session.id}" class="qr-code hidden"></div>
                </div>
            `).join('');
        }

        async function createNewSession() {
            const name = prompt('Enter session name (optional):');
            
            try {
                const response = await fetch('/api/sessions', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ name }),
                });

                const data = await response.json();

                if (response.ok) {
                    showSuccess('Session created successfully!');
                    loadSessions();
                    loadSessionStats();
                } else {
                    showError(data.message || 'Failed to create session');
                }
            } catch (error) {
                showError('Network error. Please try again.');
            }
        }

        async function showQRCode(sessionId) {
            try {
                const response = await fetch(`/api/sessions/${sessionId}/qr`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                    },
                });

                const data = await response.json();

                if (response.ok) {
                    const qrContainer = document.getElementById(`qr-${sessionId}`);
                    qrContainer.innerHTML = `
                        <h4>Scan QR Code with WhatsApp</h4>
                        <img src="https://api.qrserver.com/v1/create-qr-code/?size=256x256&data=${encodeURIComponent(data.data.qr)}" alt="QR Code">
                        <p>Open WhatsApp → Settings → WhatsApp Web → Scan QR Code</p>
                    `;
                    qrContainer.classList.remove('hidden');
                } else {
                    showError(data.message || 'Failed to get QR code');
                }
            } catch (error) {
                showError('Network error. Please try again.');
            }
        }

        async function deleteSession(sessionId) {
            if (!confirm('Are you sure you want to delete this session?')) {
                return;
            }

            try {
                const response = await fetch(`/api/sessions/${sessionId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                    },
                });

                const data = await response.json();

                if (response.ok) {
                    showSuccess('Session deleted successfully!');
                    loadSessions();
                    loadSessionStats();
                } else {
                    showError(data.message || 'Failed to delete session');
                }
            } catch (error) {
                showError('Network error. Please try again.');
            }
        }

        function showSendMessage(sessionId) {
            const to = prompt('Enter recipient phone number (with country code):');
            const message = prompt('Enter your message:');
            
            if (to && message) {
                sendMessage(sessionId, to, message);
            }
        }

        async function sendMessage(sessionId, to, text) {
            try {
                const response = await fetch(`/api/messages/${sessionId}/send`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ to, text }),
                });

                const data = await response.json();

                if (response.ok) {
                    showSuccess('Message sent successfully!');
                } else {
                    showError(data.message || 'Failed to send message');
                }
            } catch (error) {
                showError('Network error. Please try again.');
            }
        }

        function logout() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('refreshToken');
            authToken = null;
            currentUser = null;
            showAuthSection();
        }

        function showError(message) {
            // Simple error display - in a real app, you'd want a proper notification system
            alert('Error: ' + message);
        }

        function showSuccess(message) {
            // Simple success display - in a real app, you'd want a proper notification system
            alert('Success: ' + message);
        }

        // Event listeners
        document.getElementById('logout-btn').addEventListener('click', logout);

        // Auto-refresh sessions every 30 seconds
        setInterval(() => {
            if (authToken && document.getElementById('dashboard-content').classList.contains('active')) {
                loadSessions();
                loadSessionStats();
            }
        }, 30000);
    </script>
</body>
</html>