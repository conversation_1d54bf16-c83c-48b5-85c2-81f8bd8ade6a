import { Router } from 'express';
import * as sessionController from '../controllers/session.controller';

const router = Router();

// Session CRUD operations
router.post('/', sessionController.createSession as any);
router.get('/', sessionController.getAllSessions as any);
router.get('/:id', sessionController.getSession as any);
router.put('/:id', sessionController.updateSession as any);
router.delete('/:id', sessionController.deleteSession as any);

// Session management operations
router.post('/:id/reconnect', sessionController.reconnectSession as any);

// Session health and statistics
router.get('/:id/health', sessionController.getSessionHealth as any);
router.get('/:id/stats', sessionController.getSessionStats as any);

// Webhook management
router.put('/:id/webhook', sessionController.updateSessionWebhook as any);
router.post('/:id/webhook/test', sessionController.testSessionWebhook as any);

export default router;
