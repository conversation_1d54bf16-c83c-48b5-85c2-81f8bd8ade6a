import { Router } from 'express';
import { enhancedMessageController } from '../controllers/message.controller.enhanced';
import { authenticateToken } from '../middleware/auth.middleware';

const router = Router();

// All message routes require authentication
router.use(authenticateToken);

// Send message routes
router.post('/:id/send', enhancedMessageController.sendMessage);
router.post('/:id/send-bulk', enhancedMessageController.sendBulkMessage);

// Message statistics
router.get('/:id/stats', enhancedMessageController.getMessageStats);

export default router;