import { Router } from 'express';
import sessionRoutes from './session.routes';
import messageRoutes from './message.routes';
import monitoringRoutes from './monitoring.routes';

const router = Router();

// API routes
router.use('/sessions', sessionRoutes);
router.use('/messages', messageRoutes);
router.use('/monitoring', monitoringRoutes);

// Health check endpoint
router.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'WhatsApp API is running',
    timestamp: new Date().toISOString(),
  });
});

export default router;
