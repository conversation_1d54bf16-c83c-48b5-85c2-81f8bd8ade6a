import { Router } from 'express';
import { enhancedSessionController } from '../controllers/session.controller.enhanced';
import { authenticateToken } from '../middleware/auth.middleware';

const router = Router();

// All session routes require authentication
router.use(authenticateToken);

// Session management routes
router.post('/', enhancedSessionController.createSession);
router.get('/', enhancedSessionController.getUserSessions);
router.get('/stats', enhancedSessionController.getSessionStats);
router.get('/:id', enhancedSessionController.getSession);
router.delete('/:id', enhancedSessionController.deleteSession);

// QR code route
router.get('/:id/qr', enhancedSessionController.getQRCode);

// Admin route (would need admin role check in real implementation)
router.get('/admin/all', enhancedSessionController.getAllSessions);

export default router;