import { Router } from 'express';
import * as monitoringController from '../controllers/monitoring.controller';

const router = Router();

// System health and monitoring
router.get('/health', monitoringController.getSystemHealth as any);
router.get('/stats', monitoringController.getServiceStats as any);
router.get('/report', monitoringController.generateReport as any);

// Health check operations
router.post('/health-check', monitoringController.performHealthCheck as any);
router.post('/cleanup', monitoringController.performCleanup as any);

// Monitoring control
router.post('/start', monitoringController.startMonitoring as any);
router.post('/stop', monitoringController.stopMonitoring as any);

export default router;