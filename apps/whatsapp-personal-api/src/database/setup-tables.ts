import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { CreateTableCommand, DescribeTableCommand } from '@aws-sdk/client-dynamodb';

const client = new DynamoDBClient({
  region: process.env.AWS_REGION || 'us-east-1',
});

const TABLES = {
  USERS: process.env.USERS_TABLE_NAME || 'whatsapp-users',
  SESSIONS: process.env.SESSIONS_TABLE_NAME || 'whatsapp-sessions',
};

async function createUsersTable() {
  try {
    await client.send(new DescribeTableCommand({ TableName: TABLES.USERS }));
    console.log(`Users table ${TABLES.USERS} already exists`);
  } catch (error) {
    console.log(`Creating users table ${TABLES.USERS}...`);
    await client.send(new CreateTableCommand({
      TableName: TABLES.USERS,
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'email', AttributeType: 'S' },
      ],
      GlobalSecondaryIndexes: [
        {
          IndexName: 'EmailIndex',
          KeySchema: [
            { AttributeName: 'email', KeyType: 'HASH' },
          ],
          Projection: { ProjectionType: 'ALL' },
          BillingMode: 'PAY_PER_REQUEST',
        },
      ],
      BillingMode: 'PAY_PER_REQUEST',
    }));
    console.log(`Users table ${TABLES.USERS} created successfully`);
  }
}

async function createSessionsTable() {
  try {
    await client.send(new DescribeTableCommand({ TableName: TABLES.SESSIONS }));
    console.log(`Sessions table ${TABLES.SESSIONS} already exists`);
  } catch (error) {
    console.log(`Creating sessions table ${TABLES.SESSIONS}...`);
    await client.send(new CreateTableCommand({
      TableName: TABLES.SESSIONS,
      KeySchema: [
        { AttributeName: 'id', KeyType: 'HASH' },
      ],
      AttributeDefinitions: [
        { AttributeName: 'id', AttributeType: 'S' },
        { AttributeName: 'userId', AttributeType: 'S' },
      ],
      GlobalSecondaryIndexes: [
        {
          IndexName: 'UserIdIndex',
          KeySchema: [
            { AttributeName: 'userId', KeyType: 'HASH' },
          ],
          Projection: { ProjectionType: 'ALL' },
          BillingMode: 'PAY_PER_REQUEST',
        },
      ],
      BillingMode: 'PAY_PER_REQUEST',
    }));
    console.log(`Sessions table ${TABLES.SESSIONS} created successfully`);
  }
}

async function setupTables() {
  try {
    await createUsersTable();
    await createSessionsTable();
    console.log('All tables created successfully!');
  } catch (error) {
    console.error('Error setting up tables:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  setupTables();
}

export { setupTables };