import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, PutCommand, GetCommand, UpdateCommand, DeleteCommand, ScanCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';

const client = new DynamoDBClient({
  region: process.env.AWS_REGION || 'us-east-1',
});

export const dynamoDb = DynamoDBDocumentClient.from(client);

export const TABLES = {
  USERS: process.env.USERS_TABLE_NAME || 'whatsapp-users',
  SESSIONS: process.env.SESSIONS_TABLE_NAME || 'whatsapp-sessions',
};

export interface User {
  id: string;
  email: string;
  password: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  sessionCount: number;
  maxSessions: number;
}

export interface WhatsAppSession {
  id: string;
  userId: string;
  name?: string;
  status: 'pending' | 'connecting' | 'connected' | 'disconnected' | 'expired';
  qrCode?: string;
  phoneNumber?: string;
  createdAt: string;
  updatedAt: string;
  lastSeen?: string;
  isActive: boolean;
  sessionDirectory: string;
}

export class DatabaseService {
  // User operations
  async createUser(user: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
    const now = new Date().toISOString();
    const newUser: User = {
      ...user,
      id: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: now,
      updatedAt: now,
      sessionCount: 0,
      maxSessions: user.maxSessions || 3,
    };

    await dynamoDb.send(new PutCommand({
      TableName: TABLES.USERS,
      Item: newUser,
    }));

    return newUser;
  }

  async getUserByEmail(email: string): Promise<User | null> {
    const result = await dynamoDb.send(new ScanCommand({
      TableName: TABLES.USERS,
      FilterExpression: 'email = :email',
      ExpressionAttributeValues: {
        ':email': email,
      },
    }));

    return result.Items?.[0] as User || null;
  }

  async getUserById(id: string): Promise<User | null> {
    const result = await dynamoDb.send(new GetCommand({
      TableName: TABLES.USERS,
      Key: { id },
    }));

    return result.Item as User || null;
  }

  async updateUser(id: string, updates: Partial<User>): Promise<User | null> {
    const updateExpression = Object.keys(updates).map(key => `#${key} = :${key}`).join(', ');
    const expressionAttributeNames = Object.keys(updates).reduce((acc, key) => {
      acc[`#${key}`] = key;
      return acc;
    }, {} as Record<string, string>);
    const expressionAttributeValues = Object.keys(updates).reduce((acc, key) => {
      acc[`:${key}`] = updates[key as keyof User];
      return acc;
    }, {} as Record<string, any>);

    expressionAttributeValues[':updatedAt'] = new Date().toISOString();

    const result = await dynamoDb.send(new UpdateCommand({
      TableName: TABLES.USERS,
      Key: { id },
      UpdateExpression: `SET ${updateExpression}, #updatedAt = :updatedAt`,
      ExpressionAttributeNames: {
        ...expressionAttributeNames,
        '#updatedAt': 'updatedAt',
      },
      ExpressionAttributeValues: expressionAttributeValues,
      ReturnValues: 'ALL_NEW',
    }));

    return result.Attributes as User || null;
  }

  // Session operations
  async createSession(session: Omit<WhatsAppSession, 'id' | 'createdAt' | 'updatedAt'>): Promise<WhatsAppSession> {
    const now = new Date().toISOString();
    const newSession: WhatsAppSession = {
      ...session,
      id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: now,
      updatedAt: now,
      status: 'pending',
      isActive: true,
    };

    await dynamoDb.send(new PutCommand({
      TableName: TABLES.SESSIONS,
      Item: newSession,
    }));

    // Update user session count
    await this.incrementUserSessionCount(session.userId);

    return newSession;
  }

  async getSessionById(id: string): Promise<WhatsAppSession | null> {
    const result = await dynamoDb.send(new GetCommand({
      TableName: TABLES.SESSIONS,
      Key: { id },
    }));

    return result.Item as WhatsAppSession || null;
  }

  async getSessionsByUserId(userId: string): Promise<WhatsAppSession[]> {
    const result = await dynamoDb.send(new QueryCommand({
      TableName: TABLES.SESSIONS,
      IndexName: 'UserIdIndex',
      KeyConditionExpression: 'userId = :userId',
      ExpressionAttributeValues: {
        ':userId': userId,
      },
    }));

    return result.Items as WhatsAppSession[] || [];
  }

  async updateSession(id: string, updates: Partial<WhatsAppSession>): Promise<WhatsAppSession | null> {
    const updateExpression = Object.keys(updates).map(key => `#${key} = :${key}`).join(', ');
    const expressionAttributeNames = Object.keys(updates).reduce((acc, key) => {
      acc[`#${key}`] = key;
      return acc;
    }, {} as Record<string, string>);
    const expressionAttributeValues = Object.keys(updates).reduce((acc, key) => {
      acc[`:${key}`] = updates[key as keyof WhatsAppSession];
      return acc;
    }, {} as Record<string, any>);

    expressionAttributeValues[':updatedAt'] = new Date().toISOString();

    const result = await dynamoDb.send(new UpdateCommand({
      TableName: TABLES.SESSIONS,
      Key: { id },
      UpdateExpression: `SET ${updateExpression}, #updatedAt = :updatedAt`,
      ExpressionAttributeNames: {
        ...expressionAttributeNames,
        '#updatedAt': 'updatedAt',
      },
      ExpressionAttributeValues: expressionAttributeValues,
      ReturnValues: 'ALL_NEW',
    }));

    return result.Attributes as WhatsAppSession || null;
  }

  async deleteSession(id: string): Promise<boolean> {
    const session = await this.getSessionById(id);
    if (!session) return false;

    await dynamoDb.send(new DeleteCommand({
      TableName: TABLES.SESSIONS,
      Key: { id },
    }));

    // Update user session count
    await this.decrementUserSessionCount(session.userId);

    return true;
  }

  async getAllActiveSessions(): Promise<WhatsAppSession[]> {
    const result = await dynamoDb.send(new ScanCommand({
      TableName: TABLES.SESSIONS,
      FilterExpression: 'isActive = :isActive',
      ExpressionAttributeValues: {
        ':isActive': true,
      },
    }));

    return result.Items as WhatsAppSession[] || [];
  }

  // Helper methods
  private async incrementUserSessionCount(userId: string): Promise<void> {
    await dynamoDb.send(new UpdateCommand({
      TableName: TABLES.USERS,
      Key: { id: userId },
      UpdateExpression: 'SET sessionCount = sessionCount + :inc',
      ExpressionAttributeValues: {
        ':inc': 1,
      },
    }));
  }

  private async decrementUserSessionCount(userId: string): Promise<void> {
    await dynamoDb.send(new UpdateCommand({
      TableName: TABLES.USERS,
      Key: { id: userId },
      UpdateExpression: 'SET sessionCount = sessionCount - :dec',
      ExpressionAttributeValues: {
        ':dec': 1,
      },
    }));
  }

  async canUserCreateSession(userId: string): Promise<boolean> {
    const user = await this.getUserById(userId);
    if (!user) return false;
    
    return user.sessionCount < user.maxSessions;
  }
}

export const db = new DatabaseService();