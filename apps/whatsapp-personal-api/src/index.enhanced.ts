import express from 'express';
import cors from 'cors';
import path from 'path';
import dotenv from 'dotenv';
import config from './config';

// Import enhanced routes
import authRoutes from './routes/auth.routes';
import sessionRoutes from './routes/session.routes.enhanced';
import messageRoutes from './routes/message.routes.enhanced';

// Import database setup
import { setupTables } from './database/setup-tables';

// Load environment variables
dotenv.config();

// Create Express app
const app = express();

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Security headers
app.use((req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  next();
});

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'whatsapp-personal-api',
    version: '2.0.0'
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/sessions', sessionRoutes);
app.use('/api/messages', messageRoutes);

// Legacy routes for backward compatibility (optional)
app.use('/api', require('./routes').default);

// Serve the web portal
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Error handling middleware
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Unhandled error:', err);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    ...(process.env.NODE_ENV === 'development' && { error: err.message })
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

// Initialize database tables and start server
async function startServer() {
  try {
    console.log('Setting up database tables...');
    await setupTables();
    console.log('Database setup complete');

    // Start server
    const server = app.listen(config.port, () => {
      console.log(`✅ Multi-Session WhatsApp API Server running on port ${config.port}`);
      console.log(`🌐 Environment: ${config.environment}`);
      console.log(`🔗 Webhook URL: ${config.webhookUrl}`);
      console.log(`📁 Sessions directory: ${config.sessionsDir}`);
      console.log(`\n🚀 API Endpoints:`);
      console.log(`   POST /api/auth/register - Register new user`);
      console.log(`   POST /api/auth/login - Login user`);
      console.log(`   GET  /api/auth/profile - Get user profile`);
      console.log(`   POST /api/sessions - Create new WhatsApp session`);
      console.log(`   GET  /api/sessions - Get user sessions`);
      console.log(`   POST /api/messages/:id/send - Send message`);
      console.log(`   GET  /api/sessions/:id/qr - Get QR code`);
      console.log(`\n📋 Required Environment Variables:`);
      console.log(`   - JWT_SECRET: JWT signing secret`);
      console.log(`   - AWS_REGION: AWS region for DynamoDB`);
      console.log(`   - USERS_TABLE_NAME: DynamoDB users table`);
      console.log(`   - SESSIONS_TABLE_NAME: DynamoDB sessions table`);
    });

    // Graceful shutdown
    process.on('SIGTERM', () => {
      console.log('SIGTERM received, shutting down gracefully');
      server.close(() => {
        console.log('Server closed');
        process.exit(0);
      });
    });

    process.on('SIGINT', () => {
      console.log('SIGINT received, shutting down gracefully');
      server.close(() => {
        console.log('Server closed');
        process.exit(0);
      });
    });

  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Start the server
startServer();