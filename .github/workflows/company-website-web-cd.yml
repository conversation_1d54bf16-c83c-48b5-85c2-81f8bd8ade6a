name: Company Web CD

on:
  push:
    branches:
      - main
    paths:
      - "apps/company-website-web/**"
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    env:
      AWS_DEFAULT_REGION: ap-southeast-1
      S3_BUCKET_NAME: anchorsprint.com
      LOCAL_FOLDER: ./apps/company-website-web
      CLOUDFRONT_DISTRIBUTION_ID: E1FVO7VNYIXPZH
      CLOUDFRONT_INVALIDATION_PATH: /*

    # Secrets access: AWS_OIDC_ROLE_ARN, AWS_TARGET_ROLE_ARN

    steps:
      - uses: actions/checkout@v3

      - name: Configure AWS Credentials for OIDC Role
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-region: ${{ env.AWS_DEFAULT_REGION }}
          role-to-assume: ${{ secrets.AWS_OIDC_ROLE_ARN }}
          role-session-name: GitHubActionsOIDCSession

      - name: Assume Target Role and Set AWS Credentials
        run: |
          OUTPUT=$(aws sts assume-role --role-arn ${{ secrets.AWS_TARGET_ROLE_ARN }} --role-session-name GitHubActionsTargetSession)
          echo "AWS_ACCESS_KEY_ID=$(echo $OUTPUT | jq -r .Credentials.AccessKeyId)" >> $GITHUB_ENV
          echo "AWS_SECRET_ACCESS_KEY=$(echo $OUTPUT | jq -r .Credentials.SecretAccessKey)" >> $GITHUB_ENV
          echo "AWS_SESSION_TOKEN=$(echo $OUTPUT | jq -r .Credentials.SessionToken)" >> $GITHUB_ENV

      - name: Sync Directory to S3 and Capture Output
        id: s3_sync
        run: |
          SYNC_OUTPUT=$(aws s3 sync ${{ env.LOCAL_FOLDER }} s3://${{ env.S3_BUCKET_NAME }} --delete)
          if [[ -z "$SYNC_OUTPUT" ]]; then
            echo "No changes detected."
            echo "HAS_CHANGES=false" >> $GITHUB_ENV
          else
            echo "Changes detected."
            echo "HAS_CHANGES=true" >> $GITHUB_ENV
          fi

      - name: Invalidate CloudFront Distribution (Conditional)
        if: env.HAS_CHANGES == 'true'
        run: |
          aws cloudfront create-invalidation --distribution-id ${{ env.CLOUDFRONT_DISTRIBUTION_ID }} --paths "${{ env.CLOUDFRONT_INVALIDATION_PATH }}"
