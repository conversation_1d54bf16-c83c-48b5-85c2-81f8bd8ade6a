# ECS Services Configuration for api.dev.anchorsprint.com

# Test Service with default route to nginx
test-service:
  container_image: nginx:latest
  container_port: 80
  path_patterns:
    - "/test*"
  health_check_path: "/health"
  # Custom command to create health check endpoint
  command:
    [
      "/bin/sh",
      "-c",
      'echo ''server { listen 80; location / { root /usr/share/nginx/html; index index.html; } location /health { return 200 "OK"; } }'' > /etc/nginx/conf.d/default.conf && nginx -g ''daemon off;''',
    ]
  # Task CPU and memory settings
  task_cpu: 256 # Minimum CPU for Fargate
  task_memory: 512 # Minimum memory for Fargate
  container_memory: 512 # Explicitly set container memory
  assign_public_ip: true # Enable public IP for tasks in default VPC
  environment_variables:
    SERVICE_NAME: test-service
    LOG_LEVEL: info
  desired_count: 1
  tags:
    Service: Test Service
    Description: Default route to nginx for testing
    schedule: "true" # Enable scheduling for this service

sales-ai-ttl:
  container_image: "533267093267.dkr.ecr.ap-southeast-1.amazonaws.com/sales-assistant-ttl:latest"
  container_port: 8080
  path_patterns:
    - "/webhook*"
    - "/whatsapp-webhook"
    - "/api/sales-assistant*"
  health_check_path: "/api/sales-assistant-ttl/health"
  # Task CPU and memory settings
  task_cpu: 256 #
  task_memory: 512 #
  container_memory: 512 # Explicitly set container memory
  assign_public_ip: true # Enable public IP for tasks in default VPC
  environment_variables:
    PORT: "8080"
    HOST: "0.0.0.0"
    ENVIRONMENT: "development"
    OPENAI_MODEL: "gpt-4.1-nano"
    MENU_CSV_PATH: "/app/data/menu.csv"
    ORDERS_CSV_PATH: "/app/data/orders.csv"
    LOG_DIR: "/app/logs"
  # Secrets from AWS Secrets Manager
  secrets:
    "OPENAI_API_KEY": "arn:aws:secretsmanager:ap-southeast-1:533267093267:secret:sales-assistant-ttl/openai-api-key-Zf6jIq"
    "TELEGRAM_BOT_TOKEN": "arn:aws:secretsmanager:ap-southeast-1:533267093267:secret:sales-assistant-ttl/telegram-bot-token-rSLBF7"
  desired_count: 1
  tags:
    Service: "Sales Assistant TTL"
    Description: "AI-powered sales assistant chatbot with Telegram webhook"
    schedule: "false" # Enable scheduling for this service

whatsapp-personal-api:
  container_image: "533267093267.dkr.ecr.ap-southeast-1.amazonaws.com/whatsapp-personal-api:latest"
  container_port: 3000
  path_patterns:
    - "/ws"
    - "/js*"
    - "/whatsapp-web"
    - "/whatsapp-api*"
  health_check_path: "/whatsapp-api/health"
  task_cpu: 256
  task_memory: 512
  container_memory: 512
  assign_public_ip: true
  environment_variables:
    HOST: "0.0.0.0"
    PORT: "3000"
    NODE_ENV: "production"
    AWS_REGION: "ap-southeast-1"
    ALLOWED_API_KEY_SECRET_NAME: "arn:aws:secretsmanager:ap-southeast-1:533267093267:secret:dev/whatsapp-api/poc-wa-api-key-LUkANM"
  # Secrets from AWS Secrets Manager
  secrets:
    "WHATSAPP_API_KEY": "arn:aws:secretsmanager:ap-southeast-1:533267093267:secret:dev/whatsapp-api/poc-wa-api-key-LUkANM"
  desired_count: 1
  tags:
    Service: "WhatsApp Personal API"
    Description: "Node.js WhatsApp API"
    schedule: "false"