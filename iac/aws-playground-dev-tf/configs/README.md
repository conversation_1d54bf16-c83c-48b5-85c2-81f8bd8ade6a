# ECS Services Configuration Guide

This document explains how to configure the `ecs_services.yaml` file for the ECS App Platform.

## Overview

The `ecs_services.yaml` file defines the configuration for each service in the ECS App Platform. Each service is defined as a top-level key in the YAML file, with various configuration options as nested keys.

## Example Configuration

Here's a basic example of a service configuration:

```yaml
# Service name (used as identifier)
my-service:
  # Container configuration
  container_image: nginx:latest
  container_port: 80
  container_memory: 512
  
  # Task configuration
  task_cpu: 256
  task_memory: 512
  
  # Routing configuration
  path_patterns:
    - "/api/*"
  health_check_path: "/health"
  
  # Network configuration
  assign_public_ip: true
  
  # Custom command (optional)
  command:
    - "/bin/sh"
    - "-c"
    - "echo 'server { listen 80; location / { root /usr/share/nginx/html; } location /health { return 200 \"OK\"; } }' > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"
  
  # Environment variables
  environment_variables:
    SERVICE_NAME: my-service
    LOG_LEVEL: info
  
  # Service scaling
  desired_count: 2
  
  # Additional tags
  tags:
    Service: "My Service"
    Description: "Description of my service"
```

## Configuration Options

### Container Configuration

| Option | Description | Required | Default |
|--------|-------------|----------|---------|
| `container_image` | Docker image to use for the container | Yes | - |
| `container_port` | Port the container listens on | No | 80 |
| `container_cpu` | CPU units for the container | No | Same as task_cpu |
| `container_memory` | Memory limit for the container (MiB) | No | Same as task_memory |
| `command` | Command to run in the container | No | Default command in the image |

### Task Configuration

| Option | Description | Required | Default |
|--------|-------------|----------|---------|
| `task_cpu` | CPU units for the ECS task | No | 256 (minimum for Fargate) |
| `task_memory` | Memory for the ECS task (MiB) | No | 512 (minimum for Fargate) |

### Routing Configuration

| Option | Description | Required | Default |
|--------|-------------|----------|---------|
| `path_patterns` | List of path patterns to route to this service | Yes | - |
| `host_headers` | List of host headers to route to this service | No | - |
| `listener_rule_priority` | Priority for the listener rule | No | Auto-assigned |
| `health_check_path` | Path for health checks | No | `/` |
| `health_check_interval` | Interval between health checks (seconds) | No | 30 |
| `health_check_timeout` | Timeout for health checks (seconds) | No | 5 |
| `health_check_healthy_threshold` | Number of successful checks to consider healthy | No | 3 |
| `health_check_unhealthy_threshold` | Number of failed checks to consider unhealthy | No | 3 |
| `health_check_matcher` | HTTP codes to consider healthy | No | `200-299` |
| `health_check_grace_period_seconds` | Grace period before health checks start | No | 60 |

### Network Configuration

| Option | Description | Required | Default |
|--------|-------------|----------|---------|
| `assign_public_ip` | Whether to assign a public IP to the task | No | Value from module config |

### Service Configuration

| Option | Description | Required | Default |
|--------|-------------|----------|---------|
| `desired_count` | Number of task instances to run | No | 1 |
| `enable_circuit_breaker` | Enable deployment circuit breaker | No | true |
| `enable_circuit_breaker_rollback` | Enable rollback on deployment failure | No | true |

### Environment Variables and Secrets

| Option | Description | Required | Default |
|--------|-------------|----------|---------|
| `environment_variables` | Map of environment variables | No | `{}` |
| `secrets` | Map of secrets (ARN references) | No | `{}` |

### Logging Configuration

| Option | Description | Required | Default |
|--------|-------------|----------|---------|
| `log_retention_days` | Number of days to retain logs | No | 30 |

### Additional Configuration

| Option | Description | Required | Default |
|--------|-------------|----------|---------|
| `tags` | Map of tags to add to resources | No | `{}` |

## Multiple Services Example

You can define multiple services in the same file:

```yaml
# API Service
api-service:
  container_image: my-api:latest
  path_patterns:
    - "/api/*"
  health_check_path: "/api/health"
  task_cpu: 512
  task_memory: 1024
  environment_variables:
    SERVICE_NAME: api-service
    LOG_LEVEL: info

# Frontend Service
frontend-service:
  container_image: my-frontend:latest
  path_patterns:
    - "/*"
  health_check_path: "/health"
  task_cpu: 256
  task_memory: 512
  environment_variables:
    SERVICE_NAME: frontend-service
    LOG_LEVEL: info
```

## Best Practices

1. **Resource Allocation**: Start with the minimum resources (256 CPU units, 512 MiB memory) and increase as needed based on monitoring.
2. **Health Checks**: Always define a health check path that returns a 200 status code when the service is healthy.
3. **Path Patterns**: Use specific path patterns to route traffic to the appropriate service.
4. **Environment Variables**: Use environment variables for configuration that might change between environments.
5. **Tags**: Add descriptive tags to make it easier to identify resources.
