# ECS App Platform for api.dev.anchorsprint.com

# Use default VPC and subnets for the ECS App Platform
data "aws_vpc" "default" {
  default = true
}

data "aws_subnets" "default" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.default.id]
  }
}

# Load services configuration from YAML
locals {
  services_config = yamldecode(file("${path.module}/configs/ecs_services.yaml"))
}

# Using the ALB certificate defined in certificates.tf

# Create the ECS App Platform
module "api_platform" {
  source   = "../../modules/ecs_app_platform"
  env      = "dev"
  app_name = "api"

  # Use default VPC and subnets
  vpc_id             = data.aws_vpc.default.id
  alb_subnet_ids     = data.aws_subnets.default.ids
  service_subnet_ids = data.aws_subnets.default.ids

  # Use services configuration from YAML
  services_config = local.services_config

  # HTTPS Configuration
  create_https_listener = true
  certificate_arn       = module.alb_certificate.certificate_arn
  ssl_policy            = "ELBSecurityPolicy-TLS13-1-2-2021-06"

  # HTTP to HTTPS redirect
  create_http_listener = true
  http_listener_type   = "redirect"

  # Default settings for all services
  default_assign_public_ip = true # Required for tasks in default VPC subnets

  task_role_policy_arns = [aws_iam_policy.ecs_tasks_secrets_read.arn]

  tags = merge(local.this.tags, {
    Project = "ECS App Platform"
  })

  depends_on = [module.alb_certificate]
}

# Read-only access to Secrets Manager for ECS tasks
resource "aws_iam_policy" "ecs_tasks_secrets_read" {
  name        = "ecs-tasks-secrets-read"
  description = "Allow ECS tasks in api.dev.anchorsprint.com to read secrets"
  policy      = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid    = "SecretsRead",
        Effect = "Allow",
        Action = [
          "secretsmanager:GetSecretValue",
          "secretsmanager:DescribeSecret"
        ],
        Resource = "arn:aws:secretsmanager:ap-southeast-1:533267093267:secret:*"
      }
    ]
  })
}

# Create DNS record for the ALB
resource "aws_route53_record" "api" {
  zone_id = aws_route53_zone.subdomain_zone.zone_id
  name    = "api"
  type    = "A"

  alias {
    name                   = module.api_platform.alb_dns_name
    zone_id                = module.api_platform.alb_zone_id
    evaluate_target_health = true
  }
}

# Outputs
output "api_dns_name" {
  value       = "api.dev.anchorsprint.com"
  description = "The DNS name of the API"
}

output "alb_dns_name" {
  value       = module.api_platform.alb_dns_name
  description = "The DNS name of the ALB"
}

output "ecs_cluster_name" {
  value       = module.api_platform.cluster_name
  description = "The name of the ECS cluster"
}

output "services" {
  value       = module.api_platform.services
  description = "The ECS services"
}
