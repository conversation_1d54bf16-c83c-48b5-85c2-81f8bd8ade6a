# Create ACM certificate for ALB (in the current region)
module "alb_certificate" {
  source = "../../modules/acm_certificate"

  domain_name     = "api.dev.anchorsprint.com"
  route53_zone_id = aws_route53_zone.subdomain_zone.zone_id

  tags = merge(local.this.tags, {
    Name = "api.dev.anchorsprint.com-alb-certificate"
  })

  providers = {
    aws.certificate_region = aws
  }
}

# Output the certificate ARNs
output "alb_certificate_arn" {
  value       = module.alb_certificate.certificate_arn
  description = "The ARN of the ALB certificate"
}
