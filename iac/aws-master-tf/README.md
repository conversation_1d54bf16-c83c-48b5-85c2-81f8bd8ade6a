# AWS Master Terraform Configuration

This directory contains the Terraform configuration for managing the AWS master account, including:

- AWS Organizations accounts
- Service Control Policies (SCPs)
- Route53 DNS zones and records
- OpenID Connect providers for Terraform Cloud and GitHub Actions

## Usage

### Terraform Cloud OIDC Configuration

The `open_id_tfc.tf` file configures OpenID Connect for Terraform Cloud, allowing secure authentication without long-lived credentials.

#### Single Project Access

To grant access to a single project:

```hcl
module "aws_master" {
  source = "./iac/aws-master-tf"

  tfc_organization_name = "your-org"
  tfc_project_name      = "your-project"
  # Other variables...
}
```

#### Multiple Project/Workspace Access

To grant access to multiple projects or specific workspaces:

```hcl
module "aws_master" {
  source = "./iac/aws-master-tf"

  tfc_organization_name = "your-org"
  tfc_project_name      = "your-project"  # Still required as a base

  tfc_sub_conditions = [
    "organization:your-org:project:project1:workspace:*:*:*",
    "organization:your-org:project:project2:workspace:specific-workspace:*:*",
    "organization:your-org:project:project3:workspace:another-workspace:run:*"
  ]

  # Other variables...
}
```

#### Default Configuration

The module comes with default sub conditions for Anchor Sprint and Grolier organizations:

```hcl
# These values are already set as defaults in vars.tf
tfc_sub_conditions = [
  "organization:anchor-sprint:project:AWS-Project:workspace:*:*:*",
  "organization:grolier:project:*:workspace:*:*:*"
]
```

This default configuration allows:

- Any workspace in the AWS-Project project in the anchor-sprint organization
- Any workspace in any project in the grolier organization

You don't need to specify these values unless you want to override them.

## Variables

| Name                     | Description                                                                 | Type         | Default                                                                                                              | Required |
| ------------------------ | --------------------------------------------------------------------------- | ------------ | -------------------------------------------------------------------------------------------------------------------- | :------: |
| tfc_aws_audience         | The audience value to use in run identity tokens                            | string       | "aws.workload.identity"                                                                                              |    no    |
| tfc_hostname             | The hostname of the TFC or TFE instance                                     | string       | "app.terraform.io"                                                                                                   |    no    |
| tfc_organization_name    | The name of your Terraform Cloud organization                               | string       | n/a                                                                                                                  |   yes    |
| tfc_project_name         | Name for Terraform project that grant access by this AWS master sub account | string       | n/a                                                                                                                  |   yes    |
| tfc_sub_conditions       | List of sub conditions for Terraform Cloud OIDC provider                    | list(string) | ["organization:anchor-sprint:project:AWS-Project:workspace:*:*:*", "organization:grolier:project:*:workspace:*:*:*"] |    no    |
| github_organization_name | The name of your GitHub organization name for OIDC Connect                  | string       | n/a                                                                                                                  |   yes    |
| github_repo_name         | The name of your GitHub Repository for OIDC Connect                         | string       | n/a                                                                                                                  |   yes    |

## Notes

- If `tfc_sub_conditions` is empty, a default condition will be created using `tfc_organization_name` and `tfc_project_name`
- The OIDC provider requires the TLS certificate from the Terraform Cloud hostname
- The IAM role created will have permissions to assume roles in child AWS accounts
