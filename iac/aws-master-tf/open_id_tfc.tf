# Data source used to grab the TLS certificate for Terraform Cloud.
#
# https://registry.terraform.io/providers/hashicorp/tls/latest/docs/data-sources/certificate
data "tls_certificate" "tfc_certificate" {
  url = "https://${var.tfc_hostname}"
}

# Local variable to handle the sub conditions
locals {
  # Default sub condition if none provided
  default_sub_condition = "organization:${var.tfc_organization_name}:project:${var.tfc_project_name}:workspace:*:*:*"

  # Use provided sub conditions or default if empty
  tfc_sub_conditions = length(var.tfc_sub_conditions) > 0 ? var.tfc_sub_conditions : [local.default_sub_condition]
}

# Creates an OIDC provider which is restricted to
#
# https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_openid_connect_provider
resource "aws_iam_openid_connect_provider" "tfc_provider" {
  url             = data.tls_certificate.tfc_certificate.url
  client_id_list  = [var.tfc_aws_audience]
  thumbprint_list = [data.tls_certificate.tfc_certificate.certificates[0].sha1_fingerprint]

  lifecycle {
    ignore_changes = [thumbprint_list]
  }
}

# Creates a role which can only be used by the specified Terraform
# cloud workspace.
#
# https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role
resource "aws_iam_role" "tfc_role" {
  name = "tfc-role"

  # Use jsonencode instead of heredoc for better dynamic content handling
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Federated = aws_iam_openid_connect_provider.tfc_provider.arn
        },
        Action = "sts:AssumeRoleWithWebIdentity",
        Condition = {
          StringEquals = {
            "${var.tfc_hostname}:aud" = one(aws_iam_openid_connect_provider.tfc_provider.client_id_list)
          },
          # When using jsonencode with an array value, AWS automatically handles it as a logical OR
          # This means the condition is satisfied if ANY of the values match
          StringLike = {
            "${var.tfc_hostname}:sub" = local.tfc_sub_conditions
          }
        }
      }
    ]
  })
}

# Creates a policy that will be used to only allow assume role to target account role
# the previously created role has within AWS.
#
#https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy
resource "aws_iam_policy" "tfc_policy" {
  # Create the policy only if there are created accounts
  count = length(local.account_arns) > 0 ? 1 : 0

  name        = "tfc-policy"
  description = "TFC run policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect   = "Allow"
        Action   = "sts:AssumeRole"
        Resource = local.account_arns
      },
    ]
  })
}


# Creates an attachment to associate the above policy with the
# previously created role.
#
# https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment
resource "aws_iam_role_policy_attachment" "tfc_policy_attachment" {
  count      = length(aws_iam_policy.tfc_policy) > 0 ? 1 : 0
  role       = aws_iam_role.tfc_role.name
  policy_arn = aws_iam_policy.tfc_policy[0].arn
}
