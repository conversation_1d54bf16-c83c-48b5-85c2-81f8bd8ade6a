resource "aws_organizations_account" "this" {
  for_each = local.accounts

  name                       = each.value.name
  email                      = each.value.email
  role_name                  = try(each.value.role_name, local.account_role) # Default role name
  close_on_deletion          = try(each.value.close_on_deletion, false)      # Default to false if not specified
  iam_user_access_to_billing = try(each.value.iam_user_access_to_billing ? "ALLOW" : "DENY", "ALLOW")

  lifecycle {
    //ignore changes if role modified
    ignore_changes = [role_name, iam_user_access_to_billing]
  }
}

output "organization_accounts" {
  value       = aws_organizations_account.this
  description = "The IDs and names of the created AWS Organization accounts."
}

