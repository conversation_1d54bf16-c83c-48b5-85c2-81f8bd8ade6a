# https://docs.github.com/en/actions/deployment/security-hardening-your-deployments/configuring-openid-connect-in-amazon-web-services
data "tls_certificate" "github_action_certificate" {
  url = "https://token.actions.githubusercontent.com"
}

resource "aws_iam_openid_connect_provider" "github_action_provider" {
  url             = data.tls_certificate.github_action_certificate.url
  client_id_list  = ["sts.amazonaws.com"]
  thumbprint_list = [data.tls_certificate.github_action_certificate.certificates[0].sha1_fingerprint]

  lifecycle {
    ignore_changes = [thumbprint_list]
  }
}

#https://docs.github.com/en/actions/deployment/security-hardening-your-deployments/configuring-openid-connect-in-amazon-web-services#configuring-the-role-and-trust-policy
resource "aws_iam_role" "github_action_role" {
  name = "github-action-role"

  assume_role_policy = <<EOF
{
 "Version": "2012-10-17",
 "Statement": [
   {
     "Effect": "Allow",
     "Principal": {
       "Federated": "${aws_iam_openid_connect_provider.github_action_provider.arn}"
     },
     "Action": "sts:AssumeRoleWithWebIdentity",
     "Condition": {
       "StringEquals": {
         "token.actions.githubusercontent.com:aud": "${one(aws_iam_openid_connect_provider.github_action_provider.client_id_list)}"
       },
       "StringLike": {
         "token.actions.githubusercontent.com:sub": "repo:${var.github_organization_name}/${var.github_repo_name}:ref:refs/heads/main"
       }
     }
   }
 ]
}
EOF
}

# Creates a policy that will be used to only allow assume role to target account role
# the previously created role has within AWS.
resource "aws_iam_policy" "github_action_policy" {
  # Create the policy only if there are created accounts
  count = length(local.account_arns) > 0 ? 1 : 0

  name        = "github-action-policy"
  description = "Github Action run policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect   = "Allow"
        Action   = "sts:AssumeRole"
        Resource = local.account_arns
      },
    ]
  })
}

# Creates an attachment to associate the above policy with the
# previously created role.
#
resource "aws_iam_role_policy_attachment" "github_action_policy_attachment" {
  count      = length(aws_iam_policy.github_action_policy) > 0 ? 1 : 0
  role       = aws_iam_role.github_action_role.name
  policy_arn = aws_iam_policy.github_action_policy[0].arn
}
