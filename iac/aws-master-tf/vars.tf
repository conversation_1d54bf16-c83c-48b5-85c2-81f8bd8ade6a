variable "tfc_aws_audience" {
  type        = string
  default     = "aws.workload.identity"
  description = "The audience value to use in run identity tokens"
}

variable "tfc_hostname" {
  type        = string
  default     = "app.terraform.io"
  description = "The hostname of the TFC or TFE instance you'd like to use with AWS"
}

variable "tfc_organization_name" {
  type        = string
  description = "The name of your Terraform Cloud organization"
}

variable "tfc_project_name" {
  type        = string
  description = "Name for Terraform project that grant access by this AWS master sub account"
}

variable "tfc_sub_conditions" {
  type        = list(string)
  description = "List of sub conditions for Terraform Cloud OIDC provider"
  default = [
    "organization:anchor-sprint:project:AWS-Project:workspace:*:*:*",
    "organization:grolier:project:*:workspace:*:*:*"
  ]
}

variable "github_organization_name" {
  type        = string
  description = "The name of your GitHub organization name for OIDC Connect"
}

variable "github_repo_name" {
  type        = string
  description = "The name of your GitHub Repository for OIDC Connect"
}
