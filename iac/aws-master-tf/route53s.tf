locals {
  route53_config = try(yamldecode(file("${path.module}/configs/route53s.yaml")), {})
}

locals {
  all_records = flatten([
    for zone_key, zone_detail in local.route53_config : [
      for record_key, record_detail in try(zone_detail["records"], {})
      : merge(
        record_detail,
        {
          id       = "${record_key}-${zone_detail.name}-${record_detail.type}"
          zone_key = zone_key
        }
      )
    ]
  ])
}

resource "aws_route53_zone" "this" {
  for_each = local.route53_config

  name          = each.value.name
  comment       = try(each.value.comment, "Manage by Terraform")
  force_destroy = try(each.value.force_destroy, false)
  tags          = try(each.value.tags, null)
}

resource "aws_route53_record" "this" {
  for_each = { for rec in local.all_records : rec.id => rec }

  zone_id = aws_route53_zone.this[each.value.zone_key].id
  name    = try("${each.value.name}.${aws_route53_zone.this[each.value.zone_key].name}", "") # Keep DNS record name standard
  type    = try(each.value.type, null)
  ttl     = try(each.value.alias, null) == null ? try(each.value.ttl, 300) : null
  records = try(each.value.values, null)

  # Dynamically add an alias block if conditions are met
  dynamic "alias" {
    for_each = try(each.value.alias, null) != null ? [1] : []

    content {
      name                   = try(each.value.alias.name, null)
      zone_id                = try(each.value.alias.zone_id, null)
      evaluate_target_health = try(each.value.alias.evaluate_target_health, false)
    }
  }
}
