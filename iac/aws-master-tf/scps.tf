locals {
  // Load and decode the SCP configurations from a YAML file
  service_control_policies = try(yamldecode(file("${path.module}/configs/scps.yaml")), {})
}

resource "aws_organizations_policy" "this" {
  for_each = local.service_control_policies

  // Use the policy's key as the name of the SCP
  name = each.key
  // Set description if available, otherwise default to a generic message
  description = try(each.value.description, "Service Control Policy for ${each.key}")

  // Generate the policy content as JSON from the YAML content block
  content = jsonencode({
    Version   = try(each.value.content.Version, "2012-10-17")
    Statement = each.value.content.Statement
  })
}

resource "aws_organizations_policy_attachment" "this" {
  # Constructing a flat map with static keys for 'for_each'
  for_each = toset(flatten([
    for account_key, account in local.accounts : [
      for scp_name in try(account.scp_ids, []) :
      "${account_key}:${scp_name}"
      if contains(keys(local.service_control_policies), scp_name)
    ]
  ]))

  # Extracting account_id and policy_name from each.key
  policy_id = aws_organizations_policy.this[split(":", each.key)[1]].id
  target_id = aws_organizations_account.this[split(":", each.key)[0]].id
}

