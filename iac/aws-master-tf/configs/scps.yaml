limit-ec2-instance-types:
  description: "Limit EC2 instance types"
  content:
    Statement:
      - Sid: "LimitEC2InstanceTypes"
        Effect: "Deny"
        Action: "ec2:RunInstances"
        Resource: "arn:aws:ec2:*:*:instance/*"
        Condition:
          StringNotEquals:
            ec2:InstanceType:
              - "t2.micro"
              - "t2.small"
              - "t3.micro"
              - "t3.small"
              - "t3.medium"

      - Sid: "LimitSimultaneousInstances"
        Effect: "Deny"
        Action: "ec2:RunInstances"
        Resource: "arn:aws:ec2:*:*:instance/*"
        Condition:
          NumericGreaterThan:
            ec2:InstanceCount: "2"

deny-non-my-sg-regions:
  description: "Deny any resources not in Malaysia and Singapore regions"
  content:
    Statement:
      - Sid: "DenyNonMalaysiaSingaporeRegions"
        Effect: "Deny"
        Action:
          - "ec2:Run*"
          - "ec2:Create*"
          - "s3:Create*"
          - "lambda:Create*"
          - "rds:Create*"
          - "elasticloadbalancing:Create*"
          - "elasticache:Create*"
          - "redshift:Create*"
          - "dynamodb:Create*"
          - "kinesis:Create*"
          - "glue:Create*"
          - "sns:Create*"
          - "sqs:Create*"
          - "cloudwatch:Create*"
          - "logs:Create*"
          - "events:Create*"
          - "codebuild:Create*"
          - "codepipeline:Create*"
          - "codedeploy:Create*"
          - "eks:Create*"
          - "ecs:Create*"
          - "batch:Create*"
          - "appstream:Create*"
          - "workspaces:Create*"
          - "autoscaling:Create*"
          - "fsx:Create*"
          - "iot:Create*"
          - "lightsail:Create*"
          - "sagemaker:Create*"
          - "elasticbeanstalk:Create*"
          - "gamelift:Create*"
          - "transcribe:Create*"
          - "comprehend:Create*"
          - "rekognition:Create*"
          - "mediaconvert:Create*"
          - "medialive:Create*"
          - "mediapackage:Create*"
          - "mediastore:Create*"
          - "timestream:Create*"
          - "quicksight:Create*"
          - "dataexchange:Create*"
          - "athena:Create*"
          - "guardduty:Create*"
          - "detective:Create*"
          - "securityhub:Create*"
          - "backup:Create*"
          - "transfer:Create*"
          - "outposts:Create*"
          - "cloudhsm:Create*"
          - "kendra:Create*"
          - "finspace:Create*"
          - "memorydb:Create*"
          - "panorama:Create*"
          - "lookoutvision:Create*"
          - "forecast:Create*"
          - "frauddetector:Create*"
          - "personalize:Create*"
          - "bedrock:Create*"
          - "ivs:Create*"
          - "appmesh:Create*"
          - "resiliencehub:Create*"
          - "access-analyzer:Create*"
          - "iotanalytics:Create*"
          - "greengrass:Create*"
          - "robomaker:Create*"
          - "deepracer:Create*"
          - "braket:Create*"
          - "snowball:Create*"
          - "groundstation:Create*"
          - "appconfig:Create*"
          - "amplify:Create*"
          - "backup-gateway:Create*"
          - "drs:Create*"
          - "migrationhub-orchestrator:Create*"
          - "mgn:Create*"
          - "cloudshell:Create*"
          - "refactor-spaces:Create*"
          - "support:Create*"
          - "license-manager:Create*"
          - "states:Create*"
          - "cloudformation:Create*"
          - "es:Create*"
          - "macie2:Create*"
          - "docdb-elastic:Create*"
          - "neptune-db:Create*"
        Resource: "*"
        Condition:
          StringNotEquals:
            aws:RequestedRegion:
              - "ap-southeast-1"
              - "ap-southeast-5"
