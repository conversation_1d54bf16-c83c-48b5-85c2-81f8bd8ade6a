# Example ALB configurations

web_alb:
  env: prod
  app_name: web
  internal: false
  enable_deletion_protection: true
  tags:
    Environment: Production
    Service: WebApp
    ManagedBy: Terraform
  
  # HTTP listener configuration
  create_http_listener: true
  http_listener_type: redirect  # Redirect HTTP to HTTPS
  
  # HTTPS listener configuration
  create_https_listener: true
  certificate_arn: "arn:aws:acm:ap-southeast-1:123456789012:certificate/example-cert"
  
  # Health check settings
  health_check_path: "/health"
  health_check_interval: 30
  health_check_timeout: 5
  health_check_healthy_threshold: 3
  health_check_unhealthy_threshold: 3
  health_check_matcher: "200-299"
  
  # Access logs
  access_logs_bucket: "example-logs-bucket"
  access_logs_prefix: "alb-logs/web"

api_alb:
  env: prod
  app_name: api
  internal: true
  enable_deletion_protection: true
  tags:
    Environment: Production
    Service: API
    ManagedBy: Terraform
  
  # Custom security settings
  allowed_cidr_blocks:
    - "10.0.0.0/8"
    - "**********/12"
    - "***********/16"
  
  # Target group settings
  target_group_port: 8080
  target_group_protocol: "HTTP"
  target_type: "ip"
  
  # Health check settings
  health_check_path: "/api/health"
  health_check_port: "8080"
  health_check_protocol: "HTTP"
  health_check_interval: 15
  health_check_timeout: 10
  health_check_healthy_threshold: 2
  health_check_unhealthy_threshold: 5
  health_check_matcher: "200"
  
  # Access logs
  access_logs_bucket: "example-logs-bucket"
  access_logs_prefix: "alb-logs/api"
