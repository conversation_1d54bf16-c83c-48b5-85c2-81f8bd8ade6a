provider "aws" {
  region  = "ap-southeast-1"
  profile = "playground"
}

# Load ALB configurations from YAML
locals {
  albs_config = yamldecode(file("${path.module}/configs/albs.yaml"))
}

# Create ALBs from YAML configuration
module "albs" {
  source   = "../../../modules/alb"
  for_each = local.albs_config
  
  # Required parameters
  env      = each.value.env
  app_name = each.value.app_name
  
  # Optional parameters with defaults
  internal                  = try(each.value.internal, false)
  vpc_id                    = try(each.value.vpc_id, null)
  subnet_ids                = try(each.value.subnet_ids, null)
  security_group_ids        = try(each.value.security_group_ids, null)
  allowed_cidr_blocks       = try(each.value.allowed_cidr_blocks, ["0.0.0.0/0"])
  enable_deletion_protection = try(each.value.enable_deletion_protection, false)
  idle_timeout              = try(each.value.idle_timeout, 60)
  drop_invalid_header_fields = try(each.value.drop_invalid_header_fields, true)
  enable_http2              = try(each.value.enable_http2, true)
  
  # Access logs
  access_logs_bucket        = try(each.value.access_logs_bucket, null)
  access_logs_prefix        = try(each.value.access_logs_prefix, null)
  
  # HTTP listener
  create_http_listener      = try(each.value.create_http_listener, true)
  http_listener_type        = try(each.value.http_listener_type, "redirect")
  fixed_response_content    = try(each.value.fixed_response_content, "OK")
  
  # HTTPS listener
  create_https_listener     = try(each.value.create_https_listener, true)
  certificate_arn           = try(each.value.certificate_arn, null)
  ssl_policy                = try(each.value.ssl_policy, "ELBSecurityPolicy-2016-08")
  
  # Target group
  default_target_group_arn  = try(each.value.default_target_group_arn, null)
  create_default_target_group = try(each.value.create_default_target_group, true)
  target_group_port         = try(each.value.target_group_port, 80)
  target_group_protocol     = try(each.value.target_group_protocol, "HTTP")
  target_type               = try(each.value.target_type, "instance")
  
  # Health check
  health_check_interval     = try(each.value.health_check_interval, 30)
  health_check_path         = try(each.value.health_check_path, "/")
  health_check_port         = try(each.value.health_check_port, "traffic-port")
  health_check_protocol     = try(each.value.health_check_protocol, "HTTP")
  health_check_timeout      = try(each.value.health_check_timeout, 5)
  health_check_healthy_threshold = try(each.value.health_check_healthy_threshold, 3)
  health_check_unhealthy_threshold = try(each.value.health_check_unhealthy_threshold, 3)
  health_check_matcher      = try(each.value.health_check_matcher, "200-299")
  
  # Tags
  tags                      = try(each.value.tags, {})
}

# Output the DNS names of all ALBs
output "alb_dns_names" {
  value = {
    for k, v in module.albs : k => v.alb_dns_name
  }
}

# Output the ARNs of all ALBs
output "alb_arns" {
  value = {
    for k, v in module.albs : k => v.alb_arn
  }
}
