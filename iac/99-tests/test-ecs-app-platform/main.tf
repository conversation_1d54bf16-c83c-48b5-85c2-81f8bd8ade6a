provider "aws" {
  region  = "ap-southeast-1"
  profile = "playground"
}

module "api_platform" {
  source   = "../../../modules/ecs_app_platform"
  env      = "test"
  app_name = "api"
  
  # Configure multiple services with different paths
  services_config = {
    # User API Service
    users = {
      container_image = "nginx:latest"  # Using nginx as a placeholder
      container_port  = 80
      path_patterns   = ["/api/users*"]
      health_check_path = "/api/users/health"
      task_cpu        = 256
      task_memory     = 512
    },
    
    # Products API Service
    products = {
      container_image = "nginx:latest"  # Using nginx as a placeholder
      container_port  = 80
      path_patterns   = ["/api/products*"]
      health_check_path = "/api/products/health"
      task_cpu        = 256
      task_memory     = 512
      environment_variables = {
        SERVICE_NAME = "products"
        LOG_LEVEL = "info"
      }
    },
    
    # Frontend Web Service
    web = {
      container_image = "nginx:latest"  # Using nginx as a placeholder
      container_port  = 80
      path_patterns   = ["/*"]
      listener_rule_priority = 200  # Lower priority (higher number) than API services
      health_check_path = "/health"
    }
  }
  
  # Default settings for all services
  default_assign_public_ip = true  # For testing in default VPC
  default_desired_count    = 1
  
  tags = {
    Environment = "Test"
    ManagedBy   = "Terraform"
    Project     = "ECS App Platform Testing"
  }
}

# Output the ALB DNS name
output "alb_dns_name" {
  value = module.api_platform.alb_dns_name
}

# Output the service details
output "services" {
  value = module.api_platform.services
}

# Output the target groups
output "target_groups" {
  value = module.api_platform.target_groups
}

# Output the log groups
output "log_groups" {
  value = module.api_platform.log_groups
}
