provider "aws" {
  region  = "ap-southeast-1"
  profile = "playground"
}

# Load services configuration from YAML
locals {
  services_config = yamldecode(file("${path.module}/configs/services.yaml"))
}

module "api_platform" {
  source   = "../../../modules/ecs_app_platform"
  env      = "test"
  app_name = "api"
  
  # Use services configuration from YAML
  services_config = local.services_config
  
  # Default settings for all services
  default_assign_public_ip = true  # For testing in default VPC
  
  tags = {
    Environment = "Test"
    ManagedBy   = "Terraform"
    Project     = "ECS App Platform YAML Testing"
  }
}

# Output the ALB DNS name
output "alb_dns_name" {
  value = module.api_platform.alb_dns_name
}

# Output the service details
output "services" {
  value = module.api_platform.services
}

# Output the target groups
output "target_groups" {
  value = module.api_platform.target_groups
}

# Output the log groups
output "log_groups" {
  value = module.api_platform.log_groups
}
