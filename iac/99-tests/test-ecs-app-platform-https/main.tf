provider "aws" {
  region  = "ap-southeast-1"
  profile = "playground"
}

# Load services configuration from YAML
locals {
  services_config = yamldecode(file("${path.module}/configs/services.yaml"))
}

module "api_platform" {
  source   = "../../../modules/ecs_app_platform"
  env      = "test"
  app_name = "api"

  # Use services configuration from YAML
  services_config = local.services_config

  # HTTPS Configuration
  create_https_listener = true
  certificate_arn       = "arn:aws:acm:ap-southeast-1:123456789012:certificate/example-cert" # Replace with actual certificate ARN
  additional_certificate_arns = [
    "arn:aws:acm:ap-southeast-1:123456789012:certificate/additional-cert" # Replace with actual certificate ARN
  ]
  ssl_policy = "ELBSecurityPolicy-TLS13-1-2-2021-06"

  # HTTP to HTTPS redirect
  create_http_listener = true
  http_listener_type   = "redirect"

  # Use HTTPS for target groups (if your containers support HTTPS)
  use_https_for_target_groups = true

  # Default settings for all services
  default_assign_public_ip = true # For testing in default VPC

  tags = {
    Environment = "Test"
    ManagedBy   = "Terraform"
    Project     = "ECS App Platform HTTPS Testing"
  }
}

# Output the ALB DNS name
output "alb_dns_name" {
  value = module.api_platform.alb_dns_name
}

# Output the service details
output "services" {
  value = module.api_platform.services
}

# Output the target groups
output "target_groups" {
  value = module.api_platform.target_groups
}

# Output the log groups
output "log_groups" {
  value = module.api_platform.log_groups
}
