# ECS Services Configuration for HTTPS

# User API Service
users:
  container_image: nginx:latest  # Using nginx as a placeholder
  container_port: 443  # Using HTTPS port
  path_patterns:
    - "/api/users*"
  health_check_path: "/api/users/health"
  task_cpu: 256
  task_memory: 512
  environment_variables:
    SERVICE_NAME: users
    LOG_LEVEL: info
    ENABLE_HTTPS: "true"
  desired_count: 1
  tags:
    Service: Users API

# Products API Service
products:
  container_image: nginx:latest  # Using nginx as a placeholder
  container_port: 443  # Using HTTPS port
  path_patterns:
    - "/api/products*"
  health_check_path: "/api/products/health"
  task_cpu: 512
  task_memory: 1024
  environment_variables:
    SERVICE_NAME: products
    LOG_LEVEL: info
    DB_HOST: products-db.example.com
    ENABLE_HTTPS: "true"
  desired_count: 2
  tags:
    Service: Products API

# Frontend Web Service
web:
  container_image: nginx:latest  # Using nginx as a placeholder
  container_port: 443  # Using HTTPS port
  path_patterns:
    - "/*"
  listener_rule_priority: 999  # Lowest priority (highest number)
  health_check_path: "/health"
  task_cpu: 256
  task_memory: 512
  environment_variables:
    SERVICE_NAME: web
    LOG_LEVEL: info
    ENABLE_HTTPS: "true"
  desired_count: 2
  tags:
    Service: Web Frontend
