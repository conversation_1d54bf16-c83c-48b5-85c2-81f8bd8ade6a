provider "aws" {
  region = "ap-southeast-1"
}

# Basic ALB with default settings
module "basic_alb" {
  source   = "../../../modules/alb"
  env      = "test"
  app_name = "basic"

  tags = {
    Environment = "Test"
    ManagedBy   = "Terraform"
    Project     = "ALB Module Testing"
  }
}

# Internal ALB with custom settings
module "internal_alb" {
  source   = "../../../modules/alb"
  env      = "test"
  app_name = "internal"
  internal = true

  # Custom health check settings
  health_check_path     = "/health"
  health_check_interval = 20
  health_check_timeout  = 10

  tags = {
    Environment = "Test"
    ManagedBy   = "Terraform"
    Project     = "ALB Module Testing"
    Type        = "Internal"
  }
}

# Output the DNS names of the ALBs
output "basic_alb_dns_name" {
  value = module.basic_alb.alb_dns_name
}

output "internal_alb_dns_name" {
  value = module.internal_alb.alb_dns_name
}

# Output the security group IDs
output "basic_alb_security_group_id" {
  value = module.basic_alb.security_group_id
}

output "internal_alb_security_group_id" {
  value = module.internal_alb.security_group_id
}
