# ACM Certificate Configuration
# This file creates and validates an ACM certificate for both the main domain and www subdomain
# The certificate is used by CloudFront to serve HTTPS traffic for both domains
resource "aws_acm_certificate" "cert" {
  provider                  = aws.aws_cloudfront # Use the CloudFront provider (us-east-1)
  domain_name               = "s3test.dev.anchorsprint.com"
  subject_alternative_names = ["www.s3test.dev.anchorsprint.com"]
  validation_method         = "DNS"

  lifecycle {
    create_before_destroy = true
  }

  tags = var.tags
}

# Output the certificate domain names for verification
output "certificate_domain_names" {
  value       = [aws_acm_certificate.cert.domain_name, tolist(aws_acm_certificate.cert.subject_alternative_names)]
  description = "The domain names covered by the certificate"
}

# Create DNS records for certificate validation
resource "aws_route53_record" "cert_validation" {
  for_each = {
    for dvo in aws_acm_certificate.cert.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  }

  zone_id = data.aws_route53_zone.selected.zone_id
  name    = each.value.name
  type    = each.value.type
  records = [each.value.record]
  ttl     = 60
}

# Validate the certificate
resource "aws_acm_certificate_validation" "cert" {
  provider                = aws.aws_cloudfront # Use the CloudFront provider (us-east-1)
  certificate_arn         = aws_acm_certificate.cert.arn
  validation_record_fqdns = [for record in aws_route53_record.cert_validation : record.fqdn]
}

# Output the certificate ARN
output "certificate_arn" {
  value       = aws_acm_certificate.cert.arn
  description = "The ARN of the ACM certificate"
}

# Output the certificate validation status
output "certificate_status" {
  value       = aws_acm_certificate.cert.status
  description = "The status of the ACM certificate"
}
