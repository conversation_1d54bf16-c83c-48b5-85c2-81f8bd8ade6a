# Test module for the cf_s3_website module
# This test demonstrates how to use the module with www redirection using CloudFront Functions
# It creates a custom ACM certificate that covers both the main domain and www subdomain
# and sets up Route 53 records for both domains

provider "aws" {
  region  = "ap-southeast-1" # Main region for S3 bucket
  profile = "playground"
}

provider "aws" {
  region  = "us-east-1" # Required for CloudFront and ACM
  alias   = "aws_cloudfront"
  profile = "playground"
}

# Test configuration for the cf_s3_website module
# This configuration tests the www redirection feature using CloudFront Functions
# It creates a custom ACM certificate that covers both the main domain and www subdomain
module "cloudfront_s3_website_with_domain" {
  source                 = "../../../modules/cf_s3_website"
  domain_name            = "s3test.dev.anchorsprint.com"
  acm_certificate_domain = "s3test.dev.anchorsprint.com" # Use the new certificate domain
  use_default_domain     = false                         # Use ACM certificate for custom domain
  upload_sample_file     = true                          # Upload sample files to the S3 bucket
  enable_www_redirect    = true                          # Enable www redirection for testing
  tags                   = var.tags

  cloudfront_min_ttl       = 10
  cloudfront_default_ttl   = 1400
  cloudfront_max_ttl       = 86400
  minimum_protocol_version = "TLSv1.2_2021" # Use the latest TLS protocol version

  providers = {
    aws.aws_cloudfront = aws.aws_cloudfront
  }

  depends_on = [aws_acm_certificate_validation.cert] # Ensure certificate is validated first
}

variable "tags" {
  default = {
    env = "playground"
  }
}

# Outputs for testing the cf_s3_website module

# Main domain outputs
output "domain" {
  value       = module.cloudfront_s3_website_with_domain.website_address
  description = "The website address (domain name)"
}

output "s3_bucket_name" {
  value       = module.cloudfront_s3_website_with_domain.s3_bucket_name
  description = "The name of the S3 bucket for the main domain"
}

output "cf_domain_name" {
  value       = module.cloudfront_s3_website_with_domain.cloudfront_domain_name
  description = "The CloudFront domain name for the main domain"
}

# WWW redirection outputs
output "www_redirect_enabled" {
  value       = module.cloudfront_s3_website_with_domain.www_redirect_enabled
  description = "Whether www redirection is enabled"
}

output "www_redirect_function_name" {
  value       = module.cloudfront_s3_website_with_domain.www_redirect_function_name
  description = "Name of the CloudFront function used for www redirection"
}

output "www_redirect_function_arn" {
  value       = module.cloudfront_s3_website_with_domain.www_redirect_function_arn
  description = "ARN of the CloudFront function used for www redirection"
}

# Route 53 configuration to link the domain to CloudFront
data "aws_route53_zone" "selected" {
  name         = "dev.anchorsprint.com."
  private_zone = false
}

# Create Route 53 record for the main domain
resource "aws_route53_record" "main" {
  zone_id = data.aws_route53_zone.selected.zone_id
  name    = "s3test.dev.anchorsprint.com"
  type    = "A"

  alias {
    name                   = module.cloudfront_s3_website_with_domain.cloudfront_domain_name
    zone_id                = "Z2FDTNDATAQYW2" # CloudFront's hosted zone ID (always the same)
    evaluate_target_health = false
  }
}

# Create Route 53 record for the www subdomain
resource "aws_route53_record" "www" {
  zone_id = data.aws_route53_zone.selected.zone_id
  name    = "www.s3test.dev.anchorsprint.com"
  type    = "A"

  alias {
    name                   = module.cloudfront_s3_website_with_domain.cloudfront_domain_name
    zone_id                = "Z2FDTNDATAQYW2" # CloudFront's hosted zone ID (always the same)
    evaluate_target_health = false
  }
}

# Output the Route 53 records
output "route53_main_record" {
  value       = aws_route53_record.main.fqdn
  description = "The fully qualified domain name of the main domain"
}

output "route53_www_record" {
  value       = aws_route53_record.www.fqdn
  description = "The fully qualified domain name of the www subdomain"
}
