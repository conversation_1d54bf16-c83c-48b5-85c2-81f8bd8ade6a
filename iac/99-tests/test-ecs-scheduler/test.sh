#!/bin/bash
set -e

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting ECS Scheduler Test${NC}"

# Initialize and apply Terraform
echo -e "${YELLOW}Initializing Terraform...${NC}"
terraform init

echo -e "${YELLOW}Applying Terraform configuration...${NC}"
terraform apply -auto-approve

# Get outputs
CLUSTER_NAME=$(terraform output -raw cluster_name)
SERVICE_NAME=$(terraform output -raw service_name)
LAMBDA_FUNCTION_NAME=$(terraform output -raw lambda_function_name)

echo -e "${YELLOW}Test resources created:${NC}"
echo "Cluster: $CLUSTER_NAME"
echo "Service: $SERVICE_NAME"
echo "Lambda: $LAMBDA_FUNCTION_NAME"

# Check initial service state
echo -e "${YELLOW}Checking initial service state...${NC}"
INITIAL_COUNT=$(aws ecs describe-services --cluster $CLUSTER_NAME --services $SERVICE_NAME --query 'services[0].desiredCount' --output text)
echo "Initial desired count: $INITIAL_COUNT"

if [ "$INITIAL_COUNT" -eq "1" ]; then
  echo -e "${GREEN}✓ Initial state check passed${NC}"
else
  echo -e "${RED}✗ Initial state check failed - expected 1, got $INITIAL_COUNT${NC}"
  exit 1
fi

# Test scale down
echo -e "${YELLOW}Testing scale down...${NC}"
aws lambda invoke --function-name $LAMBDA_FUNCTION_NAME --payload '{"action":"scale_down","desiredCount":0}' --cli-binary-format raw-in-base64-out response.json
cat response.json
rm response.json

# Wait for the service to scale down
echo -e "${YELLOW}Waiting for service to scale down...${NC}"
sleep 10

# Check service state after scale down
SCALED_DOWN_COUNT=$(aws ecs describe-services --cluster $CLUSTER_NAME --services $SERVICE_NAME --query 'services[0].desiredCount' --output text)
echo "Desired count after scale down: $SCALED_DOWN_COUNT"

if [ "$SCALED_DOWN_COUNT" -eq "0" ]; then
  echo -e "${GREEN}✓ Scale down test passed${NC}"
else
  echo -e "${RED}✗ Scale down test failed - expected 0, got $SCALED_DOWN_COUNT${NC}"
  exit 1
fi

# Test scale up
echo -e "${YELLOW}Testing scale up...${NC}"
aws lambda invoke --function-name $LAMBDA_FUNCTION_NAME --payload '{"action":"scale_up","desiredCount":1}' --cli-binary-format raw-in-base64-out response.json
cat response.json
rm response.json

# Wait for the service to scale up
echo -e "${YELLOW}Waiting for service to scale up...${NC}"
sleep 15

# Check service state after scale up
SCALED_UP_COUNT=$(aws ecs describe-services --cluster $CLUSTER_NAME --services $SERVICE_NAME --query 'services[0].desiredCount' --output text)
echo "Desired count after scale up: $SCALED_UP_COUNT"

if [ "$SCALED_UP_COUNT" -eq "1" ]; then
  echo -e "${GREEN}✓ Scale up test passed${NC}"
else
  echo -e "${RED}✗ Scale up test failed - expected 1, got $SCALED_UP_COUNT${NC}"
  exit 1
fi

echo -e "${GREEN}All tests passed!${NC}"

# Cleanup
echo -e "${YELLOW}Cleaning up resources...${NC}"
terraform destroy -auto-approve

echo -e "${GREEN}Test completed successfully${NC}"
