output "cluster_name" {
  description = "The name of the test ECS cluster"
  value       = aws_ecs_cluster.test_cluster.name
}

output "service_name" {
  description = "The name of the test ECS service"
  value       = aws_ecs_service.test_service.name
}

output "scale_down_schedule_arn" {
  description = "The ARN of the scale down schedule"
  value       = module.ecs_scheduler.scale_down_schedule_arn
}

output "scale_up_schedule_arn" {
  description = "The ARN of the scale up schedule"
  value       = module.ecs_scheduler.scale_up_schedule_arn
}

output "scheduler_role_arn" {
  description = "The ARN of the scheduler IAM role"
  value       = module.ecs_scheduler.scheduler_role_arn
}

output "lambda_function_arn" {
  description = "The ARN of the Lambda function"
  value       = module.ecs_scheduler.lambda_function_arn
}

output "lambda_function_name" {
  description = "The name of the Lambda function"
  value       = module.ecs_scheduler.lambda_function_name
}

output "service_desired_count" {
  description = "The desired count of the test service"
  value       = aws_ecs_service.test_service.desired_count
}
