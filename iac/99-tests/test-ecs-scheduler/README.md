# ECS Scheduler Module Test

This test module verifies the functionality of the ECS scheduler module. It creates a mock ECS cluster and service, then applies the scheduler module to test its behavior.

## What This Test Does

1. Creates a mock ECS cluster and service with the tag `schedule=true`
2. Applies the ECS scheduler module to the cluster
3. Tests the Lambda function's ability to scale services up and down based on tags

## How to Run the Test

### Automated Test

```bash
# Navigate to the test directory
cd iac/99-tests/test-ecs-scheduler

# Run the automated test script
./test.sh
```

The test script will:

1. Initialize and apply Terraform
2. Check the initial service state
3. Test scaling down by invoking the Lambda function
4. Test scaling up by invoking the Lambda function
5. Clean up resources

### Manual Testing

```bash
# Navigate to the test directory
cd iac/99-tests/test-ecs-scheduler

# Initialize Terraform
terraform init

# Run the test
terraform apply -auto-approve

# Verify the outputs
terraform output

# Test scaling down
aws lambda invoke --function-name $(terraform output -raw lambda_function_name) \
  --payload '{"action":"scale_down","desiredCount":0}' response.json

# Check the service desired count
aws ecs describe-services --cluster $(terraform output -raw cluster_name) \
  --services $(terraform output -raw service_name) \
  --query 'services[0].desiredCount'

# Test scaling up
aws lambda invoke --function-name $(terraform output -raw lambda_function_name) \
  --payload '{"action":"scale_up","desiredCount":1}' response.json

# Check the service desired count again
aws ecs describe-services --cluster $(terraform output -raw cluster_name) \
  --services $(terraform output -raw service_name) \
  --query 'services[0].desiredCount'

# Clean up resources
terraform destroy -auto-approve
```

## Expected Results

The test should create:

- A mock ECS cluster named "test-ecs-scheduler-cluster"
- A mock ECS service named "test-ecs-scheduler-service" with tag `schedule=true`
- A Lambda function that handles the scaling operations
- Two EventBridge scheduler schedules:
  - One for scaling down every 2 minutes
  - One for scaling up every 3 minutes
- IAM roles and policies for the scheduler and Lambda function

## Verification

The test script will automatically verify:

1. That the initial service desired count is 1
2. That the Lambda function can scale down the service to 0
3. That the Lambda function can scale up the service to 1

You can also manually verify the functionality by:

1. Checking the AWS Management Console:

   - Go to the Lambda service and check the function
   - Go to the EventBridge Scheduler service
   - Verify that the schedules are created with the correct configuration
   - Verify that the IAM roles and policies are created correctly

2. Checking the CloudWatch Logs:

   - Go to CloudWatch Logs
   - Find the log group for the Lambda function
   - Check the logs to see the function's execution details

3. Testing with multiple services:
   - Create another service without the `schedule=true` tag
   - Invoke the Lambda function
   - Verify that only the service with the tag is affected
