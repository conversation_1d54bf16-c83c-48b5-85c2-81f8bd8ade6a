terraform {
  required_version = ">= 1.0.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 4.0.0"
    }
  }
}

provider "aws" {
  region = "ap-southeast-1"
  # Use default credentials for testing
  default_tags {
    tags = {
      Environment = "test"
      ManagedBy   = "Terraform"
      Project     = "ECS Scheduler Test"
    }
  }
}

# Mock ECS cluster for testing
resource "aws_ecs_cluster" "test_cluster" {
  name = "test-ecs-scheduler-cluster"

  setting {
    name  = "containerInsights"
    value = "disabled"
  }

  tags = {
    Name = "test-ecs-scheduler-cluster"
  }
}

# Create IAM role for ECS task execution
resource "aws_iam_role" "execution_role" {
  name = "test-ecs-scheduler-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })

  tags = {
    Name = "test-ecs-scheduler-execution-role"
  }
}

# Create CloudWatch log group for ECS task
resource "aws_cloudwatch_log_group" "ecs_logs" {
  name              = "/ecs/test-ecs-scheduler"
  retention_in_days = 1

  tags = {
    Name = "test-ecs-scheduler-logs"
  }
}

# Attach the ECS task execution role policy
resource "aws_iam_role_policy_attachment" "execution_role_policy" {
  role       = aws_iam_role.execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

# Mock ECS task definition for testing
resource "aws_ecs_task_definition" "test_task" {
  family                   = "test-ecs-scheduler-task"
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = 256
  memory                   = 512
  execution_role_arn       = aws_iam_role.execution_role.arn

  container_definitions = jsonencode([
    {
      name      = "test-container"
      image     = "nginx:latest"
      essential = true

      portMappings = [
        {
          containerPort = 80
          hostPort      = 80
          protocol      = "tcp"
        }
      ]

      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-group"         = "/ecs/test-ecs-scheduler"
          "awslogs-region"        = "ap-southeast-1"
          "awslogs-stream-prefix" = "test"
        }
      }
    }
  ])

  tags = {
    Name = "test-ecs-scheduler-task"
  }
}

# Security group for ECS service
resource "aws_security_group" "ecs_service_sg" {
  name        = "test-ecs-scheduler-service-sg"
  description = "Security group for test ECS service"
  vpc_id      = data.aws_vpc.default.id

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "test-ecs-scheduler-service-sg"
  }
}

# Mock ECS service for testing
resource "aws_ecs_service" "test_service" {
  name            = "test-ecs-scheduler-service"
  cluster         = aws_ecs_cluster.test_cluster.id
  task_definition = aws_ecs_task_definition.test_task.arn
  launch_type     = "FARGATE"
  desired_count   = 1

  network_configuration {
    subnets          = data.aws_subnets.default.ids
    security_groups  = [aws_security_group.ecs_service_sg.id]
    assign_public_ip = true
  }

  tags = {
    Name     = "test-ecs-scheduler-service"
    schedule = "true"
  }
}

# Use the ECS scheduler module
module "ecs_scheduler" {
  source = "../../../modules/ecs_scheduler"

  env              = "test"
  app_name         = "scheduler"
  ecs_cluster_name = aws_ecs_cluster.test_cluster.name

  # Tag configuration
  tag_key   = "schedule"
  tag_value = "true"

  # Default desired count when scaling up
  default_desired_count = 1

  # Enable the scheduler
  enabled = true

  # Test schedule expressions (1 minute apart)
  scale_down_expression = "rate(2 minutes)"
  scale_up_expression   = "rate(3 minutes)"

  tags = {
    Name = "test-ecs-scheduler"
  }
}

# Data sources
data "aws_vpc" "default" {
  default = true
}

data "aws_subnets" "default" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.default.id]
  }
}

# Manual verification is required
# Check the outputs to verify that the resources were created correctly

# Outputs are defined in outputs.tf
