resource "aws_eip" "bastion" {
  tags = { Name = "Bastion IP" }
}

resource "aws_eip_association" "bastion_assoc" {
  instance_id   = module.bastion.output.instance_id
  allocation_id = aws_eip.bastion.id
}

module "bastion" {
  source        = "../../modules/ec2"
  instance_type = "t3a.nano"
}

output "bastion_instance_id" {
  value = module.bastion.output.instance_id
}

output "bastion_public_ip" {
  value = aws_eip.bastion.public_ip
}
