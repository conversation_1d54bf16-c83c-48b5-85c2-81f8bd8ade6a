// For cert query must be in us-east-1
provider "aws" {
  region = "us-east-1"
  assume_role {
    role_arn = "arn:aws:iam::${var.aws_provision_id}:role/${var.aws_provision_role}"
  }
  default_tags {
    tags = try(local.this.tags)
  }
  alias = "aws_cloudfront"
}

module "cloudfront_s3_website_with_domain" {
  source                 = "../../modules/cf_s3_website"
  domain_name            = "anchorsprint.com"
  acm_certificate_domain = "*.anchorsprint.com"
  use_default_domain     = false
  upload_sample_file     = false
  enable_www_redirect    = true  # Enable www.anchorsprint.com redirect to anchorsprint.com
  tags                   = var.tags

  cloudfront_min_ttl     = 10
  cloudfront_default_ttl = 1400
  cloudfront_max_ttl     = 86400

  providers = {
    aws.aws_cloudfront = aws.aws_cloudfront
  }
}

# Output information about the CloudFront function for www redirection
output "www_redirect_function_name" {
  value       = module.cloudfront_s3_website_with_domain.www_redirect_function_name
  description = "Name of the CloudFront function used for www redirection"
}

output "www_redirect_enabled" {
  value       = module.cloudfront_s3_website_with_domain.www_redirect_enabled
  description = "Whether www redirection is enabled"
}



variable "tags" {
  default = {
    env = "prod"
  }
}

output "domain" {
  value = module.cloudfront_s3_website_with_domain.website_address
}

output "cf_domain_name" {
  value = module.cloudfront_s3_website_with_domain.cloudfront_domain_name
}

# With the new implementation, the www subdomain is handled by the same CloudFront distribution
# as the main domain. You only need to create Route 53 records that point to the same CloudFront distribution.
# The CloudFront Function will handle redirecting www.anchorsprint.com to anchorsprint.com.
#
# Example Route 53 configuration:
#
# resource "aws_route53_record" "main" {
#   zone_id = "your-route53-zone-id"
#   name    = "anchorsprint.com"
#   type    = "A"
#
#   alias {
#     name                   = module.cloudfront_s3_website_with_domain.cloudfront_domain_name
#     zone_id                = "Z2FDTNDATAQYW2" # CloudFront's hosted zone ID
#     evaluate_target_health = false
#   }
# }
#
# resource "aws_route53_record" "www" {
#   zone_id = "your-route53-zone-id"
#   name    = "www.anchorsprint.com"
#   type    = "A"
#
#   alias {
#     name                   = module.cloudfront_s3_website_with_domain.cloudfront_domain_name
#     zone_id                = "Z2FDTNDATAQYW2" # CloudFront's hosted zone ID
#     evaluate_target_health = false
#   }
# }
