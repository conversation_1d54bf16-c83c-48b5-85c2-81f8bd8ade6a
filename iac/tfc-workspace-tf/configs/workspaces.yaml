defs:
  default: &default
    terraform_version: 1.7.5
    auto_apply: true
  variables:
    - &TFC_AWS_PROVIDER_AUTH
      key: TFC_AWS_PROVIDER_AUTH
      category: env
      value: true
    - &TFC_AWS_RUN_ROLE_ARN
      key: TFC_AWS_RUN_ROLE_ARN
      category: env
      value: arn:aws:iam::************:role/tfc-role
    - &aws_provision_role
      key: aws_provision_role
      category: terraform
      value: OrganizationAccountAccessRole

aws-master:
  <<: *default
  name: aws-master
  description: Manage all aws master account configuration
  working_directory: iac/aws-master-tf
  auto_apply: false
  project_id: aws-project
  trigger_prefixes:
    - iac/aws-master-tf
  vcs_repo:
    branch: main
    identifier: anchorsprint/company-master
    github_install_id: true
  variables:
    - key: tfc_organization_name
      category: terraform
      value: anchor-sprint
    - key: tfc_project_name
      category: terraform
      value: AWS-Project
    - key: github_organization_name
      category: terraform
      value: anchorsprint
    - key: github_repo_name
      category: terraform
      value: company-master

aws-company-prod:
  <<: *default
  name: aws-company-prod
  description: Manage all AWS resource for Company Prod environment
  working_directory: iac/aws-company-prod-tf
  auto_apply: false
  project_id: aws-project
  trigger_prefixes:
    - iac/aws-company-prod-tf
  vcs_repo:
    branch: main
    identifier: anchorsprint/company-master
    github_install_id: true
  variables:
    - *TFC_AWS_PROVIDER_AUTH
    - *TFC_AWS_RUN_ROLE_ARN
    - *aws_provision_role
    - key: aws_provision_id
      category: terraform
      value: 637423655351

aws-playground-dev:
  <<: *default
  name: aws-playground-dev
  description: Manage all AWS resource for Playground environment
  working_directory: iac/aws-playground-dev-tf
  auto_apply: true
  project_id: aws-project
  trigger_prefixes:
    - iac/aws-playground-dev-tf
  vcs_repo:
    branch: main
    identifier: anchorsprint/company-master
    github_install_id: true
  variables:
    - *TFC_AWS_PROVIDER_AUTH
    - *TFC_AWS_RUN_ROLE_ARN
    - *aws_provision_role
    - key: aws_provision_id # This is the AWS account ID to deleted access to
      category: terraform
      value: ************

aws-grolier-wrc-prod:
  name: aws-grolier-wrc-prod
  description: Manage all AWS resource for Grolier WRC production environment
  working_directory: grolier-wrc/prod
  auto_apply: false # Set to true if you want Terraform Cloud to automatically
  project_id: aws-grolier-project
  trigger_prefixes:
    - grolier-wrc/prod
    - grolier-wrc/core
  vcs_repo:
    branch: main
    identifier: anchorsprint/grolier-aws
    github_install_id: true

aws-invoice-extractor-uat:
  name: aws-invoice-extractor-uat
  description: Manage all invoice extractor resources for uat environment
  working_directory: iac/uat
  auto_apply: true
  project_id: aws-project
  trigger_prefixes:
    - iac/uat
  vcs_repo:
    branch: main
    identifier: anchorsprint/textract_poc
    bitbucket_auth_token_id: true
  variables:
    - *TFC_AWS_PROVIDER_AUTH
    - *TFC_AWS_RUN_ROLE_ARN
    - *aws_provision_role
    - key: aws_provision_id
      category: terraform
      value: ************

cloudflare-master:
  <<: *default
  name: cloudflare-master
  description: Manage Cloudflare resources for company site
  working_directory: iac/cloudflare-master-tf
  auto_apply: true
  project_id: cloudflare-project
  trigger_prefixes:
    - iac/cloudflare-master-tf
  vcs_repo:
    branch: main
    identifier: anchorsprint/company-master
    github_install_id: true
  variables:
    # This key will manually create in terraform cloud ui for security handling
    # - key: CLOUDFLARE_API_TOKEN
    #   category: env
    #   value: ""
    #   sensitive: true
    #   description: "Cloudflare API token with appropriate permissions"
    - key: CLOUDFLARE_ACCOUNT_ID
      category: env
      value: d10123ae33a533932d129c18b35f1b8d
      sensitive: true
      description: "Cloudflare account ID"
