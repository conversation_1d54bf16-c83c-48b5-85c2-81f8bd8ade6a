locals {
  projects       = try(yamldecode(file("configs/projects.yaml")), {})
  workspaces_raw = try(yamldecode(file("configs/workspaces.yaml")), {})
  # Exclude the 'defs' block from the workspace_variables
  workspaces = { for k, v in local.workspaces_raw : k => v if k != "defs" }

  flat_vars = flatten([
    for workspace_name, workspace_details in local.workspaces : [
      # Assuming variables might not include every workspace, hence the try function.
      for v in try(workspace_details["variables"], []) :
      merge(v, { workspace = workspace_name })
    ]
  ])
}
