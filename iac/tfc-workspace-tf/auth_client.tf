# # Configure the OAuth client for Bitbucket Cloud
# # Refer https://bitbucket.org/anchorsprint/workspace/settings/oauth-consumers/new
# resource "tfe_oauth_client" "anchorsprint_bitbucket" {
#   name             = "anchorsprint-bitbucket"
#   organization     = var.organization
#   api_url          = "https://api.bitbucket.org"
#   http_url         = "https://bitbucket.org"
#   key              = "FL7UmKKvUsrqxMWBhs"               # Your Bitbucket OAuth consumer key
#   secret           = "3DKP4DdC8k9H7eKwkb6ZBpegD6aJWzNb" # Your Bitbucket OAuth consumer secret
#   service_provider = "bitbucket_hosted"
# }
