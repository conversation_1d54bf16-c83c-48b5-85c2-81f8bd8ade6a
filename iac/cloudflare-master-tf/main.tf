terraform {
  required_providers {
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 4.0"
    }
  }
  required_version = ">= 1.7.5"
}

# Configure the Cloudflare provider
# Authentication is handled via environment variables:
# - CLOUDFLARE_API_TOKEN
# - CLOUDFLARE_ACCOUNT_ID
provider "cloudflare" {}

# Import local variables
locals {
  common_tags = {
    Environment = "production"
    ManagedBy   = "terraform"
    Project     = "company-site"
  }
}

# Example: DNS records for company site
# Uncomment and modify as needed
/*
resource "cloudflare_record" "www" {
  zone_id = var.cloudflare_zone_id
  name    = "www"
  value   = "your-website-ip-or-cname-target"
  type    = "A"  # or "CNAME" if pointing to another domain
  ttl     = 3600
  proxied = true
  tags    = local.common_tags
}
*/

# Example: Zone settings for company site
# Uncomment and modify as needed
/*
resource "cloudflare_zone_settings_override" "company_site_settings" {
  zone_id = var.cloudflare_zone_id
  settings {
    ssl                      = "full"
    always_use_https         = "on"
    automatic_https_rewrites = "on"
    browser_check            = "on"
    min_tls_version          = "1.2"
  }
}
*/

# Example: Page Rules for company site
# Uncomment and modify as needed
/*
resource "cloudflare_page_rule" "redirect_to_www" {
  zone_id  = var.cloudflare_zone_id
  target   = "example.com/*"
  priority = 1

  actions {
    forwarding_url {
      url         = "https://www.example.com/$1"
      status_code = 301
    }
  }
}
*/
