# Cloudflare Master Terraform Configuration

This directory contains the Terraform configuration for managing Cloudflare resources for the company site, including:

- DNS records
- Zone settings
- Page rules
- Security settings
- Workers (if applicable)
- Access policies (if applicable)

## Prerequisites

To use this Terraform configuration, you need:

1. A Cloudflare account with appropriate access
2. A Cloudflare API token with the necessary permissions
3. The Zone ID for your domain

## Required Variables

The following variables must be set in Terraform Cloud:

| Variable | Description | Type |
|----------|-------------|------|
| `CLOUDFLARE_API_TOKEN` | Cloudflare API token with appropriate permissions | Environment variable (sensitive) |
| `CLOUDFLARE_ACCOUNT_ID` | Cloudflare account ID | Environment variable (sensitive) |
| `cloudflare_zone_id` | Zone ID for the company domain | Terraform variable (sensitive) |

## Usage

This configuration is managed through Terraform Cloud in the `cloudflare-master` workspace.

### Adding DNS Records

To add DNS records, uncomment and modify the example in `main.tf`:

```hcl
resource "cloudflare_record" "www" {
  zone_id = var.cloudflare_zone_id
  name    = "www"
  value   = "your-website-ip-or-cname-target"
  type    = "A"  # or "CNAME" if pointing to another domain
  ttl     = 3600
  proxied = true
  tags    = local.common_tags
}
```

### Configuring Zone Settings

To configure zone settings, uncomment and modify the example in `main.tf`:

```hcl
resource "cloudflare_zone_settings_override" "company_site_settings" {
  zone_id = var.cloudflare_zone_id
  settings {
    ssl                      = "full"
    always_use_https         = "on"
    automatic_https_rewrites = "on"
    browser_check            = "on"
    min_tls_version          = "1.2"
  }
}
```

## Best Practices

1. Always use descriptive names for resources
2. Use tags to organize resources
3. Keep sensitive information in Terraform Cloud variables marked as sensitive
4. Document any manual changes made outside of Terraform

## Extending This Configuration

This configuration can be extended to manage additional Cloudflare resources as needed. Refer to the [Cloudflare Terraform Provider documentation](https://registry.terraform.io/providers/cloudflare/cloudflare/latest/docs) for more information on available resources.
