variable "cloudflare_zone_id" {
  description = "The Cloudflare Zone ID for the company domain"
  type        = string
  sensitive   = true
}

# Add more variables as needed for your specific Cloudflare configuration
# For example:
/*
variable "domain_name" {
  description = "The domain name managed by Cloudflare"
  type        = string
  default     = "example.com"
}

variable "enable_security_features" {
  description = "Whether to enable advanced security features"
  type        = bool
  default     = true
}
*/
