terraform {
  required_providers {
    aws = {
      configuration_aliases = [aws.certificate_region]
    }
  }
  required_version = ">= 1.0.0"
}

# Create ACM certificate
resource "aws_acm_certificate" "this" {
  provider                  = aws.certificate_region
  domain_name               = var.domain_name
  subject_alternative_names = var.subject_alternative_names
  validation_method         = "DNS"

  lifecycle {
    create_before_destroy = true
  }

  tags = merge(
    var.tags,
    {
      Name = "${var.domain_name}-certificate"
    }
  )
}

# Create DNS records for certificate validation
resource "aws_route53_record" "validation" {
  provider = aws.certificate_region
  for_each = {
    for dvo in aws_acm_certificate.this.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  }

  zone_id = var.route53_zone_id
  name    = each.value.name
  type    = each.value.type
  records = [each.value.record]
  ttl     = 60
}

# Validate the certificate
resource "aws_acm_certificate_validation" "this" {
  provider                = aws.certificate_region
  certificate_arn         = aws_acm_certificate.this.arn
  validation_record_fqdns = [for record in aws_route53_record.validation : record.fqdn]
}
