terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "5.43.0"
      //Create alias to be use explcitiy by different resoure
      configuration_aliases = [aws.aws_cloudfront]
    }
  }
  required_version = ">= 1.7.5"
}

locals {
  default_certs = var.use_default_domain ? ["default"] : []
  acm_certs     = var.use_default_domain ? [] : ["acm"]
  # Include both the root domain and www subdomain in the main distribution if www redirect is enabled
  domain_name = var.use_default_domain ? [] : (
    var.enable_www_redirect ? [var.domain_name, "www.${var.domain_name}"] : [var.domain_name]
  )
}

// Cert resource to query, it must query from use-east-1, hence require special provider alias
data "aws_acm_certificate" "acm_cert" {
  count    = var.use_default_domain ? 0 : 1
  domain   = var.acm_certificate_domain
  provider = aws.aws_cloudfront
  //CloudFront uses certificates from US-EAST-1 region only
  statuses = [
    "ISSUED",
  ]
}

data "aws_iam_policy_document" "s3_bucket_policy" {
  statement {
    sid = "1"

    actions = [
      "s3:GetObject",
    ]

    resources = [
      "arn:aws:s3:::${var.domain_name}/*",
    ]

    principals {
      type = "AWS"

      identifiers = [
        aws_cloudfront_origin_access_identity.origin_access_identity.iam_arn,
      ]
    }
  }
}

resource "aws_s3_bucket" "s3_bucket" {
  bucket = var.domain_name
  tags   = var.tags
}
resource "aws_s3_bucket_policy" "s3_bucket_policy" {
  bucket = aws_s3_bucket.s3_bucket.id
  policy = data.aws_iam_policy_document.s3_bucket_policy.json

}

resource "aws_s3_bucket_website_configuration" "s3_bucket" {
  bucket = aws_s3_bucket.s3_bucket.id

  index_document {
    suffix = "index.html"
  }

  error_document {
    key = "error.html"
  }

  # SEO improvement: Add routing rules to handle various path patterns
  # Using 301 (Moved Permanently) redirects to ensure search engines update their indexes
  # and transfer link equity to the canonical URL
  routing_rules = jsonencode([
    {
      # Redirect requests with trailing slash to the root path
      Condition = {
        KeyPrefixEquals = "/index.html"
      }
      Redirect = {
        ReplaceKeyWith   = ""
        Protocol         = "https"
        HostName         = var.domain_name
        HttpRedirectCode = "301" # Proper 301 redirect for SEO
      }
    },
    {
      # Handle URLs without trailing slash
      Condition = {
        KeyPrefixEquals = "index"
      }
      Redirect = {
        ReplaceKeyWith   = ""
        Protocol         = "https"
        HostName         = var.domain_name
        HttpRedirectCode = "301" # Proper 301 redirect for SEO
      }
    }
  ])
}

resource "aws_s3_bucket_versioning" "s3_bucket" {
  bucket = var.domain_name
  versioning_configuration {
    status = "Enabled"
  }
}

# WWW redirection is now handled by CloudFront Functions instead of a separate S3 bucket

resource "aws_s3_object" "object" {
  count        = var.upload_sample_file ? 1 : 0
  bucket       = aws_s3_bucket.s3_bucket.bucket
  key          = "index.html"
  source       = "${path.module}/index.html"
  content_type = "text/html"
  etag         = filemd5("${path.module}/index.html")

  // Allow new file to ovrride tihs
  lifecycle {
    ignore_changes = [etag]
  }
}

resource "aws_s3_object" "errorobject" {
  count        = var.upload_sample_file ? 1 : 0
  bucket       = aws_s3_bucket.s3_bucket.bucket
  key          = "error.html"
  source       = "${path.module}/error.html"
  content_type = "text/html"
  etag         = filemd5("${path.module}/error.html")

  // Allow new file to ovrride tihs
  lifecycle {
    ignore_changes = [etag]
  }
}

# CloudFront distribution with SEO optimizations
# - Properly handles root path ("/") requests
# - Manages redirects for clean URLs
# - Supports SPA routing patterns
resource "aws_cloudfront_distribution" "s3_distribution" {
  depends_on = [
    aws_s3_bucket.s3_bucket
  ]

  origin {
    domain_name = aws_s3_bucket.s3_bucket.bucket_regional_domain_name
    origin_id   = "s3-cloudfront"

    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.origin_access_identity.cloudfront_access_identity_path
    }
  }

  enabled             = true
  is_ipv6_enabled     = true
  default_root_object = "index.html"

  aliases = local.domain_name

  default_cache_behavior {
    allowed_methods = [
      "GET",
      "HEAD",
    ]

    cached_methods = [
      "GET",
      "HEAD",
    ]

    target_origin_id = "s3-cloudfront"

    forwarded_values {
      query_string = false

      cookies {
        forward = "none"
      }
    }

    viewer_protocol_policy = "redirect-to-https"

    # https://stackoverflow.com/questions/********/cloudfront-s3-etag-possible-for-cloudfront-to-send-updated-s3-object-before-t
    min_ttl     = var.cloudfront_min_ttl
    default_ttl = var.cloudfront_default_ttl
    max_ttl     = var.cloudfront_max_ttl

    # Add CloudFront function for www to non-www redirection if enabled
    dynamic "function_association" {
      for_each = var.enable_www_redirect ? [1] : []
      content {
        event_type   = "viewer-request"
        function_arn = aws_cloudfront_function.www_redirect[0].arn
      }
    }
  }

  price_class = var.price_class

  restrictions {
    geo_restriction {
      restriction_type = var.enable_geo_restriction ? var.cloudfront_geo_restriction_type : "none"
      locations        = var.enable_geo_restriction ? var.cloudfront_geo_restriction_locations : []
    }
  }

  dynamic "viewer_certificate" {
    for_each = local.default_certs
    content {
      cloudfront_default_certificate = true
    }
  }

  dynamic "viewer_certificate" {
    for_each = local.acm_certs
    content {
      acm_certificate_arn      = data.aws_acm_certificate.acm_cert[0].arn
      ssl_support_method       = "sni-only"
      minimum_protocol_version = var.minimum_protocol_version
    }
  }

  # Improved error handling for SEO and SPA routing
  custom_error_response {
    error_code            = 403
    response_code         = 200
    error_caching_min_ttl = 0
    response_page_path    = "/index.html"
  }

  # Handle 404 errors for SPA routing
  custom_error_response {
    error_code            = 404
    response_code         = 200
    error_caching_min_ttl = 0
    response_page_path    = "/index.html"
  }

  wait_for_deployment = false
  tags                = var.tags
}

resource "aws_cloudfront_origin_access_identity" "origin_access_identity" {
  comment = "access-identity-${var.domain_name}.s3.amazonaws.com"
}

# Create a CloudFront function to redirect www to non-www
resource "aws_cloudfront_function" "www_redirect" {
  count   = var.enable_www_redirect ? 1 : 0
  name    = "www-to-non-www-redirect-${replace(var.domain_name, ".", "-")}"
  runtime = "cloudfront-js-1.0"
  comment = "Redirect www to non-www domain"
  publish = true
  code    = <<-EOT
function handler(event) {
  var request = event.request;
  var host = request.headers.host.value;

  // Check if this is the www domain
  if (host.startsWith('www.')) {
    var response = {
      statusCode: 301,
      statusDescription: 'Moved Permanently',
      headers: {
        'location': { value: 'https://${var.domain_name}' + request.uri }
      }
    };
    return response;
  }

  return request;
}
EOT
}

# WWW redirection is now handled by CloudFront Functions instead of a separate CloudFront distribution

output "cloudfront_domain_name" {
  value = aws_cloudfront_distribution.s3_distribution.domain_name
}

output "cloudfront_dist_id" {
  value = aws_cloudfront_distribution.s3_distribution.id
}

output "s3_domain_name" {
  value = aws_s3_bucket_website_configuration.s3_bucket.website_domain
}

output "website_address" {
  value = var.domain_name
}

output "s3_bucket_arn" {
  value = aws_s3_bucket.s3_bucket.arn
}

output "s3_bucket_name" {
  value = aws_s3_bucket.s3_bucket.id
}

output "www_redirect_enabled" {
  value       = var.enable_www_redirect
  description = "Whether www redirection is enabled"
}

output "www_redirect_function_name" {
  value       = var.enable_www_redirect ? aws_cloudfront_function.www_redirect[0].name : null
  description = "Name of the CloudFront function used for www redirection"
}

output "www_redirect_function_arn" {
  value       = var.enable_www_redirect ? aws_cloudfront_function.www_redirect[0].arn : null
  description = "ARN of the CloudFront function used for www redirection"
}

///vars
variable "domain_name" {
  description = "domain name (or application name if no domain name available)"
}

variable "tags" {
  type        = map(string)
  default     = {}
  description = "tags for all the resources, if any"
}


variable "acm_certificate_domain" {
  default     = null
  description = "Domain of the ACM certificate"
}

variable "price_class" {
  default     = "PriceClass_200" // Default to southeast asia to reduce cost
  description = "CloudFront distribution price class"
}

variable "use_default_domain" {
  default     = false
  description = "Use CloudFront website address without Route53 and ACM certificate"
}

variable "upload_sample_file" {
  default     = false
  description = "Upload sample html file to s3 bucket"
}

# All values for the TTL are important when uploading static content that changes
# https://stackoverflow.com/questions/********/cloudfront-s3-etag-possible-for-cloudfront-to-send-updated-s3-object-before-t
variable "cloudfront_min_ttl" {
  default     = 0
  description = "The minimum TTL for the cloudfront cache"
}

variable "cloudfront_default_ttl" {
  default     = 86400
  description = "The default TTL for the cloudfront cache"
}

variable "cloudfront_max_ttl" {
  default     = 31536000
  description = "The maximum TTL for the cloudfront cache"
}

variable "cloudfront_geo_restriction_locations" {
  default     = ["SG", "MY", "TW"]
  description = "ISO 3166-1-alpha-2 codes to restrice or allow content"
}

variable "cloudfront_geo_restriction_type" {
  default     = "whitelist"
  description = "Geo restriction type either [none,whitelist,blacklist]"
}

variable "enable_geo_restriction" {
  default     = false
  description = "Enable geo restriction"
}

variable "enable_www_redirect" {
  default     = false
  description = "If true, enables redirection from www subdomain to the main domain using CloudFront Functions"
}

# Security best practice: Use the latest TLS protocol version
# TLSv1.2_2021 is the latest and most secure protocol version supported by CloudFront
# It includes the most secure cipher suites and disables older, less secure ones
# See: https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/secure-connections-supported-viewer-protocols-ciphers.html
variable "minimum_protocol_version" {
  default     = "TLSv1.2_2021"
  description = "The minimum TLS protocol version that CloudFront will use for HTTPS connections"
  validation {
    condition     = contains(["TLSv1", "TLSv1.1", "TLSv1.2_2018", "TLSv1.2_2019", "TLSv1.2_2021"], var.minimum_protocol_version)
    error_message = "Valid values for minimum_protocol_version are: TLSv1, TLSv1.1, TLSv1.2_2018, TLSv1.2_2019, TLSv1.2_2021."
  }
}
