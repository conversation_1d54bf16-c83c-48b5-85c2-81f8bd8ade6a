<!-- BEGIN_TF_DOCS -->

## Requirements

| Name                                                                     | Version  |
| ------------------------------------------------------------------------ | -------- |
| <a name="requirement_terraform"></a> [terraform](#requirement_terraform) | >= 1.7.5 |
| <a name="requirement_aws"></a> [aws](#requirement_aws)                   | 5.43.0   |

## Providers

| Name                                                                                          | Version |
| --------------------------------------------------------------------------------------------- | ------- |
| <a name="provider_aws"></a> [aws](#provider_aws)                                              | 5.43.0  |
| <a name="provider_aws.aws_cloudfront"></a> [aws.aws_cloudfront](#provider_aws.aws_cloudfront) | 5.43.0  |

## Modules

No modules.

## Resources

| Name                                                                                                                                                                          | Type        |
| ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------- |
| [aws_cloudfront_distribution.s3_distribution](https://registry.terraform.io/providers/hashicorp/aws/5.43.0/docs/resources/cloudfront_distribution)                            | resource    |
| [aws_cloudfront_origin_access_identity.origin_access_identity](https://registry.terraform.io/providers/hashicorp/aws/5.43.0/docs/resources/cloudfront_origin_access_identity) | resource    |
| [aws_s3_bucket.s3_bucket](https://registry.terraform.io/providers/hashicorp/aws/5.43.0/docs/resources/s3_bucket)                                                              | resource    |
| [aws_s3_bucket_policy.s3_bucket_policy](https://registry.terraform.io/providers/hashicorp/aws/5.43.0/docs/resources/s3_bucket_policy)                                         | resource    |
| [aws_s3_bucket_versioning.s3_bucket](https://registry.terraform.io/providers/hashicorp/aws/5.43.0/docs/resources/s3_bucket_versioning)                                        | resource    |
| [aws_s3_bucket_website_configuration.s3_bucket](https://registry.terraform.io/providers/hashicorp/aws/5.43.0/docs/resources/s3_bucket_website_configuration)                  | resource    |
| [aws_s3_object.errorobject](https://registry.terraform.io/providers/hashicorp/aws/5.43.0/docs/resources/s3_object)                                                            | resource    |
| [aws_s3_object.object](https://registry.terraform.io/providers/hashicorp/aws/5.43.0/docs/resources/s3_object)                                                                 | resource    |
| [aws_acm_certificate.acm_cert](https://registry.terraform.io/providers/hashicorp/aws/5.43.0/docs/data-sources/acm_certificate)                                                | data source |
| [aws_iam_policy_document.s3_bucket_policy](https://registry.terraform.io/providers/hashicorp/aws/5.43.0/docs/data-sources/iam_policy_document)                                | data source |
| [aws_cloudfront_function.www_redirect](https://registry.terraform.io/providers/hashicorp/aws/5.43.0/docs/resources/cloudfront_function)                                       | resource    |

## Inputs

| Name                                                                                                                                          | Description                                                                                   | Type          | Default                              | Required |
| --------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------- | ------------- | ------------------------------------ | :------: |
| <a name="input_acm_certificate_domain"></a> [acm_certificate_domain](#input_acm_certificate_domain)                                           | Domain of the ACM certificate                                                                 | `any`         | `null`                               |    no    |
| <a name="input_cloudfront_default_ttl"></a> [cloudfront_default_ttl](#input_cloudfront_default_ttl)                                           | The default TTL for the cloudfront cache                                                      | `number`      | `86400`                              |    no    |
| <a name="input_cloudfront_geo_restriction_locations"></a> [cloudfront_geo_restriction_locations](#input_cloudfront_geo_restriction_locations) | ISO 3166-1-alpha-2 codes to restrice or allow content                                         | `list`        | <pre>[<br> "SG",<br> "MY"<br>]</pre> |    no    |
| <a name="input_cloudfront_geo_restriction_type"></a> [cloudfront_geo_restriction_type](#input_cloudfront_geo_restriction_type)                | Geo restriction type either [none,whitelist,blacklist]                                        | `string`      | `"whitelist"`                        |    no    |
| <a name="input_cloudfront_max_ttl"></a> [cloudfront_max_ttl](#input_cloudfront_max_ttl)                                                       | The maximum TTL for the cloudfront cache                                                      | `number`      | `31536000`                           |    no    |
| <a name="input_cloudfront_min_ttl"></a> [cloudfront_min_ttl](#input_cloudfront_min_ttl)                                                       | The minimum TTL for the cloudfront cache                                                      | `number`      | `0`                                  |    no    |
| <a name="input_domain_name"></a> [domain_name](#input_domain_name)                                                                            | domain name (or application name if no domain name available)                                 | `any`         | n/a                                  |   yes    |
| <a name="input_price_class"></a> [price_class](#input_price_class)                                                                            | CloudFront distribution price class                                                           | `string`      | `"PriceClass_200"`                   |    no    |
| <a name="input_tags"></a> [tags](#input_tags)                                                                                                 | tags for all the resources, if any                                                            | `map(string)` | `{}`                                 |    no    |
| <a name="input_upload_sample_file"></a> [upload_sample_file](#input_upload_sample_file)                                                       | Upload sample html file to s3 bucket                                                          | `bool`        | `false`                              |    no    |
| <a name="input_use_default_domain"></a> [use_default_domain](#input_use_default_domain)                                                       | Use CloudFront website address without Route53 and ACM certificate                            | `bool`        | `false`                              |    no    |
| <a name="input_enable_geo_restriction"></a> [enable_geo_restriction](#input_enable_geo_restriction)                                           | Enable geo restriction                                                                        | `bool`        | `false`                              |    no    |
| <a name="input_enable_www_redirect"></a> [enable_www_redirect](#input_enable_www_redirect)                                                    | If true, enables redirection from www subdomain to the main domain using CloudFront Functions | `bool`        | `false`                              |    no    |

## Outputs

| Name                                                                                                              | Description                                              |
| ----------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------- |
| <a name="output_cloudfront_dist_id"></a> [cloudfront_dist_id](#output_cloudfront_dist_id)                         | n/a                                                      |
| <a name="output_cloudfront_domain_name"></a> [cloudfront_domain_name](#output_cloudfront_domain_name)             | n/a                                                      |
| <a name="output_s3_bucket_arn"></a> [s3_bucket_arn](#output_s3_bucket_arn)                                        | n/a                                                      |
| <a name="output_s3_bucket_name"></a> [s3_bucket_name](#output_s3_bucket_name)                                     | n/a                                                      |
| <a name="output_s3_domain_name"></a> [s3_domain_name](#output_s3_domain_name)                                     | n/a                                                      |
| <a name="output_website_address"></a> [website_address](#output_website_address)                                  | n/a                                                      |
| <a name="output_www_redirect_enabled"></a> [www_redirect_enabled](#output_www_redirect_enabled)                   | Whether www redirection is enabled                       |
| <a name="output_www_redirect_function_name"></a> [www_redirect_function_name](#output_www_redirect_function_name) | Name of the CloudFront function used for www redirection |
| <a name="output_www_redirect_function_arn"></a> [www_redirect_function_arn](#output_www_redirect_function_arn)    | ARN of the CloudFront function used for www redirection  |

<!-- END_TF_DOCS -->
