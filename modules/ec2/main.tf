resource "aws_instance" "this" {
  ami                         = data.aws_ami.amzlinux2.id
  iam_instance_profile        = aws_iam_instance_profile.this.name
  instance_type               = var.instance_type
  vpc_security_group_ids      = [aws_security_group.this.id]
  subnet_id                   = data.aws_subnet.selected.id
  associate_public_ip_address = var.associate_public_ip_address

  root_block_device {
    volume_type = var.volume_type
    volume_size = var.volume_size
  }
  tags = merge(var.tags, { Name = var.app_id })

  lifecycle {
    // Prevent instance replacement when AMI or public IP changes
    ignore_changes = [
      ami,                        // Ignore AMI updates to prevent replacement
      associate_public_ip_address // Ignore changes from external IP attachment
    ]
  }
}
