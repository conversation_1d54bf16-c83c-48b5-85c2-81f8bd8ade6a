# ALB Outputs
output "alb_id" {
  description = "The ID of the ALB"
  value       = module.alb.alb_id
}

output "alb_arn" {
  description = "The ARN of the ALB"
  value       = module.alb.alb_arn
}

output "alb_dns_name" {
  description = "The DNS name of the ALB"
  value       = module.alb.alb_dns_name
}

output "alb_zone_id" {
  description = "The canonical hosted zone ID of the ALB"
  value       = module.alb.alb_zone_id
}

output "http_listener_arn" {
  description = "The ARN of the HTTP listener"
  value       = module.alb.http_listener_arn
}

output "https_listener_arn" {
  description = "The ARN of the HTTPS listener"
  value       = module.alb.https_listener_arn
}

output "alb_security_group_id" {
  description = "The ID of the ALB security group"
  value       = module.alb.security_group_id
}

# ECS Cluster Outputs
output "cluster_id" {
  description = "The ID of the ECS cluster"
  value       = aws_ecs_cluster.this.id
}

output "cluster_arn" {
  description = "The ARN of the ECS cluster"
  value       = aws_ecs_cluster.this.arn
}

output "cluster_name" {
  description = "The name of the ECS cluster"
  value       = aws_ecs_cluster.this.name
}

# ECS Services Outputs
output "services" {
  description = "Map of service details"
  value = {
    for k, v in aws_ecs_service.services : k => {
      id           = v.id
      name         = v.name
      cluster      = v.cluster
      desired_count = v.desired_count
      task_definition = v.task_definition
    }
  }
}

# Task Definitions Outputs
output "task_definitions" {
  description = "Map of task definition ARNs"
  value = {
    for k, v in aws_ecs_task_definition.services : k => v.arn
  }
}

# Target Groups Outputs
output "target_groups" {
  description = "Map of target group ARNs"
  value = {
    for k, v in aws_lb_target_group.services : k => v.arn
  }
}

# Security Group Outputs
output "ecs_security_group_id" {
  description = "The ID of the ECS tasks security group"
  value       = aws_security_group.ecs_tasks.id
}

# IAM Role Outputs
output "task_execution_role_arn" {
  description = "The ARN of the task execution role"
  value       = aws_iam_role.execution_role.arn
}

output "task_role_arn" {
  description = "The ARN of the task role"
  value       = aws_iam_role.task_role.arn
}

# Log Group Outputs
output "log_groups" {
  description = "Map of CloudWatch log group names"
  value = {
    for k, v in aws_cloudwatch_log_group.services : k => v.name
  }
}

# Listener Rules Outputs
output "listener_rules" {
  description = "Map of listener rule ARNs"
  value = {
    for k, v in aws_lb_listener_rule.services : k => v.arn
  }
}
